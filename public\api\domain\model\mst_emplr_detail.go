package model

import (
	"time"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
)

type MstEmplrDetail struct {
	EmplrID       int    `json:"emplr_id"`
	EmplrName     string `json:"emplr_name"`
	EmplrLabel    string `json:"emplr_label"`
	RSVPtn        int    `json:"rsv_ptn"`
	EmplrKBN      int    `json:"emplr_kbn"`
	ReceSTYM      int    `json:"rece_st_ym"`
	ReceEDYM      int    `json:"rece_ed_ym"`
	MemSTYM       int    `json:"mem_st_ym"`
	MemEDYM       int    `json:"mem_ed_ym"`
	HeSTYM        int    `json:"he_st_ym"`
	HeEDYM        int    `json:"he_ed_ym"`
	FlaSTYM       int    `json:"fla_st_ym"`
	FlaEDYM       int    `json:"fla_ed_ym"`
	FullSTYM      int    `json:"full_st_ym"`
	FullEDYM      int    `json:"full_ed_ym"`
	MemUPDDATE         *time.Time `json:"mem_upd_date"`
	FlaUPDDATE         *time.Time `json:"fla_upd_date"`
	ReceUPDDATE        *time.Time `json:"rece_upd_date"`
	DeptUseFlg         int        `json:"dept_use_flg"`
	DelFlg             int        `json:"del_flg"`
	PasswordPattern    string     `json:"password_pattern"`
	PasswordChangeDays int        `json:"password_change_days"`
	LockCount          int        `json:"lock_count"`
}

func NewMstEmplrDetail(row dbmodels.MSTEmplr) MstEmplrDetail {
	return MstEmplrDetail{
		EmplrID:    row.EmplrID,
		EmplrName:  row.EmplrName,
		EmplrLabel: row.EmplrLabel,
		RSVPtn:     row.RSVPtn,
		EmplrKBN:   row.EmplrKBN,
		ReceSTYM:   row.ReceSTYM,
		ReceEDYM:   row.ReceEDYM,
		MemSTYM:    row.MemSTYM,
		MemEDYM:    row.MemEDYM,
		HeSTYM:     row.HeSTYM,
		HeEDYM:     row.HeEDYM,
		FlaSTYM:    row.FlaSTYM,
		FlaEDYM:    row.FlaEDYM,
		FullSTYM:           row.FullSTYM,
		FullEDYM:           row.FullEDYM,
		MemUPDDATE:         row.MemUPDDATE,
		FlaUPDDATE:         row.FlaUPDDATE,
		ReceUPDDATE:        row.ReceUPDDATE,
		DeptUseFlg:         row.DeptUseFlg,
		DelFlg:             row.DelFlg,
		PasswordPattern:    row.PasswordPattern,
		PasswordChangeDays: row.PasswordChangeDays,
		LockCount:          row.LockCount,
	}
}
