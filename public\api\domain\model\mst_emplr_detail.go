package model

import (
	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
)

type MstEmplrDetail struct {
	EmplrID              int       `json:"emplr_id"`
	EmplrName            string
	EmplrLabel            string
	RSV_PTN int
	EmplrName
}

func NewMstEmplrDetail(row dbmodels.MSTEmplr) MstEmplrDetail {
	return MstEmplrDetail{
		EmplrID:              row.EmplrID,
		EmplrCD:              row.EmplrCD,
		EmplrName:            row.EmplrName,
		SecondaryUseProperty: row.SecondaryUseProperty,
	}
}
