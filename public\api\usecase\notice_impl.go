package usecase

import (
	"context"
	"math"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/public"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/model"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/repository"
)

type Notice struct {
	NoticeRepo repository.NoticeRepository
}

func NewNotice(NoticeRepo repository.NoticeRepository) NoticeUseCase {
	return &Notice{
		NoticeRepo: NoticeRepo,
	}
}

func (n Notice) GetNotices(ctx context.Context, page, limit int, keyword *string) (oapi.GetApiNoticesResponseObject, error) {
	filter := model.NoticeListFilter{
		Page:    page,
		Limit:   limit,
		Keyword: keyword,
	}

	notices, total, err := n.NoticeRepo.GetNotices(ctx, filter)
	if err != nil {
		message := err.Error()
		return oapi.GetApiNotices500JSONResponse{
			Message: message,
		}, err
	}

	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	var noticeDetails []oapi.NoticeInfo
	for _, notice := range notices {
		noticeDetail := oapi.NoticeInfo{
			Id:          notice.ID,
			Title:       notice.Title,
			Comment:     notice.Comment,
			FileName:    notice.FileName,
			ReleaseDate: notice.ReleaseDate,
			RegUser:     notice.RegUser,
			RegDate:     notice.RegDate,
			UpdUser:     notice.UpdUser,
			UpdDate:     notice.UpdDate,
		}
		noticeDetails = append(noticeDetails, noticeDetail)
	}

	return oapi.GetApiNotices200JSONResponse{
		Data:       noticeDetails,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}, nil
}

func (n Notice) GetNoticeByID(ctx context.Context, id int) (oapi.GetApiNoticeByIdResponseObject, error) {
	notice, err := n.NoticeRepo.GetNoticeByID(ctx, id)
	if err != nil {
		message := err.Error()
		return oapi.GetApiNoticeById404JSONResponse{
			Message: message,
		}, err
	}

	noticeDetail := oapi.NoticeInfo{
		Id:          notice.ID,
		Title:       notice.Title,
		Comment:     notice.Comment,
		FileName:    notice.FileName,
		ReleaseDate: notice.ReleaseDate,
		RegUser:     notice.RegUser,
		RegDate:     notice.RegDate,
		UpdUser:     notice.UpdUser,
		UpdDate:     notice.UpdDate,
	}

	return oapi.GetApiNoticeById200JSONResponse(noticeDetail), nil
}
