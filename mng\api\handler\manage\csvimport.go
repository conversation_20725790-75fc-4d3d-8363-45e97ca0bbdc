package handler

import (
	"context"
	"log/slog"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
)

// ユーザー登録確認CSV作成
// (P30OST /confirm-regist-user-list-csv)
func (h *API) PostConfirmRegistUserList(ctx context.Context, request oapi.PostConfirmRegistUserListRequestObject) (oapi.PostConfirmRegistUserListResponseObject, error) {
	results, errors, err := h.csvImportUseCase.ConfirmRegistUserList(ctx, request)

	if err != nil {
		return oapi.PostConfirmRegistUserList500JSONResponse{
			Message: err.Error(),
		}, err
	}
	if len(errors) > 0 {
		slog.Error("CSVファイルのフォーマットチェックに失敗", "errors", errors)
		return oapi.PostConfirmRegistUserList400JSONResponse{
			Errors: errors,
		}, nil
	}

	return oapi.PostConfirmRegistUserList200JSONResponse{
		Results: results,
	}, nil
}

// ユーザー登録
// (POST /regist-user-list)
func (h *API) PostRegistUserList(ctx context.Context, request oapi.PostRegistUserListRequestObject) (oapi.PostRegistUserListResponseObject, error) {
	results, errors, err := h.csvImportUseCase.RegistUserList(ctx, request)

	if err != nil {
		return oapi.PostRegistUserList500JSONResponse{
			Message: err.Error(),
		}, err
	}
	if len(errors) > 0 {
		slog.Error("CSVファイルのフォーマットチェックに失敗", "errors", errors)
		return oapi.PostRegistUserList400JSONResponse{
			Errors: errors,
		}, nil
	}

	if len(errors) > 0 {
		slog.Error("作成失敗", "errors", errors)
		return oapi.PostRegistUserList400JSONResponse{
			Errors: errors,
		}, nil
	}
	return oapi.PostRegistUserList200JSONResponse{
		Results: results,
	}, nil

}

// メール送信確認
// (POST /confirm-send-mail-user-list)
func (h *API) PostConfirmSendMailUserList(ctx context.Context, request oapi.PostConfirmSendMailUserListRequestObject) (oapi.PostConfirmSendMailUserListResponseObject, error) {
	results, errors, err := h.csvImportUseCase.ConfirmSendMailUserList(ctx, request)
	if err != nil {
		return oapi.PostConfirmSendMailUserList500JSONResponse{
			Message: err.Error(),
		}, err
	}
	if len(errors) > 0 {
		slog.Error("メール送信確認に失敗", "errors", errors)
		return oapi.PostConfirmSendMailUserList400JSONResponse{
			Errors: errors,
		}, nil
	}
	return oapi.PostConfirmSendMailUserList200JSONResponse{
		Results: results,
	}, nil
}

// メール送信
// (POST /send-mail-user-list)
func (h *API) PostSendMailUserList(ctx context.Context, request oapi.PostSendMailUserListRequestObject) (oapi.PostSendMailUserListResponseObject, error) {
	result, resultErrors, err := h.csvImportUseCase.SendMailUserList(ctx, request)
	if err != nil {
		return oapi.PostSendMailUserList500JSONResponse{
			Message: err.Error(),
		}, err
	}
	if len(resultErrors) > 0 {
		slog.Error("メール送信に失敗", "errors", resultErrors)
		return oapi.PostSendMailUserList400JSONResponse{
			Errors: resultErrors,
		}, nil
	}
	return oapi.PostSendMailUserList200JSONResponse{
		Results: result,
	}, nil
}
