package handler

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/public"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/middleware"
)

// Generate JWT token for Tableau embedding
// (GET /api/tableau-jwt)
func (h *API) GetTableauJwt(ctx context.Context, request oapi.GetTableauJwtRequestObject) (oapi.GetTableauJwtResponseObject, error) {
	authHeader := ctx.Value(middleware.AuthorizationKey)
	if authHeader == nil {
		return oapi.GetTableauJwt500JSONResponse{
			Message: "Unauthorized: No authorization token provided",
		}, nil
	}

	auth0ID, err := h.auth0TokenService.ParseAuth0Token(authHeader.(string))
	if err != nil {
		return oapi.GetTableauJwt500JSONResponse{
			Message: "Unauthorized: Invalid token",
		}, err
	}

	token, err := h.tableauUseCase.GenerateTableauJWT(ctx, auth0ID)
	if err != nil {
		return oapi.GetTableauJwt500JSONResponse{
			Message: err.Error(),
		}, err
	}

	return oapi.GetTableauJwt200JSONResponse{
		Token: token,
	}, nil
}
