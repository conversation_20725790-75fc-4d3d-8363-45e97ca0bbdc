openapi: 3.0.3
info:
  title: 健助+管理API
  version: "1.0.0"

servers:
  - url: https://example.com/api
    description: プロダクション API
  - url: http://{host}:{port}
    description: 開発用
    variables:
      host:
        default: localhost
      port:
        default: '3000'
components:
  schemas:
    EmplrMaster:
      description: 組合マスタ
      type: object
      required:
        - id
        - cd
        - name
        - secondaryUseProperty
      properties:
        id:
          type: integer
          description: 組合ID
          example: 100001
        cd:
          type: string
          description: 組合CD
          example: JMD
        name:
          type: string
        secondaryUseProperty:
          type: integer
    Emplr:
      description: 健康保険組合
      type: object
      required:
        - id
        - customEmplrName
        - groupId
      properties:
        id:
          type: integer
          description: 組合ID
          maxLength: 5
          minLength: 5
          example: 100010
        cd:
          type: string
          example: JMD
        customEmplrName:
          type: string
          description: "組合名"
          example: 北海道健保
        groupId:
          type: integer
          description: "組合区分 1: 単一 2: 組合 3: 自治体"
          example: 1
    HierarchyRole:
      description: 階層情報ロール
      type: object
      required:
        - emplrId
        - hierarchyLevel
      properties:
        hierarchyRoleId:
          type: integer
          description: 階層情報ロールID
          example: 1
        emplrId:
          type: integer
          description: 組合ID
          maxLength: 5
          minLength: 5
          example: 100010
        hierarchyLevel:
          type: integer
          description: 階層レベル
          example: 1
          maximum: 4
        jigyosyoCd:
          type: string
          description: 事業所コード
          example: 23
    JigyosyoInfo:
      description: 事業所情報
      type: object
      required:
        - jigyosyoCd
        - jigyosyoName
      properties:
        jigyosyoCd:
          type: string
          description: 事業所コード
          example: 23
        jigyosyoName:
          type: string
          description: 事業所名
          example: 北海道健保事業所名
    User:
      type: object
      required:
          - name
          - email
          - hierarchyRoleId
          - isAdmin
      properties:
        id:
          type: integer
          description: ユーザーID
        name:
          type: string
          format: string
          description: ユーザー名
          example: 山田太郎
        email:
          type: string
          format: email
          example: <EMAIL>
          description: メールアドレス
        hierarchyRoleId:
          type: integer
          description: 階層情報ロールID
          example: 1
        isAdmin:
          type: boolean
          description: 組合ID
          example: true
        comment:
          type: string
          description: 備考
          example: "備考"
    UserSimple:
      type: object
      required:
          - id
          - name
          - email
          - emplrName
          - isAdmin
      properties:
        id:
          type: integer
          description: ユーザーID
          example: jmdc-yamada
        name:
          type: string
          format: string
          description: ユーザー名
          example: 山田太郎
        email:
          type: string
          format: email
          example: <EMAIL>
          description: メールアドレス
        emplrName:
          type: string
          description: 階層情報ロールID
          example: 1
        isAdmin:
          type: boolean
          description: 組合ID
          example: true
    UserDetail:
      type: object
      properties:
        id:
          type: integer
          description: ユーザーID
          example: jmdc-yamada
        name:
          type: string
          format: string
          description: ユーザー名
          example: 山田太郎
        email:
          type: string
          format: email
          example: <EMAIL>
          description: メールアドレス
        emplrCd:
          type: string
          description: 組合ID
          example: emplr-123
        emplrName:
          type: string
          description: 組合ID
          example: emplr-123
        isAdmin:
          type: boolean
          description: 権限
          example: false
        comment:
          type: string
          description: 備考
          example: "備考"
        auth0UserId:
          type: string
          description: Auth0のユーザーID
          example: auth0|123456789
        hierarchyLevel:
          type: integer
          description: 階層レベル
          example: 1
        jigyosyoCd:
          type: string
          description: 事業所コード
          example: 23
        isBlocked:
          type: boolean
          description: ブロック状態
          example: false
        isMfa:
          type: boolean
          description: MFA状態
          example: true
        lastLogin:
          type: string
          format: date-time
          description: 最終ログイン日時
          example: '2020-01-31T23:59:59+09:00'
      required:
        - id
        - name
        - email
        - emplrCd
        - emplrName
        - isAdmin
        - comment
        - hierarchyLevel
        - jigyosyoCd
        - auth0UserId
        - isBlocked
        - isMfa
        - lastLogin
    NoticeInfo:
      type: object
      required:
        - id
      properties:
        id:
          type: integer
          format: int64
          description: お知らせID
          example: 1
        releaseDate:
          type: string
          format: date-time
          description: 日付
          example: '2023-10-01T12:00:00Z'
        title:
          type: string
          description: タイトル
          example: メンテナンスタイトル
        category:
          type: integer
        comment:
          type: string
          description: 内容
          example: メンテナンスの内容
        fileName:
          type: string
          description: ファイル名
          example: sample.pdf
    MaintenanceInfo:
        type: object
        required:
          - releaseDate
          - title
          - comment
        properties:
          id:
            type: integer
            format: int64
            description: お知らせID
            example: 1
          releaseDate:
            type: string
            format: full-date
            description: 日付
            example: '2023-10-01'
          title:
            type: string
            description: タイトル
            example: メンテナンスタイトル
          comment:
            type: string
            description: 内容
            example: メンテナンスの内容

    RegistCsvResult:
        type: object
        required:
          - email
          - name
          - memo
          - emplrId
          - emplrName
          - hierarchyRoleId
          - isAdmin
          - isRegist
        properties:
          email:
            type: string
            format: email
            description: メールアドレス
            example: "<EMAIL>"
          name:
            type: string
            description: 名前
            example: "砂糖太郎"
          memo:
            type: string
            description: メモ
            example: "メモ"
          emplrId:
            type: integer
            description: 企業ID
            example: "999999"
          emplrName:
            type: string
            description: 組合名
            example: "北海道健保"
          auth0UserId:
            type: string
            description: Auth0のユーザーID
            example: "auth0|684a1eec82e45b8f001a1406"
          hierarchyRoleId:
            type: integer
            description: 階層情報ロールID
            example: "1"
          isAdmin:
            type: boolean
            description: メールアドレス
            example: false
          isRegist:
            type: boolean
            description: 登録済みかどうか
            example: false

paths:
  /master-emplr-list:
    get:
      summary: 組合マスターリストの取得
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/EmplrMaster"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
  /emplr:
    put:
      summary: 組合作成
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Emplr"
      responses:
        '201':
          description: Created
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
        '409':
          description: Conflict
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"

    patch:
      summary: 組合更新
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Emplr"
      responses:
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"

    get:
      summary: 組合取得
      parameters:
      - in: query
        name: id
        required: true
        schema:
          type: integer
          description: 組合ID
          example: 100001
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Emplr"
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"

    delete:
      summary: 組合削除
      parameters:
      - in: query
        name: id
        required: true
        schema:
          type: integer
          description: 組合ID
          example: 100001
      responses:
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
  /user:
    put:
      summary: ユーザー作成
      requestBody:
        content:
          application/json:
            schema:
              type: object
              $ref: "#/components/schemas/User"
      responses:
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"

    patch:
      summary: ユーザー更新
      requestBody:
        content:
          application/json:
            schema:
              type: object
              $ref: "#/components/schemas/User"
      responses:
        '204':
          description: No Content
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
    get:
      summary: ユーザー取得
      parameters:
      - in: query
        name: id
        required: true
        schema:
          type: integer
          description: ユーザーID
          example: 1
      responses:
        '200':
          description: "成功"
          content:
            application/json:
              schema:
                type: object
                $ref: "#/components/schemas/UserDetail"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
    delete:
      summary: ユーザー削除
      parameters:
      - in: query
        name: id
        required: true
        schema:
          type: integer
          description: ユーザーID
          example: 1
      responses:
        '204':
          description: No Content
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"

  /maintenance-info:
    put:
      summary: メンテナンス情報作成
      requestBody:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MaintenanceInfo"
      responses:
        '204':
          description: No Content
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"

    get:
      summary: メンテナンス情報取得
      parameters:
      - in: query
        name: id
        required: true
        schema:
          type: integer
          format: int64
          description: メンテナンスID
          example: 1
      responses:
        '200':
          description: メンテナンス情報を取得
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MaintenanceInfo"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"

    patch:
      summary: メンテナンス情報更新
      requestBody:
        content:
          application/json:
            schema:
                $ref: "#/components/schemas/MaintenanceInfo"
      responses:
        '204':
          description: No Content
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
    delete:
      summary: メンテナンス情報削除
      parameters:
      - in: query
        name: id
        required: true
        schema:
          type: integer
          format: int64
          description: メンテナンスID
          example: 1
      responses:
        '204':
          description: No Content
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"

  /service-status:
    get:
      summary: サーバーステータス取得
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - status
                properties:
                  status:
                    type: integer
                    description: サーバーステータス
                    example: 0
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
    patch:
      summary: サーバーステータス更新
      parameters:
      - in: query
        name: status
        required: true
        schema:
          type: integer
          format: int64
          description: サーバーステータス
          example: 0

      responses:
        '204':
          description: No Content
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
  /user-list:
      get:
        summary: ユーザー一覧取得
        responses:
          '200':
            description: ユーザー一覧を取得する
            content:
              application/json:
                schema:
                  type: array
                  items:
                    $ref: "#/components/schemas/UserSimple"
          '500':
            description: Internal Server Error
            content:
              application/json:
                schema:
                  $ref: "./schemas/common/ErrorResponse.yml"
  /maintenance-info-list:
    get:
      summary: メンテナンス情報一覧取得
      responses:
        '200':
          description: メンテナンス情報一覧を取得する
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/MaintenanceInfo"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"

  /confirm-regist-user-list:
    post:
      summary: ユーザー登録確認CSV作成
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - file
              properties:
                file:
                  type: string
                  format: binary
      responses:
         '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - results
                properties:
                  results:
                    type: array
                    items:
                      $ref: "#/components/schemas/RegistCsvResult"
         '400':
          description: 異常
          content:
            application/json:
              schema:
                type: object
                required:
                  - errors
                properties:
                  errors:
                    type: array
                    items:
                      type: string
         '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"

  /regist-user-list:
    post:
      summary: ユーザー登録
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - file
              properties:
                file:
                  type: string
                  format: binary
      responses:
         '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - results
                properties:
                  results:
                    type: array
                    items:
                      $ref: "#/components/schemas/RegistCsvResult"
         '400':
          description: 異常
          content:
            application/json:
              schema:
                type: object
                required:
                  - errors
                properties:
                  errors:
                    type: array
                    items:
                      type: string
         '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"

  /confirm-send-mail-user-list:
    post:
      summary: メール送信確認
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - file
              properties:
                file:
                  type: string
                  format: binary
      responses:
         '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - results
                properties:
                  results:
                    type: array
                    items:
                      $ref: "#/components/schemas/RegistCsvResult"
         '400':
          description: 異常
          content:
            application/json:
              schema:
                type: object
                required:
                  - errors
                properties:
                  errors:
                    type: array
                    items:
                      type: string
         '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"

  /send-mail-user-list:
    post:
      summary: メール送信
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - file
              properties:
                file:
                  type: string
                  format: binary
      responses:
         '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - results
                properties:
                  results:
                    type: array
                    items:
                      $ref: "#/components/schemas/RegistCsvResult"
         '400':
          description: 異常
          content:
            application/json:
              schema:
                type: object
                required:
                  - errors
                properties:
                  errors:
                    type: array
                    items:
                      type: string
         '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
  /notice-info-list:
      get:
        summary: お知らせ一覧取得
        responses:
          '200':
            description: お知らせ一覧を取得する
            content:
              application/json:
                schema:
                  type: array
                  items:
                    $ref: "#/components/schemas/NoticeInfo"
          '500':
            description: Internal Server Error
            content:
              application/json:
                schema:
                  $ref: "./schemas/common/ErrorResponse.yml"
  /notice-info:
    get:
      summary: お知らせ取得
      parameters:
      - in: query
        name: id
        required: true
        schema:
          type: integer
          format: int64
          description: お知らせID
          example: 1
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/NoticeInfo"
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
    post:
      summary: お知らせ作成
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - releaseDate
                - title
                - comment
              properties:
                id:
                  type: integer
                  description: お知らせID（新規作成の場合のみ）
                  example: 1
                releaseDate:
                  type: string
                  format: date
                  description: 日付
                  example: "2024-10-01"
                title:
                  type: string
                  description: タイトル
                  example: アップデート情報
                comment:
                  type: string
                  description: コメント
                  example: メンテナンスは明日です
                file:
                  type: string
                  format: binary
      responses:
        '204':
          description: No Content
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
    delete:
      summary: お知らせ削除
      parameters:
      - in: query
        name: id
        required: true
        schema:
          type: integer
          format: int64
          description: お知らせID
          example: 1
      responses:
        '204':
          description: No Content
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
  /emplr-list:
    get:
      summary: 組合一覧取得
      responses:
        '200':
          description: 組合一覧を取得する
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Emplr"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
  /hierarchy-role:
    get:
      summary: 組合階層取得
      parameters:
      - in: query
        name: id
        required: true
        schema:
          type: integer
          description: 階層情報ロールID
          example: 1
      responses:
        '200':
          description: 組合階層を取得する
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HierarchyRole"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
    put:
      summary: 組合階層追加
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/HierarchyRole"
      responses:
        '204':
          description: No Content
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
    delete:
      summary: 組合階層削除
      parameters:
      - in: query
        name: id
        required: true
        schema:
          type: integer
          description: 階層情報ロールID
          example: 1
      responses:
        '204':
          description: No Content
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
  /hierarchy-role-all-list:
      get:
        summary: 階層情報ロールの一覧取得
        responses:
          '200':
            description: 組合階層を全て取得する
            content:
              application/json:
                schema:
                  type: array
                  items:
                    $ref: "#/components/schemas/HierarchyRole"
          '500':
            description: Internal Server Error
            content:
              application/json:
                schema:
                  $ref: "./schemas/common/ErrorResponse.yml"
  /hierarchy-role-list:
    get:
      summary: 階層情報ロールの一覧取得
      parameters:
      - in: query
        name: emplrId
        required: true
        schema:
          type: integer
          description: 組合ID
          example: 100001
      responses:
        '200':
          description: 組合階層を取得する
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/HierarchyRole"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
  /jigyosyo-cd-list:
    get:
      summary: 事業所CDリストの取得
      parameters:
      - in: query
        name: id
        required: true
        schema:
          type: integer
          description: 組合ID
          example: 100001
      responses:
        '200':
          description: 事業所CDリストを取得する
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/JigyosyoInfo"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
