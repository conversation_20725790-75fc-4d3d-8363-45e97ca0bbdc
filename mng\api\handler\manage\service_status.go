package handler

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
)

// サーバーステータス取得
// (GET /service-status)
func (h *API) GetServiceStatus(ctx context.Context, request oapi.GetServiceStatusRequestObject) (oapi.GetServiceStatusResponseObject, error) {
	result, err := h.serviceStatusUseCase.GetServerStatus(ctx)
	if err != nil {
		return oapi.GetServiceStatus500JSONResponse{
			Message: err.Error(),
		}, err
	}

	return result, nil
}

// サーバーステータス更新
// (PATCH /service-status)
func (h *API) PatchServiceStatus(ctx context.Context, request oapi.PatchServiceStatusRequestObject) (oapi.PatchServiceStatusResponseObject, error) {
	err := h.serviceStatusUseCase.PatchServerStatus(ctx, request)
	if err != nil {
		return oapi.PatchServiceStatus500JSONResponse{
			Message: err.Error(),
		}, err
	}

	return oapi.PatchServiceStatus204Response{}, nil
}
