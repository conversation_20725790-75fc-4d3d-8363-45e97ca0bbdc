BRANCH = $(shell git symbolic-ref --short HEAD)
.DEFAULT_GOAL = help

.PHONY: help
help: ## help を表示する
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

.PHONY: run-db
run-db: ## DBを起動する
	docker compose -f ../docker/compose.yml up -d --wait

.PHONY: run
run: ## API を起動する
	docker compose -f ../docker/compose.public.yml up -d --build

.PHONY: run-local
run-local: ## APIをローカルで起動する
	./run.sh

.PHONY: down
down: ## コンテナを消去する
	docker compose -f ../docker/compose.public.yml down

# git hooks manager
.PHONY: lefthook-install
lefthook-install: ## lefthook をインストールする
	go run github.com/evilmartians/lefthook@latest install

.PHONY: lefthook-uninstall
lefthook-uninstall: ## lefthook をアンインストールする
	go run github.com/evilmartians/lefthook@latest uninstall

.PHONY: lint
lint: ## golangci-lintを実行する
	go run github.com/golangci/golangci-lint/v2/cmd/golangci-lint@v2.1.6 run -v

.PHONY: local-lint
local-lint: ## local環境でgolangci-lintを実行する
	golangci-lint run -v

.PHONY: postgres-init
postgres-init: ## postgresのinitializeを実行する
	make postgres-create-schema && \
	make postgres-apply-ddl && \
	make postgres-copy-data

.PHONY: postgres-test-init
postgres-test-init: ## postgres_testのinitializeを実行する
	docker compose -f ./build/docker/compose.test_db.yml up -d --wait && \
	docker exec j-notice-postgres_test /bin/sh -c "/opt/postgres/bin/vsql -h localhost -U dbadmin -w postgres -f /etc/query/schema.sql" && \
	docker exec j-notice-postgres_test /bin/sh /etc/script/apply.sh && \
	docker compose -f ./build/docker/compose.test_db.yml stop db_test

.PHONY: postgres-create-schema
postgres-create-schema: ## postgresにschemaを作成する
	docker exec postgres_local /bin/sh -c "psql -U postgres -w postgres -f /etc/query/schema.sql"

.PHONY: postgres-apply-ddl
postgres-apply-ddl: ## postgresへDDLを適用する
	docker exec postgres_local /bin/sh -c "sed -i 's/\r$$//' /etc/script/apply.sh && /etc/script/apply.sh"

.PHONY: postgres-copy-data
postgres-copy-data: ## postgresへレコードをコピーする
	docker exec postgres_local /bin/sh -c "sed -i 's/\r$$//' /etc/script/copy.sh && /etc/script/copy.sh"

.PHONY: openapi-gen
openapi-gen: ## OpenAPI から Handler などのコードベースを生成する
	cd ../pkg/oapi/public && go run github.com/oapi-codegen/oapi-codegen/v2/cmd/oapi-codegen@latest -config openapi_config.yml ../../../public/openapi/openapi.yml

.PHONY: wire
wire: ## DI したコードを生成する
	go run github.com/google/wire/cmd/wire@latest gen ./api/injector/...

.PHONY: run-test
run-test: ## ユニットテストを実行する
	docker compose -f ./build/docker/compose.test_api.yml -f ./build/docker/compose.test_db.yml up --build --abort-on-container-exit

.PHONY: run-test-db
run-test-db: ## ユニットテストDBを起動する
	docker compose -f ./build/docker/compose.test_db.yml up --build

.PHONY: postgres-test-access
postgres-test-access: ## テストpostgresコンテナにアクセスする
	docker compose -f ./build/docker/compose.test_db.yml exec db_test bash

.PHONY: ginkgo-bootstrap
ginkgo-bootstrap: ## 指定したパッケージに TestSuite を生成する
	cd ${dir} && go run github.com/onsi/ginkgo/v2/ginkgo@latest bootstrap

.PHONY: ginkgo-generate
ginkgo-generate: ## 指定したファイルの test を生成する
	cd ${dir} && go run github.com/onsi/ginkgo/v2/ginkgo@latest generate ${file}

.PHONY: mock
mock: ## テスト用の mock を生成する
	cd ./pkg/mock && go run github.com/vektra/mockery/v2@v2.47.0

.PHONY: fmt
fmt: ## postgresのinitializeを実行する
	go fmt ./...

.PHONY: openapi-doc
openapi-doc: ##
	cd ./openapi && yarn && yarn build-doc

.PHONY: swagger-ui
swagger-ui: ## Swagger UIを起動する
	docker compose -f ../docker/swagger-compose.public.yml up -d

.PHONY: swagger-ui-down
swagger-ui-down: ## Swagger UIを停止する
	docker compose -f ../docker/swagger-compose.public.yml down

.PHONY: dev
dev: postgres-init swagger-ui run-local ## 開発環境をセットアップする