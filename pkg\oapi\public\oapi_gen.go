// Package oapi provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.5.0 DO NOT EDIT.
package oapi

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/oapi-codegen/runtime"
	strictnethttp "github.com/oapi-codegen/runtime/strictmiddleware/nethttp"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

const (
	BearerAuthScopes = "bearerAuth.Scopes"
)

// EmplrDetail defines model for EmplrDetail.
type EmplrDetail struct {
	// CustomEmplrName 組合名
	CustomEmplrName string `json:"customEmplrName"`

	// EmplrCd 組合コード
	EmplrCd string `json:"emplrCd"`

	// EmplrId 組合ID
	EmplrId int `json:"emplrId"`
}

// InternalServerError defines model for InternalServerError.
type InternalServerError struct {
	// Message Error message
	Message string `json:"message"`
}

// MaintenanceInfo defines model for MaintenanceInfo.
type MaintenanceInfo struct {
	// Comment コメント
	Comment string `json:"comment"`

	// Id メンテナンス情報ID
	Id int `json:"id"`

	// RegDate 登録日時
	RegDate time.Time `json:"reg_date"`

	// RegUser 登録ユーザー
	RegUser string `json:"reg_user"`

	// ReleaseDate 公開日時
	ReleaseDate time.Time `json:"release_date"`

	// Title タイトル
	Title string `json:"title"`

	// UpdDate 更新日時
	UpdDate time.Time `json:"upd_date"`

	// UpdUser 更新ユーザー
	UpdUser string `json:"upd_user"`
}

// MaintenanceListResponse defines model for MaintenanceListResponse.
type MaintenanceListResponse struct {
	// Limit 1ページあたりの件数
	Limit           int               `json:"limit"`
	MaintenanceInfo []MaintenanceInfo `json:"maintenance_info"`

	// Page 現在のページ
	Page int `json:"page"`

	// Total 総件数
	Total int `json:"total"`

	// TotalPages 総ページ数
	TotalPages int `json:"total_pages"`
}

// MstEmplrDetail defines model for MstEmplrDetail.
type MstEmplrDetail struct {
	// DelFlg 削除フラグ
	DelFlg int `json:"delFlg"`

	// DeptUseFlg 部署使用フラグ
	DeptUseFlg int `json:"deptUseFlg"`

	// EmplrId 組合ID
	EmplrId int `json:"emplrId"`

	// EmplrKbn 組合区分
	EmplrKbn int `json:"emplrKbn"`

	// EmplrLabel 組合ラベル
	EmplrLabel string `json:"emplrLabel"`

	// EmplrName 組合名
	EmplrName string `json:"emplrName"`

	// FlaEdYm FLA終了年月
	FlaEdYm int `json:"flaEdYm"`

	// FlaStYm FLA開始年月
	FlaStYm int `json:"flaStYm"`

	// FlaUpdDate FLA更新日時
	FlaUpdDate *time.Time `json:"flaUpdDate,omitempty"`

	// FullEdYm FULL終了年月
	FullEdYm int `json:"fullEdYm"`

	// FullStYm FULL開始年月
	FullStYm int `json:"fullStYm"`

	// HeEdYm 健診終了年月
	HeEdYm int `json:"heEdYm"`

	// HeStYm 健診開始年月
	HeStYm int `json:"heStYm"`

	// LockCount ロックカウント
	LockCount int `json:"lockCount"`

	// MemEdYm メンバー終了年月
	MemEdYm int `json:"memEdYm"`

	// MemStYm メンバー開始年月
	MemStYm int `json:"memStYm"`

	// MemUpdDate メンバー更新日時
	MemUpdDate *time.Time `json:"memUpdDate,omitempty"`

	// PasswordChangeDays パスワード変更日数
	PasswordChangeDays int `json:"passwordChangeDays"`

	// PasswordPattern パスワードパターン
	PasswordPattern string `json:"passwordPattern"`

	// ReceEdYm レセプト終了年月
	ReceEdYm int `json:"receEdYm"`

	// ReceStYm レセプト開始年月
	ReceStYm int `json:"receStYm"`

	// ReceUpdDate レセプト更新日時
	ReceUpdDate *time.Time `json:"receUpdDate,omitempty"`

	// RsvPtn RSVパターン
	RsvPtn int `json:"rsvPtn"`
}

// NoticeInfo defines model for NoticeInfo.
type NoticeInfo struct {
	// Comment コメント
	Comment string `json:"comment"`

	// FileName ファイル名
	FileName string `json:"file_name"`

	// Id お知らせID
	Id int `json:"id"`

	// RegDate 登録日時
	RegDate time.Time `json:"reg_date"`

	// RegUser 登録ユーザー
	RegUser string `json:"reg_user"`

	// ReleaseDate 公開日時
	ReleaseDate time.Time `json:"release_date"`

	// Title タイトル
	Title string `json:"title"`

	// UpdDate 更新日時
	UpdDate time.Time `json:"upd_date"`

	// UpdUser 更新ユーザー
	UpdUser string `json:"upd_user"`
}

// NoticeListResponse defines model for NoticeListResponse.
type NoticeListResponse struct {
	Data []NoticeInfo `json:"data"`

	// Limit 1ページあたりの件数
	Limit int `json:"limit"`

	// Page 現在のページ
	Page int `json:"page"`

	// Total 総件数
	Total int `json:"total"`

	// TotalPages 総ページ数
	TotalPages int `json:"total_pages"`
}

// UserDetail defines model for UserDetail.
type UserDetail struct {
	// Auth0UserId Auth0のユーザーID
	Auth0UserId string `json:"auth0UserId"`

	// Comment 備考
	Comment string `json:"comment"`

	// Email メールアドレス
	Email openapi_types.Email `json:"email"`

	// EmplrCd 組合コード
	EmplrCd string `json:"emplrCd"`

	// EmplrId 組合ID
	EmplrId *int `json:"emplrId,omitempty"`

	// EmplrName 組合名
	EmplrName string `json:"emplrName"`

	// HierarchyLevel 階層レベル
	HierarchyLevel int `json:"hierarchyLevel"`

	// Id ユーザーID
	Id int `json:"id"`

	// IsAdmin 権限
	IsAdmin bool `json:"isAdmin"`

	// IsBlocked ブロック状態
	IsBlocked bool `json:"isBlocked"`

	// IsMfa MFA状態
	IsMfa bool `json:"isMfa"`

	// JigyosyoCd 事業所コード
	JigyosyoCd string `json:"jigyosyoCd"`

	// LastLogin 最終ログイン日時
	LastLogin time.Time `json:"lastLogin"`

	// Name ユーザー名
	Name string `json:"name"`
}

// PostApiEmplrByIdJSONBody defines parameters for PostApiEmplrById.
type PostApiEmplrByIdJSONBody struct {
	// EmployId 組合ID
	EmployId int `json:"employId"`
}

// GetApiEmplrsParams defines parameters for GetApiEmplrs.
type GetApiEmplrsParams struct {
	// Keyword Keyword to search for employer code or employer name
	Keyword *string `form:"keyword,omitempty" json:"keyword,omitempty"`
}

// GetApiMaintenanceInfoParams defines parameters for GetApiMaintenanceInfo.
type GetApiMaintenanceInfoParams struct {
	// Page ページ番号
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit 1ページあたりの件数
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`

	// Keyword 検索キーワード（タイトル、内容で検索）
	Keyword *string `form:"keyword,omitempty" json:"keyword,omitempty"`
}

// GetApiNoticesParams defines parameters for GetApiNotices.
type GetApiNoticesParams struct {
	// Page ページ番号
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit 1ページあたりの件数
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`

	// Keyword 検索キーワード（タイトル、内容で検索）
	Keyword *string `form:"keyword,omitempty" json:"keyword,omitempty"`
}

// PostTableauDownloadCsvJSONBody defines parameters for PostTableauDownloadCsv.
type PostTableauDownloadCsvJSONBody struct {
	// Filters Optional filters to apply to the view
	Filters *map[string]interface{} `json:"filters,omitempty"`

	// ViewId Tableau view name/ID
	ViewId string `json:"viewId"`
}

// PostTableauDownloadPdfJSONBody defines parameters for PostTableauDownloadPdf.
type PostTableauDownloadPdfJSONBody struct {
	// Filters Optional filters to apply to the view
	Filters *map[string]interface{} `json:"filters,omitempty"`

	// ViewId Tableau view name/ID
	ViewId string `json:"viewId"`
}

// PostUniqueJmdcBrdgIdsJSONBody defines parameters for PostUniqueJmdcBrdgIds.
type PostUniqueJmdcBrdgIdsJSONBody struct {
	// Age 年齢
	Age []int `json:"age,omitempty"`

	// AgeGroupsIncrements10 年齢グループ（10歳刻み）
	AgeGroupsIncrements10 []string `json:"age_groups_increments_10,omitempty"`

	// AgeGroupsIncrements5 年齢グループ（5歳刻み）
	AgeGroupsIncrements5 []string `json:"age_groups_increments_5,omitempty"`

	// AggregationCd 集計コード
	AggregationCd []int `json:"aggregation_cd,omitempty"`

	// EmplrId 組合ID
	EmplrId []int `json:"emplr_id,omitempty"`

	// FilterCd フィルターコード
	FilterCd []string `json:"filter_cd,omitempty"`

	// FilterName フィルター名
	FilterName []string `json:"filter_name,omitempty"`

	// FilterType フィルタータイプ
	FilterType []int `json:"filter_type,omitempty"`

	// FiscalYear 会計年度
	FiscalYear []int `json:"fiscal_year,omitempty"`

	// GenderFamilyKbn 性別家族区分
	GenderFamilyKbn []string `json:"gender_family_kbn,omitempty"`

	// JigyosyoCd 事業所コード
	JigyosyoCd []string `json:"jigyosyo_cd,omitempty"`
}

// GetApiLevel2UsersParams defines parameters for GetApiLevel2Users.
type GetApiLevel2UsersParams struct {
	// Keyword Keyword to search for employer code or employer name
	Keyword *string `form:"keyword,omitempty" json:"keyword,omitempty"`
}

// PostApiEmplrByIdJSONRequestBody defines body for PostApiEmplrById for application/json ContentType.
type PostApiEmplrByIdJSONRequestBody PostApiEmplrByIdJSONBody

// PostTableauDownloadCsvJSONRequestBody defines body for PostTableauDownloadCsv for application/json ContentType.
type PostTableauDownloadCsvJSONRequestBody PostTableauDownloadCsvJSONBody

// PostTableauDownloadPdfJSONRequestBody defines body for PostTableauDownloadPdf for application/json ContentType.
type PostTableauDownloadPdfJSONRequestBody PostTableauDownloadPdfJSONBody

// PostUniqueJmdcBrdgIdsJSONRequestBody defines body for PostUniqueJmdcBrdgIds for application/json ContentType.
type PostUniqueJmdcBrdgIdsJSONRequestBody PostUniqueJmdcBrdgIdsJSONBody

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// 組合情報取得（ID指定）
	// (POST /emplr-by-id)
	PostApiEmplrById(w http.ResponseWriter, r *http.Request)
	// 組合一覧取得
	// (GET /emplrs)
	GetApiEmplrs(w http.ResponseWriter, r *http.Request, params GetApiEmplrsParams)
	// メンテナンス情報一覧取得（ページング付き）
	// (GET /maintenance-info)
	GetApiMaintenanceInfo(w http.ResponseWriter, r *http.Request, params GetApiMaintenanceInfoParams)
	// メンテナンス情報詳細取得
	// (GET /maintenance-info/{id})
	GetApiMaintenanceInfoById(w http.ResponseWriter, r *http.Request, id int)
	// お知らせ詳細取得
	// (GET /notice/{id})
	GetApiNoticeById(w http.ResponseWriter, r *http.Request, id int)
	// お知らせ一覧取得（ページング付き）
	// (GET /notices)
	GetApiNotices(w http.ResponseWriter, r *http.Request, params GetApiNoticesParams)
	// Generate JWT token for Tableau embedding
	// (GET /tableau-jwt)
	GetTableauJwt(w http.ResponseWriter, r *http.Request)
	// Download CSV from Tableau view
	// (POST /tableau/download/csv)
	PostTableauDownloadCsv(w http.ResponseWriter, r *http.Request)
	// Download PDF from Tableau view
	// (POST /tableau/download/pdf)
	PostTableauDownloadPdf(w http.ResponseWriter, r *http.Request)
	// 重複なしJMDC_BRDG_IDリストの取得
	// (POST /unique-jmdc-brdg-ids)
	PostUniqueJmdcBrdgIds(w http.ResponseWriter, r *http.Request)
	// Auth0トークンによるユーザー情報取得
	// (GET /user-info)
	GetApiUserInfo(w http.ResponseWriter, r *http.Request)
	// Level 2 ユーザー一覧取得
	// (GET /users/level2)
	GetApiLevel2Users(w http.ResponseWriter, r *http.Request, params GetApiLevel2UsersParams)
}

// Unimplemented server implementation that returns http.StatusNotImplemented for each endpoint.

type Unimplemented struct{}

// 組合情報取得（ID指定）
// (POST /emplr-by-id)
func (_ Unimplemented) PostApiEmplrById(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// 組合一覧取得
// (GET /emplrs)
func (_ Unimplemented) GetApiEmplrs(w http.ResponseWriter, r *http.Request, params GetApiEmplrsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// メンテナンス情報一覧取得（ページング付き）
// (GET /maintenance-info)
func (_ Unimplemented) GetApiMaintenanceInfo(w http.ResponseWriter, r *http.Request, params GetApiMaintenanceInfoParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// メンテナンス情報詳細取得
// (GET /maintenance-info/{id})
func (_ Unimplemented) GetApiMaintenanceInfoById(w http.ResponseWriter, r *http.Request, id int) {
	w.WriteHeader(http.StatusNotImplemented)
}

// お知らせ詳細取得
// (GET /notice/{id})
func (_ Unimplemented) GetApiNoticeById(w http.ResponseWriter, r *http.Request, id int) {
	w.WriteHeader(http.StatusNotImplemented)
}

// お知らせ一覧取得（ページング付き）
// (GET /notices)
func (_ Unimplemented) GetApiNotices(w http.ResponseWriter, r *http.Request, params GetApiNoticesParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Generate JWT token for Tableau embedding
// (GET /tableau-jwt)
func (_ Unimplemented) GetTableauJwt(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Download CSV from Tableau view
// (POST /tableau/download/csv)
func (_ Unimplemented) PostTableauDownloadCsv(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Download PDF from Tableau view
// (POST /tableau/download/pdf)
func (_ Unimplemented) PostTableauDownloadPdf(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// 重複なしJMDC_BRDG_IDリストの取得
// (POST /unique-jmdc-brdg-ids)
func (_ Unimplemented) PostUniqueJmdcBrdgIds(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Auth0トークンによるユーザー情報取得
// (GET /user-info)
func (_ Unimplemented) GetApiUserInfo(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Level 2 ユーザー一覧取得
// (GET /users/level2)
func (_ Unimplemented) GetApiLevel2Users(w http.ResponseWriter, r *http.Request, params GetApiLevel2UsersParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandlerFunc   func(w http.ResponseWriter, r *http.Request, err error)
}

type MiddlewareFunc func(http.Handler) http.Handler

// PostApiEmplrById operation middleware
func (siw *ServerInterfaceWrapper) PostApiEmplrById(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PostApiEmplrById(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetApiEmplrs operation middleware
func (siw *ServerInterfaceWrapper) GetApiEmplrs(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetApiEmplrsParams

	// ------------- Optional query parameter "keyword" -------------

	err = runtime.BindQueryParameter("form", true, false, "keyword", r.URL.Query(), &params.Keyword)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "keyword", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetApiEmplrs(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetApiMaintenanceInfo operation middleware
func (siw *ServerInterfaceWrapper) GetApiMaintenanceInfo(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params GetApiMaintenanceInfoParams

	// ------------- Optional query parameter "page" -------------

	err = runtime.BindQueryParameter("form", true, false, "page", r.URL.Query(), &params.Page)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "page", Err: err})
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", r.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "limit", Err: err})
		return
	}

	// ------------- Optional query parameter "keyword" -------------

	err = runtime.BindQueryParameter("form", true, false, "keyword", r.URL.Query(), &params.Keyword)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "keyword", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetApiMaintenanceInfo(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetApiMaintenanceInfoById operation middleware
func (siw *ServerInterfaceWrapper) GetApiMaintenanceInfoById(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "id" -------------
	var id int

	err = runtime.BindStyledParameterWithOptions("simple", "id", chi.URLParam(r, "id"), &id, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetApiMaintenanceInfoById(w, r, id)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetApiNoticeById operation middleware
func (siw *ServerInterfaceWrapper) GetApiNoticeById(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "id" -------------
	var id int

	err = runtime.BindStyledParameterWithOptions("simple", "id", chi.URLParam(r, "id"), &id, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetApiNoticeById(w, r, id)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetApiNotices operation middleware
func (siw *ServerInterfaceWrapper) GetApiNotices(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetApiNoticesParams

	// ------------- Optional query parameter "page" -------------

	err = runtime.BindQueryParameter("form", true, false, "page", r.URL.Query(), &params.Page)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "page", Err: err})
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", r.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "limit", Err: err})
		return
	}

	// ------------- Optional query parameter "keyword" -------------

	err = runtime.BindQueryParameter("form", true, false, "keyword", r.URL.Query(), &params.Keyword)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "keyword", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetApiNotices(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetTableauJwt operation middleware
func (siw *ServerInterfaceWrapper) GetTableauJwt(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetTableauJwt(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PostTableauDownloadCsv operation middleware
func (siw *ServerInterfaceWrapper) PostTableauDownloadCsv(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PostTableauDownloadCsv(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PostTableauDownloadPdf operation middleware
func (siw *ServerInterfaceWrapper) PostTableauDownloadPdf(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PostTableauDownloadPdf(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PostUniqueJmdcBrdgIds operation middleware
func (siw *ServerInterfaceWrapper) PostUniqueJmdcBrdgIds(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PostUniqueJmdcBrdgIds(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetApiUserInfo operation middleware
func (siw *ServerInterfaceWrapper) GetApiUserInfo(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetApiUserInfo(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetApiLevel2Users operation middleware
func (siw *ServerInterfaceWrapper) GetApiLevel2Users(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetApiLevel2UsersParams

	// ------------- Optional query parameter "keyword" -------------

	err = runtime.BindQueryParameter("form", true, false, "keyword", r.URL.Query(), &params.Keyword)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "keyword", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetApiLevel2Users(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

type UnescapedCookieParamError struct {
	ParamName string
	Err       error
}

func (e *UnescapedCookieParamError) Error() string {
	return fmt.Sprintf("error unescaping cookie parameter '%s'", e.ParamName)
}

func (e *UnescapedCookieParamError) Unwrap() error {
	return e.Err
}

type UnmarshalingParamError struct {
	ParamName string
	Err       error
}

func (e *UnmarshalingParamError) Error() string {
	return fmt.Sprintf("Error unmarshaling parameter %s as JSON: %s", e.ParamName, e.Err.Error())
}

func (e *UnmarshalingParamError) Unwrap() error {
	return e.Err
}

type RequiredParamError struct {
	ParamName string
}

func (e *RequiredParamError) Error() string {
	return fmt.Sprintf("Query argument %s is required, but not found", e.ParamName)
}

type RequiredHeaderError struct {
	ParamName string
	Err       error
}

func (e *RequiredHeaderError) Error() string {
	return fmt.Sprintf("Header parameter %s is required, but not found", e.ParamName)
}

func (e *RequiredHeaderError) Unwrap() error {
	return e.Err
}

type InvalidParamFormatError struct {
	ParamName string
	Err       error
}

func (e *InvalidParamFormatError) Error() string {
	return fmt.Sprintf("Invalid format for parameter %s: %s", e.ParamName, e.Err.Error())
}

func (e *InvalidParamFormatError) Unwrap() error {
	return e.Err
}

type TooManyValuesForParamError struct {
	ParamName string
	Count     int
}

func (e *TooManyValuesForParamError) Error() string {
	return fmt.Sprintf("Expected one value for %s, got %d", e.ParamName, e.Count)
}

// Handler creates http.Handler with routing matching OpenAPI spec.
func Handler(si ServerInterface) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{})
}

type ChiServerOptions struct {
	BaseURL          string
	BaseRouter       chi.Router
	Middlewares      []MiddlewareFunc
	ErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

// HandlerFromMux creates http.Handler with routing matching OpenAPI spec based on the provided mux.
func HandlerFromMux(si ServerInterface, r chi.Router) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseRouter: r,
	})
}

func HandlerFromMuxWithBaseURL(si ServerInterface, r chi.Router, baseURL string) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseURL:    baseURL,
		BaseRouter: r,
	})
}

// HandlerWithOptions creates http.Handler with additional options
func HandlerWithOptions(si ServerInterface, options ChiServerOptions) http.Handler {
	r := options.BaseRouter

	if r == nil {
		r = chi.NewRouter()
	}
	if options.ErrorHandlerFunc == nil {
		options.ErrorHandlerFunc = func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		}
	}
	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandlerFunc:   options.ErrorHandlerFunc,
	}

	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/emplr-by-id", wrapper.PostApiEmplrById)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/emplrs", wrapper.GetApiEmplrs)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/maintenance-info", wrapper.GetApiMaintenanceInfo)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/maintenance-info/{id}", wrapper.GetApiMaintenanceInfoById)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/notice/{id}", wrapper.GetApiNoticeById)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/notices", wrapper.GetApiNotices)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/tableau-jwt", wrapper.GetTableauJwt)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/tableau/download/csv", wrapper.PostTableauDownloadCsv)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/tableau/download/pdf", wrapper.PostTableauDownloadPdf)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/unique-jmdc-brdg-ids", wrapper.PostUniqueJmdcBrdgIds)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/user-info", wrapper.GetApiUserInfo)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/users/level2", wrapper.GetApiLevel2Users)
	})

	return r
}

type PostApiEmplrByIdRequestObject struct {
	Body *PostApiEmplrByIdJSONRequestBody
}

type PostApiEmplrByIdResponseObject interface {
	VisitPostApiEmplrByIdResponse(w http.ResponseWriter) error
}

type PostApiEmplrById200JSONResponse struct {
	Emplr *MstEmplrDetail `json:"emplr,omitempty"`
}

func (response PostApiEmplrById200JSONResponse) VisitPostApiEmplrByIdResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type PostApiEmplrById400JSONResponse InternalServerError

func (response PostApiEmplrById400JSONResponse) VisitPostApiEmplrByIdResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(400)

	return json.NewEncoder(w).Encode(response)
}

type PostApiEmplrById404JSONResponse InternalServerError

func (response PostApiEmplrById404JSONResponse) VisitPostApiEmplrByIdResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(404)

	return json.NewEncoder(w).Encode(response)
}

type PostApiEmplrById500JSONResponse InternalServerError

func (response PostApiEmplrById500JSONResponse) VisitPostApiEmplrByIdResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetApiEmplrsRequestObject struct {
	Params GetApiEmplrsParams
}

type GetApiEmplrsResponseObject interface {
	VisitGetApiEmplrsResponse(w http.ResponseWriter) error
}

type GetApiEmplrs200JSONResponse struct {
	Emplrs *[]EmplrDetail `json:"emplrs,omitempty"`
	Total  *int           `json:"total,omitempty"`
}

func (response GetApiEmplrs200JSONResponse) VisitGetApiEmplrsResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetApiEmplrs401JSONResponse InternalServerError

func (response GetApiEmplrs401JSONResponse) VisitGetApiEmplrsResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(401)

	return json.NewEncoder(w).Encode(response)
}

type GetApiEmplrs500JSONResponse InternalServerError

func (response GetApiEmplrs500JSONResponse) VisitGetApiEmplrsResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetApiMaintenanceInfoRequestObject struct {
	Params GetApiMaintenanceInfoParams
}

type GetApiMaintenanceInfoResponseObject interface {
	VisitGetApiMaintenanceInfoResponse(w http.ResponseWriter) error
}

type GetApiMaintenanceInfo200JSONResponse MaintenanceListResponse

func (response GetApiMaintenanceInfo200JSONResponse) VisitGetApiMaintenanceInfoResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetApiMaintenanceInfo401JSONResponse InternalServerError

func (response GetApiMaintenanceInfo401JSONResponse) VisitGetApiMaintenanceInfoResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(401)

	return json.NewEncoder(w).Encode(response)
}

type GetApiMaintenanceInfo500JSONResponse InternalServerError

func (response GetApiMaintenanceInfo500JSONResponse) VisitGetApiMaintenanceInfoResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetApiMaintenanceInfoByIdRequestObject struct {
	Id int `json:"id"`
}

type GetApiMaintenanceInfoByIdResponseObject interface {
	VisitGetApiMaintenanceInfoByIdResponse(w http.ResponseWriter) error
}

type GetApiMaintenanceInfoById200JSONResponse MaintenanceInfo

func (response GetApiMaintenanceInfoById200JSONResponse) VisitGetApiMaintenanceInfoByIdResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetApiMaintenanceInfoById401JSONResponse InternalServerError

func (response GetApiMaintenanceInfoById401JSONResponse) VisitGetApiMaintenanceInfoByIdResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(401)

	return json.NewEncoder(w).Encode(response)
}

type GetApiMaintenanceInfoById404JSONResponse InternalServerError

func (response GetApiMaintenanceInfoById404JSONResponse) VisitGetApiMaintenanceInfoByIdResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(404)

	return json.NewEncoder(w).Encode(response)
}

type GetApiMaintenanceInfoById500JSONResponse InternalServerError

func (response GetApiMaintenanceInfoById500JSONResponse) VisitGetApiMaintenanceInfoByIdResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetApiNoticeByIdRequestObject struct {
	Id int `json:"id"`
}

type GetApiNoticeByIdResponseObject interface {
	VisitGetApiNoticeByIdResponse(w http.ResponseWriter) error
}

type GetApiNoticeById200JSONResponse NoticeInfo

func (response GetApiNoticeById200JSONResponse) VisitGetApiNoticeByIdResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetApiNoticeById401JSONResponse InternalServerError

func (response GetApiNoticeById401JSONResponse) VisitGetApiNoticeByIdResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(401)

	return json.NewEncoder(w).Encode(response)
}

type GetApiNoticeById404JSONResponse InternalServerError

func (response GetApiNoticeById404JSONResponse) VisitGetApiNoticeByIdResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(404)

	return json.NewEncoder(w).Encode(response)
}

type GetApiNoticeById500JSONResponse InternalServerError

func (response GetApiNoticeById500JSONResponse) VisitGetApiNoticeByIdResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetApiNoticesRequestObject struct {
	Params GetApiNoticesParams
}

type GetApiNoticesResponseObject interface {
	VisitGetApiNoticesResponse(w http.ResponseWriter) error
}

type GetApiNotices200JSONResponse NoticeListResponse

func (response GetApiNotices200JSONResponse) VisitGetApiNoticesResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetApiNotices401JSONResponse InternalServerError

func (response GetApiNotices401JSONResponse) VisitGetApiNoticesResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(401)

	return json.NewEncoder(w).Encode(response)
}

type GetApiNotices500JSONResponse InternalServerError

func (response GetApiNotices500JSONResponse) VisitGetApiNoticesResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetTableauJwtRequestObject struct {
}

type GetTableauJwtResponseObject interface {
	VisitGetTableauJwtResponse(w http.ResponseWriter) error
}

type GetTableauJwt200JSONResponse struct {
	// Token JWT token for Tableau embedding
	Token string `json:"token"`
}

func (response GetTableauJwt200JSONResponse) VisitGetTableauJwtResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetTableauJwt500JSONResponse struct {
	Message string `json:"message"`
}

func (response GetTableauJwt500JSONResponse) VisitGetTableauJwtResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type PostTableauDownloadCsvRequestObject struct {
	Body *PostTableauDownloadCsvJSONRequestBody
}

type PostTableauDownloadCsvResponseObject interface {
	VisitPostTableauDownloadCsvResponse(w http.ResponseWriter) error
}

type PostTableauDownloadCsv200TextcsvResponse struct {
	Body          io.Reader
	ContentLength int64
}

func (response PostTableauDownloadCsv200TextcsvResponse) VisitPostTableauDownloadCsvResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "text/csv")
	if response.ContentLength != 0 {
		w.Header().Set("Content-Length", fmt.Sprint(response.ContentLength))
	}
	w.WriteHeader(200)

	if closer, ok := response.Body.(io.ReadCloser); ok {
		defer closer.Close()
	}
	_, err := io.Copy(w, response.Body)
	return err
}

type PostTableauDownloadCsv400JSONResponse struct {
	Message *string `json:"message,omitempty"`
}

func (response PostTableauDownloadCsv400JSONResponse) VisitPostTableauDownloadCsvResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(400)

	return json.NewEncoder(w).Encode(response)
}

type PostTableauDownloadCsv401JSONResponse struct {
	Message *string `json:"message,omitempty"`
}

func (response PostTableauDownloadCsv401JSONResponse) VisitPostTableauDownloadCsvResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(401)

	return json.NewEncoder(w).Encode(response)
}

type PostTableauDownloadCsv500JSONResponse struct {
	Message *string `json:"message,omitempty"`
}

func (response PostTableauDownloadCsv500JSONResponse) VisitPostTableauDownloadCsvResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type PostTableauDownloadPdfRequestObject struct {
	Body *PostTableauDownloadPdfJSONRequestBody
}

type PostTableauDownloadPdfResponseObject interface {
	VisitPostTableauDownloadPdfResponse(w http.ResponseWriter) error
}

type PostTableauDownloadPdf200ApplicationpdfResponse struct {
	Body          io.Reader
	ContentLength int64
}

func (response PostTableauDownloadPdf200ApplicationpdfResponse) VisitPostTableauDownloadPdfResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/pdf")
	if response.ContentLength != 0 {
		w.Header().Set("Content-Length", fmt.Sprint(response.ContentLength))
	}
	w.WriteHeader(200)

	if closer, ok := response.Body.(io.ReadCloser); ok {
		defer closer.Close()
	}
	_, err := io.Copy(w, response.Body)
	return err
}

type PostTableauDownloadPdf400JSONResponse struct {
	Message *string `json:"message,omitempty"`
}

func (response PostTableauDownloadPdf400JSONResponse) VisitPostTableauDownloadPdfResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(400)

	return json.NewEncoder(w).Encode(response)
}

type PostTableauDownloadPdf401JSONResponse struct {
	Message *string `json:"message,omitempty"`
}

func (response PostTableauDownloadPdf401JSONResponse) VisitPostTableauDownloadPdfResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(401)

	return json.NewEncoder(w).Encode(response)
}

type PostTableauDownloadPdf500JSONResponse struct {
	Message *string `json:"message,omitempty"`
}

func (response PostTableauDownloadPdf500JSONResponse) VisitPostTableauDownloadPdfResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type PostUniqueJmdcBrdgIdsRequestObject struct {
	Body *PostUniqueJmdcBrdgIdsJSONRequestBody
}

type PostUniqueJmdcBrdgIdsResponseObject interface {
	VisitPostUniqueJmdcBrdgIdsResponse(w http.ResponseWriter) error
}

type PostUniqueJmdcBrdgIds200JSONResponse struct {
	// JmdcBrdgIds JMDC_BRDG_IDリスト
	JmdcBrdgIds []string `json:"jmdc_brdg_ids,omitempty"`

	// Total リストの総数
	Total *int `json:"total,omitempty"`
}

func (response PostUniqueJmdcBrdgIds200JSONResponse) VisitPostUniqueJmdcBrdgIdsResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type PostUniqueJmdcBrdgIds500JSONResponse InternalServerError

func (response PostUniqueJmdcBrdgIds500JSONResponse) VisitPostUniqueJmdcBrdgIdsResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetApiUserInfoRequestObject struct {
}

type GetApiUserInfoResponseObject interface {
	VisitGetApiUserInfoResponse(w http.ResponseWriter) error
}

type GetApiUserInfo200JSONResponse UserDetail

func (response GetApiUserInfo200JSONResponse) VisitGetApiUserInfoResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetApiUserInfo401JSONResponse struct {
	Message *string `json:"message,omitempty"`
}

func (response GetApiUserInfo401JSONResponse) VisitGetApiUserInfoResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(401)

	return json.NewEncoder(w).Encode(response)
}

type GetApiUserInfo500JSONResponse InternalServerError

func (response GetApiUserInfo500JSONResponse) VisitGetApiUserInfoResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetApiLevel2UsersRequestObject struct {
	Params GetApiLevel2UsersParams
}

type GetApiLevel2UsersResponseObject interface {
	VisitGetApiLevel2UsersResponse(w http.ResponseWriter) error
}

type GetApiLevel2Users200JSONResponse struct {
	Total *int          `json:"total,omitempty"`
	Users *[]UserDetail `json:"users,omitempty"`
}

func (response GetApiLevel2Users200JSONResponse) VisitGetApiLevel2UsersResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetApiLevel2Users401JSONResponse InternalServerError

func (response GetApiLevel2Users401JSONResponse) VisitGetApiLevel2UsersResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(401)

	return json.NewEncoder(w).Encode(response)
}

type GetApiLevel2Users500JSONResponse InternalServerError

func (response GetApiLevel2Users500JSONResponse) VisitGetApiLevel2UsersResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

// StrictServerInterface represents all server handlers.
type StrictServerInterface interface {
	// 組合情報取得（ID指定）
	// (POST /emplr-by-id)
	PostApiEmplrById(ctx context.Context, request PostApiEmplrByIdRequestObject) (PostApiEmplrByIdResponseObject, error)
	// 組合一覧取得
	// (GET /emplrs)
	GetApiEmplrs(ctx context.Context, request GetApiEmplrsRequestObject) (GetApiEmplrsResponseObject, error)
	// メンテナンス情報一覧取得（ページング付き）
	// (GET /maintenance-info)
	GetApiMaintenanceInfo(ctx context.Context, request GetApiMaintenanceInfoRequestObject) (GetApiMaintenanceInfoResponseObject, error)
	// メンテナンス情報詳細取得
	// (GET /maintenance-info/{id})
	GetApiMaintenanceInfoById(ctx context.Context, request GetApiMaintenanceInfoByIdRequestObject) (GetApiMaintenanceInfoByIdResponseObject, error)
	// お知らせ詳細取得
	// (GET /notice/{id})
	GetApiNoticeById(ctx context.Context, request GetApiNoticeByIdRequestObject) (GetApiNoticeByIdResponseObject, error)
	// お知らせ一覧取得（ページング付き）
	// (GET /notices)
	GetApiNotices(ctx context.Context, request GetApiNoticesRequestObject) (GetApiNoticesResponseObject, error)
	// Generate JWT token for Tableau embedding
	// (GET /tableau-jwt)
	GetTableauJwt(ctx context.Context, request GetTableauJwtRequestObject) (GetTableauJwtResponseObject, error)
	// Download CSV from Tableau view
	// (POST /tableau/download/csv)
	PostTableauDownloadCsv(ctx context.Context, request PostTableauDownloadCsvRequestObject) (PostTableauDownloadCsvResponseObject, error)
	// Download PDF from Tableau view
	// (POST /tableau/download/pdf)
	PostTableauDownloadPdf(ctx context.Context, request PostTableauDownloadPdfRequestObject) (PostTableauDownloadPdfResponseObject, error)
	// 重複なしJMDC_BRDG_IDリストの取得
	// (POST /unique-jmdc-brdg-ids)
	PostUniqueJmdcBrdgIds(ctx context.Context, request PostUniqueJmdcBrdgIdsRequestObject) (PostUniqueJmdcBrdgIdsResponseObject, error)
	// Auth0トークンによるユーザー情報取得
	// (GET /user-info)
	GetApiUserInfo(ctx context.Context, request GetApiUserInfoRequestObject) (GetApiUserInfoResponseObject, error)
	// Level 2 ユーザー一覧取得
	// (GET /users/level2)
	GetApiLevel2Users(ctx context.Context, request GetApiLevel2UsersRequestObject) (GetApiLevel2UsersResponseObject, error)
}

type StrictHandlerFunc = strictnethttp.StrictHTTPHandlerFunc
type StrictMiddlewareFunc = strictnethttp.StrictHTTPMiddlewareFunc

type StrictHTTPServerOptions struct {
	RequestErrorHandlerFunc  func(w http.ResponseWriter, r *http.Request, err error)
	ResponseErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

func NewStrictHandler(ssi StrictServerInterface, middlewares []StrictMiddlewareFunc) ServerInterface {
	return &strictHandler{ssi: ssi, middlewares: middlewares, options: StrictHTTPServerOptions{
		RequestErrorHandlerFunc: func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		},
		ResponseErrorHandlerFunc: func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusInternalServerError)
		},
	}}
}

func NewStrictHandlerWithOptions(ssi StrictServerInterface, middlewares []StrictMiddlewareFunc, options StrictHTTPServerOptions) ServerInterface {
	return &strictHandler{ssi: ssi, middlewares: middlewares, options: options}
}

type strictHandler struct {
	ssi         StrictServerInterface
	middlewares []StrictMiddlewareFunc
	options     StrictHTTPServerOptions
}

// PostApiEmplrById operation middleware
func (sh *strictHandler) PostApiEmplrById(w http.ResponseWriter, r *http.Request) {
	var request PostApiEmplrByIdRequestObject

	var body PostApiEmplrByIdJSONRequestBody
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode JSON body: %w", err))
		return
	}
	request.Body = &body

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.PostApiEmplrById(ctx, request.(PostApiEmplrByIdRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "PostApiEmplrById")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(PostApiEmplrByIdResponseObject); ok {
		if err := validResponse.VisitPostApiEmplrByIdResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetApiEmplrs operation middleware
func (sh *strictHandler) GetApiEmplrs(w http.ResponseWriter, r *http.Request, params GetApiEmplrsParams) {
	var request GetApiEmplrsRequestObject

	request.Params = params

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetApiEmplrs(ctx, request.(GetApiEmplrsRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetApiEmplrs")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetApiEmplrsResponseObject); ok {
		if err := validResponse.VisitGetApiEmplrsResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetApiMaintenanceInfo operation middleware
func (sh *strictHandler) GetApiMaintenanceInfo(w http.ResponseWriter, r *http.Request, params GetApiMaintenanceInfoParams) {
	var request GetApiMaintenanceInfoRequestObject

	request.Params = params

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetApiMaintenanceInfo(ctx, request.(GetApiMaintenanceInfoRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetApiMaintenanceInfo")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetApiMaintenanceInfoResponseObject); ok {
		if err := validResponse.VisitGetApiMaintenanceInfoResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetApiMaintenanceInfoById operation middleware
func (sh *strictHandler) GetApiMaintenanceInfoById(w http.ResponseWriter, r *http.Request, id int) {
	var request GetApiMaintenanceInfoByIdRequestObject

	request.Id = id

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetApiMaintenanceInfoById(ctx, request.(GetApiMaintenanceInfoByIdRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetApiMaintenanceInfoById")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetApiMaintenanceInfoByIdResponseObject); ok {
		if err := validResponse.VisitGetApiMaintenanceInfoByIdResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetApiNoticeById operation middleware
func (sh *strictHandler) GetApiNoticeById(w http.ResponseWriter, r *http.Request, id int) {
	var request GetApiNoticeByIdRequestObject

	request.Id = id

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetApiNoticeById(ctx, request.(GetApiNoticeByIdRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetApiNoticeById")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetApiNoticeByIdResponseObject); ok {
		if err := validResponse.VisitGetApiNoticeByIdResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetApiNotices operation middleware
func (sh *strictHandler) GetApiNotices(w http.ResponseWriter, r *http.Request, params GetApiNoticesParams) {
	var request GetApiNoticesRequestObject

	request.Params = params

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetApiNotices(ctx, request.(GetApiNoticesRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetApiNotices")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetApiNoticesResponseObject); ok {
		if err := validResponse.VisitGetApiNoticesResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetTableauJwt operation middleware
func (sh *strictHandler) GetTableauJwt(w http.ResponseWriter, r *http.Request) {
	var request GetTableauJwtRequestObject

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetTableauJwt(ctx, request.(GetTableauJwtRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetTableauJwt")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetTableauJwtResponseObject); ok {
		if err := validResponse.VisitGetTableauJwtResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// PostTableauDownloadCsv operation middleware
func (sh *strictHandler) PostTableauDownloadCsv(w http.ResponseWriter, r *http.Request) {
	var request PostTableauDownloadCsvRequestObject

	var body PostTableauDownloadCsvJSONRequestBody
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode JSON body: %w", err))
		return
	}
	request.Body = &body

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.PostTableauDownloadCsv(ctx, request.(PostTableauDownloadCsvRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "PostTableauDownloadCsv")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(PostTableauDownloadCsvResponseObject); ok {
		if err := validResponse.VisitPostTableauDownloadCsvResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// PostTableauDownloadPdf operation middleware
func (sh *strictHandler) PostTableauDownloadPdf(w http.ResponseWriter, r *http.Request) {
	var request PostTableauDownloadPdfRequestObject

	var body PostTableauDownloadPdfJSONRequestBody
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode JSON body: %w", err))
		return
	}
	request.Body = &body

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.PostTableauDownloadPdf(ctx, request.(PostTableauDownloadPdfRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "PostTableauDownloadPdf")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(PostTableauDownloadPdfResponseObject); ok {
		if err := validResponse.VisitPostTableauDownloadPdfResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// PostUniqueJmdcBrdgIds operation middleware
func (sh *strictHandler) PostUniqueJmdcBrdgIds(w http.ResponseWriter, r *http.Request) {
	var request PostUniqueJmdcBrdgIdsRequestObject

	var body PostUniqueJmdcBrdgIdsJSONRequestBody
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode JSON body: %w", err))
		return
	}
	request.Body = &body

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.PostUniqueJmdcBrdgIds(ctx, request.(PostUniqueJmdcBrdgIdsRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "PostUniqueJmdcBrdgIds")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(PostUniqueJmdcBrdgIdsResponseObject); ok {
		if err := validResponse.VisitPostUniqueJmdcBrdgIdsResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetApiUserInfo operation middleware
func (sh *strictHandler) GetApiUserInfo(w http.ResponseWriter, r *http.Request) {
	var request GetApiUserInfoRequestObject

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetApiUserInfo(ctx, request.(GetApiUserInfoRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetApiUserInfo")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetApiUserInfoResponseObject); ok {
		if err := validResponse.VisitGetApiUserInfoResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetApiLevel2Users operation middleware
func (sh *strictHandler) GetApiLevel2Users(w http.ResponseWriter, r *http.Request, params GetApiLevel2UsersParams) {
	var request GetApiLevel2UsersRequestObject

	request.Params = params

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetApiLevel2Users(ctx, request.(GetApiLevel2UsersRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetApiLevel2Users")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetApiLevel2UsersResponseObject); ok {
		if err := validResponse.VisitGetApiLevel2UsersResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}
