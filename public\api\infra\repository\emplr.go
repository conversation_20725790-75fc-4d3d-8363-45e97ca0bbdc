package repository

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/db"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/errof"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/model"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/repository"
	"github.com/uptrace/bun"
)

type emplr struct {
	dbClient db.Client
}

func NewEmplr(dbClient db.Client) repository.EmplrRepository {
	return &emplr{dbClient}
}

func (r *emplr) GetEmplrs(ctx context.Context, keyword *string) ([]*model.EmplrDetail, error) {
	var results []dbmodels.MNGEmplr

	if err := r.dbClient.DB.NewSelect().Model(&results).
		Column("emplr_id", "emplr_cd", "custom_emplr_name").
		Where("mng_emplr.emplr_id != ?", 999999).
		Apply(func(query *bun.SelectQuery) *bun.SelectQuery {
			if keyword == nil {
				return query
			}
			keywordWithWildcard := "%" + *keyword + "%"
			return query.Where("mng_emplr.emplr_cd ILIKE ? OR mng_emplr.custom_emplr_name ILIKE ?", keywordWithWildcard, keywordWithWildcard)
		}).
		Order("mng_emplr.emplr_cd").
		Scan(ctx); err != nil {

		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	retList := make([]*model.EmplrDetail, 0, len(results))
	for _, result := range results {
		item := model.EmplrDetail{
			EmplrID:         result.EmplrID,
			EmplrCD:         result.EmplrCD,
			CustomEmplrName: result.CustomEmplrName,
		}
		retList = append(retList, &item)
	}

	return retList, nil
}
