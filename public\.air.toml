# .air.toml
root = "."
tmp_dir = "tmp"

[build]
  cmd = "go build -o ./tmp/main ./main/main.go"
  bin = "tmp/main"
  full_bin = "./tmp/main manage"
  delay = 1000 # ms
  include_ext = ["go", "tpl", "tmpl", "html"]
  exclude_dir = ["assets", "tmp", "vendor"]
  include_dir = []
  exclude_regex = ["_test.go"]
  log = "air.log"
  ignore_gitignore = true

[log]
  time = true

[misc]
  clean_on_exit = true
