// Package dbmodels is models from database tables.
package dbmodels

import "github.com/uptrace/bun"

type MNGUser struct {
	bun.BaseModel   `bun:"kensuke_plus.tbl_mng_user"`
	UserID          int    `bun:"user_id,pk,autoincrement"`
	Auth0Id         string `bun:"auth0_id,notnull"`
	Name            string `bun:"name,notnull"`
	Email           string `bun:"email,notnull"`
	Role            int    `bun:"role,notnull"`
	HierarchyRoleID int    `bun:"hierarchy_role_id,notnull"`
	Comment         string `bun:"comment,notnull"`
}
type MNGUserSimple struct {
	bun.BaseModel   `bun:"kensuke_plus.tbl_mng_user,alias:tmu"`
	UserID          int    `bun:"user_id,pk"`
	Name            string `bun:"name,notnull"`
	Email           string `bun:"email,notnull"`
	Role            int    `bun:"role,notnull"`
	EmplrCD         string `bun:"emplr_cd,notnull"`
	CustomEmplrName string `bun:"custom_emplr_name,notnull"`
	HierarchyLevel  int    `bun:"hierarchy_level,notnull"`
}
type MNGUserDetail struct {
	bun.BaseModel `bun:"kensuke_plus.tbl_mng_user,alias:tmu"`
	UserID        int    `bun:"user_id,pk"`
	Auth0ID       string `bun:"auth0_id,notnull"`
	Name          string `bun:"name,notnull"`
	Email         string `bun:"email,notnull"`
	Role          int    `bun:"role,notnull"`
	Comment       string `bun:"comment,notnull"`
	TableauID     string `bun:"tableau_id"`

	EmplrID         int    `bun:"emplr_id,notnull"`
	EmplrCD         string `bun:"emplr_cd,notnull"`
	CustomEmplrName string `bun:"custom_emplr_name,notnull"`

	HierarchyRoleID int    `bun:"hierarchy_role_id,notnull"`
	HierarchyLevel  int    `bun:"hierarchy_level,notnull"`
	JigyosyoCD      string `bun:"jigyosyo_cd"`
}
