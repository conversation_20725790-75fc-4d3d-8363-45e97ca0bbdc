package usecase

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/public"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/repository"
)

type Emplr struct {
	EmplrRepo repository.EmplrRepository
}

func NewEmplr(EmplrRepo repository.EmplrRepository) EmplrUseCase {
	return &Emplr{
		EmplrRepo: EmplrRepo,
	}
}

func (e Emplr) GetEmplrs(ctx context.Context, keyword *string) (oapi.GetApiEmplrsResponseObject, error) {
	emplrs, err := e.EmplrRepo.GetEmplrs(ctx, keyword)
	if err != nil {
		message := err.Error()
		return oapi.GetApiEmplrs500JSONResponse{
			Message: message,
		}, err
	}

	totalEmplrs := len(emplrs)
	var emplrsInfo []oapi.EmplrDetail
	for _, e := range emplrs {
		emplrsInfo = append(emplrsInfo, oapi.EmplrDetail{
			EmplrId:         e.EmplrID,
			EmplrCd:         e.EmplrCD,
			CustomEmplrName: e.CustomEmplrName,
		})
	}
	return oapi.GetApiEmplrs200JSONResponse{
		Emplrs: &emplrsInfo,
		Total:  &totalEmplrs,
	}, nil
}

func (e Emplr) GetMstEmplrByID(ctx context.Context, employId int) (oapi.PostApiEmplrByIdResponseObject, error) {
	emplr, err := e.EmplrRepo.GetMstEmplrByID(ctx, employId)
	if err != nil {
		message := err.Error()
		return oapi.PostApiEmplrById500JSONResponse{
			Message: message,
		}, err
	}

	if emplr == nil {
		message := "Employer not found"
		return oapi.PostApiEmplrById404JSONResponse{
			Message: message,
		}, nil
	}

	return oapi.PostApiEmplrById200JSONResponse{
		Emplr: &oapi.MstEmplrDetail{
			EmplrId:              emplr.EmplrID,
			EmplrCd:              emplr.EmplrCD,
			EmplrName:            emplr.EmplrName,
			SecondaryUseProperty: emplr.SecondaryUseProperty,
		},
	}, nil
}
