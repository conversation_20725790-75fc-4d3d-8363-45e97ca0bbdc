package usecase

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/public"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/repository"
)

type Emplr struct {
	EmplrRepo repository.EmplrRepository
}

func NewEmplr(EmplrRepo repository.EmplrRepository) EmplrUseCase {
	return &Emplr{
		EmplrRepo: EmplrRepo,
	}
}

func (e Emplr) GetEmplrs(ctx context.Context, keyword *string) (oapi.GetApiEmplrsResponseObject, error) {
	emplrs, err := e.EmplrRepo.GetEmplrs(ctx, keyword)
	if err != nil {
		message := err.Error()
		return oapi.GetApiEmplrs500JSONResponse{
			Message: message,
		}, err
	}

	totalEmplrs := len(emplrs)
	var emplrsInfo []oapi.EmplrDetail
	for _, e := range emplrs {
		emplrsInfo = append(emplrsInfo, oapi.EmplrDetail{
			EmplrId:         e.EmplrID,
			EmplrCd:         e.EmplrCD,
			CustomEmplrName: e.CustomEmplrName,
		})
	}
	return oapi.GetApiEmplrs200JSONResponse{
		Emplrs: &emplrsInfo,
		Total:  &totalEmplrs,
	}, nil
}

func (e Emplr) GetMstEmplrByID(ctx context.Context, employId int) (oapi.PostApiEmplrsResponseObject, error) {
	emplr, err := e.EmplrRepo.GetMstEmplrByID(ctx, employId)
	if err != nil {
		message := err.Error()
		return oapi.PostApiEmplrs500JSONResponse{
			Message: message,
		}, err
	}

	if emplr == nil {
		message := "Employer not found"
		return oapi.PostApiEmplrs404JSONResponse{
			Message: message,
		}, nil
	}

	// Convert time pointers to strings for JSON response
	var memUpdDate, flaUpdDate, receUpdDate *string
	if emplr.MemUPDDATE != nil {
		timeStr := emplr.MemUPDDATE.Format("2006-01-02T15:04:05Z07:00")
		memUpdDate = &timeStr
	}
	if emplr.FlaUPDDATE != nil {
		timeStr := emplr.FlaUPDDATE.Format("2006-01-02T15:04:05Z07:00")
		flaUpdDate = &timeStr
	}
	if emplr.ReceUPDDATE != nil {
		timeStr := emplr.ReceUPDDATE.Format("2006-01-02T15:04:05Z07:00")
		receUpdDate = &timeStr
	}

	return oapi.PostApiEmplrs200JSONResponse{
		Emplr: &oapi.MstEmplrDetail{
			EmplrId:            emplr.EmplrID,
			EmplrName:          emplr.EmplrName,
			EmplrLabel:         emplr.EmplrLabel,
			RsvPtn:             emplr.RSVPtn,
			EmplrKbn:           emplr.EmplrKBN,
			ReceStYm:           emplr.ReceSTYM,
			ReceEdYm:           emplr.ReceEDYM,
			MemStYm:            emplr.MemSTYM,
			MemEdYm:            emplr.MemEDYM,
			HeStYm:             emplr.HeSTYM,
			HeEdYm:             emplr.HeEDYM,
			FlaStYm:            emplr.FlaSTYM,
			FlaEdYm:            emplr.FlaEDYM,
			FullStYm:           emplr.FullSTYM,
			FullEdYm:           emplr.FullEDYM,
			MemUpdDate:         memUpdDate,
			FlaUpdDate:         flaUpdDate,
			ReceUpdDate:        receUpdDate,
			DeptUseFlg:         emplr.DeptUseFlg,
			DelFlg:             emplr.DelFlg,
			PasswordPattern:    emplr.PasswordPattern,
			PasswordChangeDays: emplr.PasswordChangeDays,
			LockCount:          emplr.LockCount,
		},
	}, nil
}
