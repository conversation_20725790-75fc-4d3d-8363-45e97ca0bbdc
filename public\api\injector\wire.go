//go:build wireinject
// +build wireinject

package injector

import (
	"log/slog"

	"github.com/google/wire"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/authenticator"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/aws/s3"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/config"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/db"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/service"
	handler "github.com/jmdc-inc/kensuke-plus-server/public/api/handler"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/infra/repository"
	usecase "github.com/jmdc-inc/kensuke-plus-server/public/api/usecase"
)

func InitializeAPIHandler(appLogger *slog.Logger, pgCfg config.Database, sfCfg config.SnowflakeDB, auth0Cfg config.Auth0, awsCfg config.AWS, appCfg config.AppConfig) (_ *handler.API) {
	wire.Build(
		db.NewPostgres,
		db.NewSnowflakeDB,
		db.NewClient,
		authenticator.New,
		s3.New,
		repository.NewUser,
		repository.NewEmplr,
		service.NewAuth0TokenService,
		repository.NewNotice,
		repository.NewMaintenance,
		usecase.NewUser,
		usecase.NewEmplr,
		usecase.NewNotice,
		usecase.NewMaintenance,
		service.NewTableauJWTService,
		usecase.NewTableauUseCase,
		service.NewTableauDownloadService,
		usecase.NewTableauDownloadUseCase,
		repository.NewSubscriberInfoRepository,
		usecase.NewSubscriberInfoUseCase,
		handler.NewAPI,
	)
	return
}
