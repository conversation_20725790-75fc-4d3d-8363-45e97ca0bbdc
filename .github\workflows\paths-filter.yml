# .github/workflows/paths-filter.yml
name: Paths filter

on:
  workflow_call:
    inputs:
      additionalTriggerFile:
        required: false
        type: string
    outputs:
      isFileChanged:
        description: Indicates if the file change matches the filters
        value: ${{ jobs.paths.outputs.isFileChanged }}
      isManageChanged:
        description: Indicates if the manage file change matches the filters
        value: ${{ jobs.paths.outputs.isManageChanged }}
      isPublicChanged:
        description: Indicates if the public file change matches the filters
        value: ${{ jobs.paths.outputs.isPublicChanged }}
      isPackageChanged:
        description: Indicates if the package file change matches the filters
        value: ${{ jobs.paths.outputs.isPackageChanged }}

jobs:
  paths:
    runs-on: ubuntu-latest
    outputs:
      isManageChanged: ${{ steps.changes.outputs.manage == 'true' }}
      isPublicChanged: ${{ steps.changes.outputs.public == 'true' }}
      isPackageChanged: ${{ steps.changes.outputs.public == 'true' }}
    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Paths filter
        uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            shared: &shared
              - ${{ inputs.additionalTriggerFile }}
              - "go.mod"
              - "go.sum"
              - ".golangci.yml"
            manage:
              - *shared
              - "mng/**/**.go"
              - "mng/main/env.yml"
            public:
              - *shared
              - "public/**/**.go"
              - "public/main/env.yml"
            package:
              - *shared
              - "pkg/**/**.go"
