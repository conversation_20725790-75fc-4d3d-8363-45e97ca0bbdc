// main.go
package main

import (
	_ "embed"
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"github.com/jmdc-inc/kensuke-plus-server/mng/router"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/config"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/util"
)

//go:embed env.yml
var envYaml []byte

// init関数はmain関数よりも先に実行される
func init() {
	util.IntLocal()
	// loadEnvFromSecretsManager()
}

func main() {
	config.EnvYaml = envYaml
	appConfig := config.Prepare()
	appLogger := slog.New(slog.NewJSONHandler(os.Stdout, nil))
	slog.SetDefault(appLogger)

	isManageMode := false
	if len(os.Args) > 1 {
		if os.Args[1] == "manage" {
			isManageMode = true
		}
	}

	var r http.Handler
	if isManageMode {
		r = router.NewManageRouter(appLogger, appConfig)
	} else {
		r = router.NewPublicRouter(appLogger, appConfig)
	}

	appLogger.Info("Starting the server")

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGTERM, os.Interrupt)

	addr := ":8082"
	fmt.Printf("Server running on %s\n", addr)
	s := &http.Server{
		Handler:     r,
		Addr:        addr,
		ReadTimeout: 5 * 1e9,
	}
	// サーバー起動
	if err := s.ListenAndServe(); err != nil {
		appLogger.Error("server error:", slog.Any("cause", err))
		os.Exit(1)
	}
}
