// Package config is setting environment.
package config

import (
	"bytes"
	"fmt"
	"log/slog"
	"os"
	"strings"

	"github.com/spf13/viper"
)

var EnvYaml []byte

type Env struct {
	Development AppConfig `mapstructure:"development"`
	Production  AppConfig `mapstructure:"production"`
	Local       AppConfig `mapstructure:"local"`
	Test        AppConfig `mapstructure:"test"`
}

type AppConfig struct {
	Env       string
	Database  Database    `mapstructure:"database"`
	Auth0     Auth0       `mapstructure:"auth0"`
	CORS      CORS        `mapstructure:"cors"`
	AWS       AWS         `mapstructure:"aws"`
	Tableau   Tableau     `mapstructure:"tableau"`
	Snowflake SnowflakeDB `mapstructure:"snowflake"`
	Server    Server      `mapstructure:"server"`
}

type Database struct {
	Host     string `mapstructure:"host"`
	Port     string `mapstructure:"port"`
	Username string `mapstructure:"user"`
	DBName   string `mapstructure:"name"`
	Password string `mapstructure:"password"`
	SSL      string `mapstructure:"ssl"`
}

type Auth0 struct {
	ClientID     string `mapstructure:"client_id"`
	Connection   string `mapstructure:"connection"`
	Domain       string `mapstructure:"domain"`
	ClientSecret string `mapstructure:"client_secret"`
}
type AWS struct {
	AttachedFileBucketName   string `mapstructure:"attached_file_bucket_name"`
	ShareSnowFlakeBucketName string `mapstructure:"share_snowflake_bucket_name"`
	HelthcheckID             string `mapstructure:"helthcheck_id"`
}
type CORS struct {
	ShouldDebug  bool     `mapstructure:"should_debug"`
	AllowOrigins []string `mapstructure:"allow_origins"`
}
type SnowflakeDB struct {
	Account   string `mapstructure:"account"`
	User      string `mapstructure:"user"`
	Password  string `mapstructure:"password"`
	Role      string `mapstructure:"role"`
	Warehouse string `mapstructure:"warehouse"`
	Database  string `mapstructure:"database"`
	Schema    string `mapstructure:"schema"`
}

type Tableau struct {
	JWT    TableauJWT    `mapstructure:"jwt"`
	Server TableauServer `mapstructure:"server"`
}

type TableauJWT struct {
	SecretValue   string `mapstructure:"secret_value"`
	ClientID      string `mapstructure:"client_id"`
	SecretID      string `mapstructure:"secret_id"`
	SecretUser    string `mapstructure:"secret_user"`
	ExpiryMinutes int    `mapstructure:"expiry_minutes"`
}

type TableauServer struct {
	BaseURL  string `mapstructure:"base_url"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
}

type Server struct {
	Port string `mapstructure:"port"`
}

func Prepare() AppConfig {
	viper.SetConfigName("config")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.SetConfigType("yaml")
	viper.AllowEmptyEnv(true)

	if err := viper.ReadConfig(bytes.NewReader(EnvYaml)); err != nil {
		slog.Error("Failed to read embedded yaml config", slog.Any("cause", err))
		os.Exit(1)
	}

	viper.AutomaticEnv()

	env := viper.GetString("env.name")
	viper.SetDefault(fmt.Sprintf("%s.server.port", env), ":8000")

	var c Env
	if err := viper.Unmarshal(&c); err != nil {
		slog.Error("Failed to unmarshal config", slog.Any("cause", err))
		os.Exit(1)
	}

	var appConfig AppConfig

	switch env {
	case "development":
		appConfig = c.Development
	case "production":
		appConfig = c.Production
	case "local":
		appConfig = c.Local
	case "test":
		appConfig = c.Test
	default:
		panic(fmt.Sprintf("unknown env type: %s", env))
	}
	appConfig.Env = env
	return appConfig
}
