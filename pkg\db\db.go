// Package db is creat db instance
package db

import (
	"log/slog"
	"time"

	"github.com/uptrace/bun"

	"github.com/uptrace/bun/dialect/pgdialect"
	"github.com/uptrace/bun/extra/bunslog"
)

type Client struct {
	DB          *bun.DB
	SnowflakeDB *bun.DB
}

// NewClient :
func NewClient(db PostgresDB, sfDB SnowflakeDB, logger *slog.Logger) Client {
	queryHook := bunslog.NewQueryHook(
		bunslog.WithLogger(logger),                      // logger を設定
		bunslog.WithQueryLogLevel(slog.LevelInfo),       // 通常のクエリのログレベルを指定
		bunslog.WithSlowQueryLogLevel(slog.LevelWarn),   // スロークエリのログレベルを指定
		bunslog.WithErrorQueryLogLevel(slog.LevelError), // 実行クエリエラーのログレベルを指定
		bunslog.WithSlowQueryThreshold(15*time.Minute),  // スロークエリと判断する時間を指定
		bunslog.WithLogFormat(func(event *bun.QueryEvent) []slog.Attr { // ログのフォーマットを指定
			duration := time.Since(event.StartTime)
			return []slog.Attr{
				slog.Any("error", event.Err),                        // エラー内容
				slog.String("tag", "QueryLogging"),                  // CloudWatchで検索しやすいように検索キーを固定で指定
				slog.String("operation", event.Operation()),         // INSERTかDELETEなどの実行タイプ
				slog.String("query", event.Query),                   // 実行SQL
				slog.String("start_time", event.StartTime.String()), // 実行開始時間
				slog.String("duration", duration.String()),          // 実行時間
			}
		}),
	)

	// PostgresDB 用 の bun の DB クライアントをインスタンス化する
	postgres := bun.NewDB(db, &CustomDialect{pgdialect.New()})
	// クエリを実行ログの設定をする
	postgres.AddQueryHook(queryHook)

	// SnowflakeDB 用 の bun の DB クライアントをインスタンス化する
	// Snowflake は PostgreSQL dialect を使用（互換性が高いため）
	snowflake := bun.NewDB(sfDB, pgdialect.New())
	// クエリを実行ログの設定をする
	snowflake.AddQueryHook(queryHook)

	return Client{postgres, snowflake}
}

// NewManagementClient :
func NewManagementClient(db PostgresDB, logger *slog.Logger) Client {
	// PostgresDB 用 の bun の DB クライアントをインスタンス化する
	postgres := bun.NewDB(db, &CustomDialect{pgdialect.New()})
	// クエリを実行ログの設定をする
	postgres.AddQueryHook(bunslog.NewQueryHook(
		bunslog.WithLogger(logger),                      // logger を設定
		bunslog.WithQueryLogLevel(slog.LevelInfo),       // 通常のクエリのログレベルを指定
		bunslog.WithSlowQueryLogLevel(slog.LevelWarn),   // スロークエリのログレベルを指定
		bunslog.WithErrorQueryLogLevel(slog.LevelError), // 実行クエリエラーのログレベルを指定
		bunslog.WithSlowQueryThreshold(15*time.Minute),  // スロークエリと判断する時間を指定
		bunslog.WithLogFormat(func(event *bun.QueryEvent) []slog.Attr { // ログのフォーマットを指定
			duration := time.Since(event.StartTime)
			return []slog.Attr{
				slog.Any("error", event.Err),                        // エラー内容
				slog.String("tag", "QueryLogging"),                  // CloudWatchで検索しやすいように検索キーを固定で指定
				slog.String("operation", event.Operation()),         // INSERTかDELETEなどの実行タイプ
				slog.String("query", event.Query),                   // 実行SQL
				slog.String("start_time", event.StartTime.String()), // 実行開始時間
				slog.String("duration", duration.String()),          // 実行時間
			}
		}),
	))

	return Client{postgres, nil}
}
