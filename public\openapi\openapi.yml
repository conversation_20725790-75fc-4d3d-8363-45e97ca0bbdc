openapi: 3.0.3
info:
  title: 健助+API
  version: "1.0.0"

servers:
  - url: https://example.com/api
    description: プロダクション API
  - url: http://{host}:{port}
    description: 開発用
    variables:
      host:
        default: localhost
      port:
        default: '8080'
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    InternalServerError:
      type: object
      properties:
        message:
          type: string
          description: Error message
          example: "Internal server error"
      required:
        - message
    UserDetail:
      type: object
      properties:
        id:
          type: integer
          description: ユーザーID
          example: jmdc-yamada
        name:
          type: string
          format: string
          description: ユーザー名
          example: 山田太郎
        email:
          type: string
          format: email
          example: <EMAIL>
          description: メールアドレス
        emplrId:
          type: integer
          description: 組合ID
          example: 999999
        emplrCd:
          type: string
          description: 組合コード
          example: emplr-123
        emplrName:
          type: string
          description: 組合名
          example: emplr-123
        isAdmin:
          type: boolean
          description: 権限
          example: false
        comment:
          type: string
          description: 備考
          example: "備考"
        auth0UserId:
          type: string
          description: Auth0のユーザーID
          example: auth0|123456789
        hierarchyLevel:
          type: integer
          description: 階層レベル
          example: 1
        jigyosyoCd:
          type: string
          description: 事業所コード
          example: 23
        isBlocked:
          type: boolean
          description: ブロック状態
          example: false
        isMfa:
          type: boolean
          description: MFA状態
          example: true
        lastLogin:
          type: string
          format: date-time
          description: 最終ログイン日時
          example: '2020-01-31T23:59:59+09:00'
      required:
        - id
        - name
        - email
        - emplrCd
        - emplrName
        - isAdmin
        - comment
        - hierarchyLevel
        - jigyosyoCd
        - auth0UserId
        - isBlocked
        - isMfa
        - lastLogin
    EmplrDetail:
      type: object
      properties:
        emplrId:
          type: integer
          description: 組合ID
          example: 100001
        emplrCd:
          type: string
          description: 組合コード
          example: "EMP"
        customEmplrName:
          type: string
          description: 組合名
          example: "テスト組合"
      required:
        - emplrId
        - emplrCd
        - customEmplrName
    MstEmplrDetail:
      type: object
      properties:
        emplrId:
          type: integer
          description: 組合ID
          example: 100001
        emplrName:
          type: string
          description: 組合名
          example: "テスト組合"
        emplrLabel:
          type: string
          description: 組合ラベル
          example: "テスト組合ラベル"
        rsvPtn:
          type: integer
          description: RSVパターン
          example: 1
        emplrKbn:
          type: integer
          description: 組合区分
          example: 1
        receStYm:
          type: integer
          description: レセプト開始年月
          example: 202401
        receEdYm:
          type: integer
          description: レセプト終了年月
          example: 202412
        memStYm:
          type: integer
          description: メンバー開始年月
          example: 202401
        memEdYm:
          type: integer
          description: メンバー終了年月
          example: 202412
        heStYm:
          type: integer
          description: 健診開始年月
          example: 202401
        heEdYm:
          type: integer
          description: 健診終了年月
          example: 202412
        flaStYm:
          type: integer
          description: FLA開始年月
          example: 202401
        flaEdYm:
          type: integer
          description: FLA終了年月
          example: 202412
        fullStYm:
          type: integer
          description: FULL開始年月
          example: 202401
        fullEdYm:
          type: integer
          description: FULL終了年月
          example: 202412
        memUpdDate:
          type: string
          format: date-time
          description: メンバー更新日時
          example: '2024-01-01T09:00:00+09:00'
        flaUpdDate:
          type: string
          format: date-time
          description: FLA更新日時
          example: '2024-01-01T09:00:00+09:00'
        receUpdDate:
          type: string
          format: date-time
          description: レセプト更新日時
          example: '2024-01-01T09:00:00+09:00'
        deptUseFlg:
          type: integer
          description: 部署使用フラグ
          example: 1
        delFlg:
          type: integer
          description: 削除フラグ
          example: 0
        passwordPattern:
          type: string
          description: パスワードパターン
          example: "pattern123"
        passwordChangeDays:
          type: integer
          description: パスワード変更日数
          example: 90
        lockCount:
          type: integer
          description: ロックカウント
          example: 0
      required:
        - emplrId
        - emplrName
        - emplrLabel
        - rsvPtn
        - emplrKbn
        - receStYm
        - receEdYm
        - memStYm
        - memEdYm
        - heStYm
        - heEdYm
        - flaStYm
        - flaEdYm
        - fullStYm
        - fullEdYm
        - deptUseFlg
        - delFlg
        - passwordPattern
        - passwordChangeDays
        - lockCount
    NoticeInfo:
      type: object
      properties:
        id:
          type: integer
          description: お知らせID
          example: 1
        title:
          type: string
          description: タイトル
          example: "システムメンテナンスのお知らせ"
        comment:
          type: string
          description: コメント
          example: "明日の午前2時よりシステムメンテナンスを実施いたします"
        file_name:
          type: string
          description: ファイル名
          example: "maintenance.pdf"
        release_date:
          type: string
          format: date-time
          description: 公開日時
          example: '2024-01-01T09:00:00+09:00'
        reg_user:
          type: string
          description: 登録ユーザー
          example: "admin"
        reg_date:
          type: string
          format: date-time
          description: 登録日時
          example: '2024-01-01T09:00:00+09:00'
        upd_user:
          type: string
          description: 更新ユーザー
          example: "admin"
        upd_date:
          type: string
          format: date-time
          description: 更新日時
          example: '2024-01-01T09:00:00+09:00'
      required:
        - id
        - title
        - comment
        - file_name
        - release_date
        - reg_user
        - reg_date
        - upd_user
        - upd_date

    NoticeListResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/NoticeInfo'
        total:
          type: integer
          description: 総件数
        page:
          type: integer
          description: 現在のページ
        limit:
          type: integer
          description: 1ページあたりの件数
        total_pages:
          type: integer
          description: 総ページ数
      required:
        - data
        - total
        - page
        - limit
        - total_pages

    MaintenanceInfo:
      type: object
      properties:
        id:
          type: integer
          description: メンテナンス情報ID
          example: 1
        title:
          type: string
          description: タイトル
          example: "システムメンテナンスのお知らせ"
        comment:
          type: string
          description: コメント
          example: "明日の午前2時よりシステムメンテナンスを実施いたします"
        release_date:
          type: string
          format: date-time
          description: 公開日時
          example: '2024-01-01T09:00:00+09:00'
        reg_user:
          type: string
          description: 登録ユーザー
          example: "admin"
        reg_date:
          type: string
          format: date-time
          description: 登録日時
          example: '2024-01-01T09:00:00+09:00'
        upd_user:
          type: string
          description: 更新ユーザー
          example: "admin"
        upd_date:
          type: string
          format: date-time
          description: 更新日時
          example: '2024-01-01T09:00:00+09:00'
      required:
        - id
        - title
        - comment
        - release_date
        - reg_user
        - reg_date
        - upd_user
        - upd_date

    MaintenanceListResponse:
      type: object
      properties:
        maintenance_info:
          type: array
          items:
            $ref: '#/components/schemas/MaintenanceInfo'
        total:
          type: integer
          description: 総件数
        page:
          type: integer
          description: 現在のページ
        limit:
          type: integer
          description: 1ページあたりの件数
        total_pages:
          type: integer
          description: 総ページ数
      required:
        - maintenance_info
        - total
        - page
        - limit
        - total_pages
paths:
  /tableau-jwt:
    get:
      summary: Generate JWT token for Tableau embedding
      operationId: getTableauJwt
      responses:
        '200':
          description: Successfully generated JWT token
          content:
            application/json:
              schema:
                type: object
                required:
                  - token
                properties:
                  token:
                    type: string
                    description: JWT token for Tableau embedding
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                required:
                  - message
                properties:
                  message:
                    type: string
  /tableau/download/pdf:
    post:
      summary: Download PDF from Tableau view
      description: Download PDF file from Tableau Server with optional filters
      operationId: postTableauDownloadPdf
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - viewId
              properties:
                viewId:
                  type: string
                  description: Tableau view name/ID
                  example: "SampleView"
                filters:
                  type: object
                  description: Optional filters to apply to the view
                  additionalProperties: true
                  example:
                    region: "Tokyo"
                    year: 2024
      responses:
        '200':
          description: PDF file downloaded successfully
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
  /tableau/download/csv:
    post:
      summary: Download CSV from Tableau view
      description: Download CSV file from Tableau Server with optional filters
      operationId: postTableauDownloadCsv
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - viewId
              properties:
                viewId:
                  type: string
                  description: Tableau view name/ID
                  example: "SampleView"
                filters:
                  type: object
                  description: Optional filters to apply to the view
                  additionalProperties: true
                  example:
                    region: "Tokyo"
                    year: 2024
      responses:
        '200':
          description: CSV file downloaded successfully
          content:
            text/csv:
              schema:
                type: string
                format: binary
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
  /user-info:
    get:
      summary: Auth0トークンによるユーザー情報取得
      description: Authorization headerのBearer tokenからユーザー情報を取得
      operationId: getApiUserInfo
      security:
        - bearerAuth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDetail'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalServerError'
  /unique-jmdc-brdg-ids:
    post:
      summary: 重複なしJMDC_BRDG_IDリストの取得
      description: SUBSCRIBER_INFOテーブルから一意のJMDC_BRDG_IDのリストを取得する
      security:
        - bearerAuth: []
      operationId: postUniqueJmdcBrdgIds
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                emplr_id:
                  type: array
                  items:
                    type: integer
                  x-go-type-skip-optional-pointer: true
                  description: 組合ID
                  example: [100001, 100002]
                jigyosyo_cd:
                  type: array
                  items:
                    type: string
                  x-go-type-skip-optional-pointer: true
                  description: 事業所コード
                  example: ["1001", "1002"]
                filter_cd:
                  type: array
                  items:
                    type: string
                  x-go-type-skip-optional-pointer: true
                  description: フィルターコード
                  example: ["FC1", "FC2"]
                filter_name:
                  type: array
                  items:
                    type: string
                  x-go-type-skip-optional-pointer: true
                  description: フィルター名
                  example: ["Filter1", "Filter2"]
                filter_type:
                  type: array
                  items:
                    type: integer
                  x-go-type-skip-optional-pointer: true
                  description: フィルタータイプ
                  example: [1, 2]
                aggregation_cd:
                  type: array
                  items:
                    type: integer
                  x-go-type-skip-optional-pointer: true
                  description: 集計コード
                  example: [1, 2]
                fiscal_year:
                  type: array
                  items:
                    type: integer
                  x-go-type-skip-optional-pointer: true
                  description: 会計年度
                  example: [2020, 2021]
                gender_family_kbn:
                  type: array
                  items:
                    type: string
                  x-go-type-skip-optional-pointer: true
                  description: 性別家族区分
                  example: ["M", "F"]
                age:
                  type: array
                  items:
                    type: integer
                  x-go-type-skip-optional-pointer: true
                  description: 年齢
                  example: [30, 40]
                age_groups_increments_5:
                  type: array
                  items:
                    type: string
                  x-go-type-skip-optional-pointer: true
                  description: 年齢グループ（5歳刻み）
                  example: ["30-34", "35-39"]
                age_groups_increments_10:
                  type: array
                  items:
                    type: string
                  x-go-type-skip-optional-pointer: true
                  description: 年齢グループ（10歳刻み）
                  example: ["30-39", "40-49"]
      responses:
        '200':
          description: 重複なしJMDC_BRDG_IDリストを返す
          content:
            application/json:
              schema:
                type: object
                properties:
                  jmdc_brdg_ids:
                    type: array
                    items:
                      type: string
                    x-go-type-skip-optional-pointer: true
                    description: JMDC_BRDG_IDリスト
                    example: ["12345", "67890"]
                  total:
                    type: integer
                    description: リストの総数
                    example: 2
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/InternalServerError"
  /users/level2:
    get:
      summary: Level 2 ユーザー一覧取得
      security:
        - bearerAuth: []
      operationId: getApiLevel2Users
      parameters:
        - in: query
          name: keyword
          schema:
            type: string
          description: Keyword to search for employer code or employer name
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  users:
                    type: array
                    items:
                      $ref: '#/components/schemas/UserDetail'
                  total:
                    type: integer
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalServerError'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalServerError'

  /emplrs:
    get:
      summary: 組合一覧取得
      security:
        - bearerAuth: []
      operationId: getApiEmplrs
      parameters:
        - in: query
          name: keyword
          schema:
            type: string
          description: Keyword to search for employer code or employer name
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  emplrs:
                    type: array
                    items:
                      $ref: '#/components/schemas/EmplrDetail'
                  total:
                    type: integer
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalServerError'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalServerError'
    post:
      summary: 組合情報取得（ID指定）
      description: 指定されたemployIdに基づいてTBL_MST_EMPLRから組合情報を取得
      operationId: postApiEmplrs
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - employId
              properties:
                employId:
                  type: integer
                  description: 組合ID
                  example: 100001
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  emplr:
                    $ref: '#/components/schemas/MstEmplrDetail'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalServerError'
        '404':
          description: Employer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalServerError'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalServerError'
  /notices:
    get:
      summary: お知らせ一覧取得（ページング付き）
      security:
        - bearerAuth: []
      operationId: getApiNotices
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            minimum: 1
            default: 1
          description: ページ番号
        - in: query
          name: limit
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
          description: 1ページあたりの件数
        - in: query
          name: keyword
          schema:
            type: string
          description: 検索キーワード（タイトル、内容で検索）
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NoticeListResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalServerError'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalServerError'
  /notice/{id}:
    get:
      summary: お知らせ詳細取得
      security:
        - bearerAuth: []
      operationId: getApiNoticeById
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: お知らせID
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NoticeInfo'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalServerError'
        '404':
          description: Notice not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalServerError'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalServerError'

  /maintenance-info:
    get:
      summary: メンテナンス情報一覧取得（ページング付き）
      operationId: getApiMaintenanceInfo
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            minimum: 1
            default: 1
          description: ページ番号
        - in: query
          name: limit
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
          description: 1ページあたりの件数
        - in: query
          name: keyword
          schema:
            type: string
          description: 検索キーワード（タイトル、内容で検索）
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenanceListResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalServerError'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalServerError'
  /maintenance-info/{id}:
    get:
      summary: メンテナンス情報詳細取得
      operationId: getApiMaintenanceInfoById
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: メンテナンス情報ID
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenanceInfo'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalServerError'
        '404':
          description: Maintenance info not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalServerError'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternalServerError'
