# Rakuraku Public API

This document provides instructions on how to run the public API for the Rakuraku application using Docker and Make.

## Prerequisites

- Docker installed and running.
- Docker Compose installed.
- Make installed.
- Git installed (for cloning the repository).

## Setup

1.  **Clone the repository** (if you haven't already):
    ```bash
    <NAME_EMAIL>:jmdc-inc/kensuke-plus-server.git
    cd kensuke-plus-server
    ```

2.  **Environment Configuration**:
    Ensure you have a `.env` file in the `public` directory. You can copy the example file if it exists:
    ```bash
    cp public/.env.template public/.env
    ```
    Modify `public/.env` as needed for your local environment.

## Running the Services

The services are designed to be run in two steps: first the database, then the API.
All `make` commands should be run from the `public` directory.

1.  **Start the PostgreSQL Database**:
    This command will start the PostgreSQL database container in detached mode. It uses the configuration from `docker/compose.yml`.
    ```bash
    make run-db
    ```
    The database will be accessible to the API service on the internal Docker network.

2.  **Start the API Application**:
    Once the database is running, you can start the API application. This command will build the API image (if necessary) and start the API container in detached mode. It uses the configuration from `docker/docker-compose.public.yml`.
    ```bash
    make run
    ```
    The API should now be running and connected to the database. By default, it might be accessible on `http://localhost:8000`.

## Viewing Logs

To view the logs for a specific service:

-   **Database (PostgreSQL)**:
    The container name is `postgres_local` (as defined in `docker/compose.yml`).
    ```bash
    docker logs postgres_local
    ```
    Or, if you are in the `docker` directory:
    ```bash
    docker compose logs postgres
    ```

-   **API Application**:
    The container name is `rakuraku_app` (as defined in `docker/docker-compose.public.yml`).
    ```bash
    docker logs rakuraku_app
    ```
    Or, if you are in the `public` directory:
    ```bash
    docker compose -f ../docker/docker-compose.public.yml logs app
    ```

## Building Docker Images for ECS Deployment

To deploy the application to Amazon ECS (Elastic Container Service), you'll need to build and push a Docker image to a container registry.

1.  **Build the Docker Image**:
    Use the production Dockerfile to build an image suitable for ECS deployment:
    ```bash
    docker build -f docker/Dockerfile.public -t rakuraku-public:latest .
    ```

2.  **Tag the Image for ECR** (assuming AWS ECR as the registry):
    Replace `[AWS_ACCOUNT_ID]` and `[AWS_REGION]` with your actual values.
    ```bash
    docker tag rakuraku-public:latest [AWS_ACCOUNT_ID].dkr.ecr.[AWS_REGION].amazonaws.com/rakuraku-public:latest
    ```

3.  **Authenticate to ECR**:
    ```bash
    aws ecr get-login-password --region [AWS_REGION] | docker login --username AWS --password-stdin [AWS_ACCOUNT_ID].dkr.ecr.[AWS_REGION].amazonaws.com
    ```

4.  **Push the Image to ECR**:
    ```bash
    docker push [AWS_ACCOUNT_ID].dkr.ecr.[AWS_REGION].amazonaws.com/rakuraku-public:latest
    ```

5.  **Update ECS Task Definition or Service**:
    After pushing the new image, you need to update your ECS task definition or service to use the new image.
    ```bash
    aws ecs update-service --cluster [CLUSTER_NAME] --service [SERVICE_NAME] --force-new-deployment
    ```

For CI/CD pipelines, these steps can be automated using GitHub Actions, AWS CodeBuild, or other CI/CD tools.

## Stopping the Services

-   **Stop the API Application**:
    If you are in the `public` directory:
    ```bash
    docker compose -f ../docker/docker-compose.public.yml down
    ```
    *Note: The `make down` target currently points to `docker-compose.public.yml`, so it will only bring down the API. You might want to add a separate target or a combined one to bring down the database as well.*

-   **Stop the PostgreSQL Database**:
    If you are in the `docker` directory:
    ```bash
    docker compose down
    ```
    Or from any directory:
    ```bash
    docker compose -f /path/to/your/project/docker/compose.yml down
    ```
