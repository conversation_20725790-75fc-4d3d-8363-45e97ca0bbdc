package handler

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/public"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/middleware"
)

// Emplr list
// (GET /api/emplrs)
func (h *API) GetApiEmplrs(ctx context.Context, request oapi.GetApiEmplrsRequestObject) (oapi.GetApiEmplrsResponseObject, error) {
	authorization := ctx.Value(middleware.AuthorizationKey)
	if authorization == nil {
		message := "Authorization header not found"
		return oapi.GetApiEmplrs401JSONResponse{
			Message: message,
		}, nil
	}

	token, ok := authorization.(string)
	if !ok {
		message := "Invalid authorization header format"
		return oapi.GetApiEmplrs401JSONResponse{
			Message: message,
		}, nil
	}

	userInfo, err := h.userUseCase.GetUserInfoByToken(ctx, token)
	if err != nil {
		message := "Authentication failed: " + err.Error()
		return oapi.GetApiEmplrs401JSONResponse{
			Message: message,
		}, err
	}

	responseObj, ok := userInfo.(oapi.GetApiUserInfo200JSONResponse)
	if !ok {
		message := "Failed to get user info"
		return oapi.GetApiEmplrs401JSONResponse{
			Message: message,
		}, nil
	}

	userDetail := responseObj

	if userDetail.HierarchyLevel != 1 {
		message := "Permission denied: Only admin can access this API"
		return oapi.GetApiEmplrs401JSONResponse{
			Message: message,
		}, nil
	}

	emplrList, err := h.emplrUseCase.GetEmplrs(ctx, request.Params.Keyword)
	if err != nil {
		return emplrList, err
	}

	return emplrList, nil
}

// Get MST Emplr by ID
// (POST /emplr-by-id)
func (h *API) PostApiEmplrById(ctx context.Context, request oapi.PostApiEmplrByIdRequestObject) (oapi.PostApiEmplrByIdResponseObject, error) {
	if request.Body == nil {
		message := "Request body is required"
		return oapi.PostApiEmplrById400JSONResponse{
			Message: message,
		}, nil
	}

	employId := request.Body.EmployId

	emplr, err := h.emplrUseCase.GetMstEmplrByID(ctx, employId)
	if err != nil {
		return emplr, err
	}

	return emplr, nil
}
