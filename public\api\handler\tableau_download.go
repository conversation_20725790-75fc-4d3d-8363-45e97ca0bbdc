package handler

import (
	"bytes"
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/public"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/middleware"
)

// Download PDF from Tableau view
// (POST /api/tableau/download/pdf)
func (h *API) PostTableauDownloadPdf(ctx context.Context, request oapi.PostTableauDownloadPdfRequestObject) (oapi.PostTableauDownloadPdfResponseObject, error) {
	// Check authorization
	authHeader := ctx.Value(middleware.AuthorizationKey)
	if authHeader == nil {
		message := "Unauthorized: No authorization token provided"
		return oapi.PostTableauDownloadPdf401JSONResponse{
			Message: &message,
		}, nil
	}

	// Parse Auth0 token to get user info
	auth0ID, err := h.auth0TokenService.ParseAuth0Token(authHeader.(string))
	if err != nil {
		message := "Unauthorized: Invalid token"
		return oapi.PostTableauDownloadPdf401JSONResponse{
			Message: &message,
		}, nil
	}

	// Validate request body
	if request.Body == nil {
		message := "Request body is required"
		return oapi.PostTableauDownloadPdf400JSONResponse{
			Message: &message,
		}, nil
	}

	if request.Body.ViewId == "" {
		message := "ViewId is required"
		return oapi.PostTableauDownloadPdf400JSONResponse{
			Message: &message,
		}, nil
	}

	// Prepare filters
	var filters map[string]interface{}
	if request.Body.Filters != nil {
		filters = *request.Body.Filters
	}

	// Download PDF from Tableau
	pdfData, err := h.tableauDownloadUseCase.DownloadPDF(ctx, request.Body.ViewId, filters)
	if err != nil {
		h.logger.Error("Failed to download PDF from Tableau", "error", err, "auth0ID", auth0ID, "viewId", request.Body.ViewId)
		message := "Failed to download PDF: " + err.Error()
		return oapi.PostTableauDownloadPdf500JSONResponse{
			Message: &message,
		}, nil
	}

	// Return PDF data as binary response
	return oapi.PostTableauDownloadPdf200ApplicationpdfResponse{
		Body:          bytes.NewReader(pdfData),
		ContentLength: int64(len(pdfData)),
	}, nil
}

// Download CSV from Tableau view
// (POST /api/tableau/download/csv)
func (h *API) PostTableauDownloadCsv(ctx context.Context, request oapi.PostTableauDownloadCsvRequestObject) (oapi.PostTableauDownloadCsvResponseObject, error) {
	// Check authorization
	authHeader := ctx.Value(middleware.AuthorizationKey)
	if authHeader == nil {
		message := "Unauthorized: No authorization token provided"
		return oapi.PostTableauDownloadCsv401JSONResponse{
			Message: &message,
		}, nil
	}

	// Parse Auth0 token to get user info
	auth0ID, err := h.auth0TokenService.ParseAuth0Token(authHeader.(string))
	if err != nil {
		message := "Unauthorized: Invalid token"
		return oapi.PostTableauDownloadCsv401JSONResponse{
			Message: &message,
		}, nil
	}

	// Validate request body
	if request.Body == nil {
		message := "Request body is required"
		return oapi.PostTableauDownloadCsv400JSONResponse{
			Message: &message,
		}, nil
	}

	if request.Body.ViewId == "" {
		message := "ViewId is required"
		return oapi.PostTableauDownloadCsv400JSONResponse{
			Message: &message,
		}, nil
	}

	// Prepare filters
	var filters map[string]interface{}
	if request.Body.Filters != nil {
		filters = *request.Body.Filters
	}

	// Download CSV from Tableau
	csvData, err := h.tableauDownloadUseCase.DownloadCSV(ctx, request.Body.ViewId, filters)
	if err != nil {
		h.logger.Error("Failed to download CSV from Tableau", "error", err, "auth0ID", auth0ID, "viewId", request.Body.ViewId)
		message := "Failed to download CSV: " + err.Error()
		return oapi.PostTableauDownloadCsv500JSONResponse{
			Message: &message,
		}, nil
	}

	// Return CSV data as binary response
	return oapi.PostTableauDownloadCsv200TextcsvResponse{
		Body:          bytes.NewReader(csvData),
		ContentLength: int64(len(csvData)),
	}, nil
}
