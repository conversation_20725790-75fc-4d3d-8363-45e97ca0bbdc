package model

import (
	"fmt"
	"time"

	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
)

type UserDetail struct {
	UserID    int
	Auth0ID   string
	Name      string
	Email     string
	Role      int
	Comment   string
	TableauID string

	IsAdmin   bool
	IsBlocked bool
	IsMFA     bool
	LastLogin time.Time

	EmplrID   int
	EmplrCD   string
	EmplrName string

	HierarchyLevel int
	JigyosyoCD     string
}

func (e *UserDetail) NameWithLabel() string {
	return fmt.Sprintf("%d %s", e.EmplrID, e.Name)
}

func NewUserDetail(row dbmodels.MNGUserDetail) UserDetail {
	return UserDetail{
		UserID:         row.UserID,
		Auth0ID:        row.Auth0ID,
		Name:           row.Name,
		Email:          row.Email,
		Role:           row.Role,
		Comment:        row.Comment,
		EmplrID:        row.EmplrID,
		EmplrCD:        row.EmplrCD,
		HierarchyLevel: row.HierarchyLevel,
		JigyosyoCD:     row.JigyosyoCD,
		EmplrName:      row.CustomEmplrName,
		TableauID:      row.TableauID,
	}
}
