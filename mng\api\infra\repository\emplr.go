// Package repository is implements DB connection.
package repository

import (
	"context"
	"log/slog"
	"path/filepath"

	"github.com/cockroachdb/errors"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/model"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/repository"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/aws/s3"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/csv"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/db"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/errof"
)

type emplr struct {
	dbClient  db.Client
	s3Client  s3.S3Client
	csvWirter csv.CsvClient
}

func NewEmplr(dbClient db.Client, s3Client s3.S3Client, csvWirter csv.CsvClient) repository.EmplrRepository {
	return &emplr{dbClient, s3Client, csvWirter}
}

func (r *emplr) Create(ctx context.Context, model model.Emplr) error {
	var input = dbmodels.MNGEmplr{
		EmplrID:         model.EmplrID,
		CustomEmplrName: model.CustomEmplrName,
		GroupID:         model.GroupID,
	}

	mstEmplr, err := r.getMstEmplr(ctx, model.EmplrID)
	if err != nil {

		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	input.SecondaryUseProperty = mstEmplr.SecondaryUseProperty
	input.EmplrCD = mstEmplr.EmplrCD

	_, err = r.dbClient.DB.NewInsert().Model(&input).Exec(ctx)
	if err != nil {

		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	err = r.uploadCSVFile(ctx)
	if err != nil {
		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	return nil
}

func (r *emplr) getMstEmplr(ctx context.Context, mstEmplrID int) (*dbmodels.MSTEmplr, error) {
	result := dbmodels.MSTEmplr{}

	if err := r.dbClient.DB.NewSelect().Model(&result).Where("emplr_id = ?", mstEmplrID).Scan(ctx); err != nil {

		// エラーが発生しているのでエラー情報を返す
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	return &result, nil
}

func (r *emplr) uploadCSVFile(ctx context.Context) error {
	results := []dbmodels.MNGEmplr{}

	if err := r.dbClient.DB.NewSelect().Model(&results).Where("emplr_id != 999999").Scan(ctx); err != nil {
		return err
	}

	if len(results) == 0 {
		slog.Error("No data found for MNGEmplr")
		return nil
	}
	// resultsを文字列配列に変換する
	var csvData [][]string
	for _, result := range results {
		csvData = append(csvData, result.ToStringArray())
	}
	// tmpに一時ファイルを作成する
	tmpFilePath := filepath.Join("/tmp", "tbl_mng_emplr.csv")

	// CSVファイルを作成する
	err := r.csvWirter.WriteCSVFile(tmpFilePath, results[0].TableHeaders(), csvData)
	if err != nil {
		return err
	}

	// S3にアップロードする
	err = r.s3Client.PutTableFile(ctx, tmpFilePath, "tbl_mng_emplr.csv")
	if err != nil {
		return err
	}
	return nil
}

func (r *emplr) Get(ctx context.Context, id int) (*model.Emplr, error) {
	var result = dbmodels.MNGEmplr{}

	if err := r.dbClient.DB.NewSelect().Model(&result).Where("emplr_id = ?", id).Scan(ctx); err != nil {

		// エラーが発生しているのでエラー情報を返す
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	ret := model.NewEmplr(result)
	return &ret, nil
}

func (r *emplr) GetList(ctx context.Context) ([]model.Emplr, error) {
	var results []dbmodels.MNGEmplr

	if err := r.dbClient.DB.NewSelect().Model(&results).Where("emplr_id != 999999").Scan(ctx); err != nil {

		// エラーが発生しているのでエラー情報を返す
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	return model.NewEmplrs(results), nil
}

func (r *emplr) Update(ctx context.Context, model model.Emplr) error {
	var input = dbmodels.MNGEmplr{
		EmplrID:         model.EmplrID,
		CustomEmplrName: model.CustomEmplrName,
		GroupID:         model.GroupID,
	}
	_, err := r.dbClient.DB.NewUpdate().Model(&input).Where("emplr_id = ?", model.EmplrID).Exec(ctx)
	if err != nil {
		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	err = r.uploadCSVFile(ctx)
	if err != nil {
		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	return nil
}

func (r *emplr) Delete(ctx context.Context, id int) error {
	model := dbmodels.MNGEmplr{
		EmplrID: id,
	}
	_, err := r.dbClient.DB.NewDelete().Model(&model).WherePK().Exec(ctx)
	if err != nil {
		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	err = r.uploadCSVFile(ctx)
	if err != nil {
		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	return nil
}

func (r *emplr) GetMstEmplrList(ctx context.Context) ([]model.MSTEmplr, error) {
	var results []dbmodels.MSTEmplr

	if err := r.dbClient.DB.NewSelect().Model(&results).Scan(ctx); err != nil {

		// エラーが発生しているのでエラー情報を返す
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	return model.NewMSTEmplrs(results), nil
}

func (r *emplr) GetRegistPossibleEmplrList(ctx context.Context) ([]model.MSTEmplr, error) {
	var results []dbmodels.MSTEmplr

	if err := r.dbClient.DB.NewSelect().Model(&results).Where("emplr_id NOT IN (SELECT emplr_id FROM kensuke_plus.tbl_mng_emplr)").Scan(ctx); err != nil {

		// エラーが発生しているのでエラー情報を返す
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	return model.NewMSTEmplrs(results), nil
}
