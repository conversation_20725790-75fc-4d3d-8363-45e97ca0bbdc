services:
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile.public.local
    container_name: rakuraku_app
    ports:
      - "${SERVER_PORT:-8000}:8000"
    env_file:
      - ../public/.env
    volumes:
      - ../:/app
    tty: true
    stdin_open: true
    restart: on-failure
    networks:
      - rakuraku-network

volumes:
  db:

networks:
  rakuraku-network:
    external: true
