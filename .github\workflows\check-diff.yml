name: Check diff

on:
  pull_request:
    types:
      - opened
      - synchronize
      - reopened

permissions:
  contents: read
  actions: read
  pull-requests: read

jobs:
  paths:
    runs-on: ubuntu-latest
    outputs:
      isFileChanged: ${{ steps.changes.outputs.target == 'true' }}
    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Paths filter
        uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            target:
              - "openapi/**/*.yml"

  check-diff:
    needs: [paths]
    runs-on: ubuntu-latest

    # Skip if Draft PR
    if: ${{ github.event.pull_request.draft == false && needs.paths.outputs.isFileChanged == 'true'}}

    steps:
      - name: Check out code
        uses: actions/checkout@v4
      - name: Merge and build yml files
        run: |
          yarn install
          yarn openapi:merge-doc && yarn openapi:build-doc
      - name: Check diff
        run: |
          git diff --quiet || (git diff && exit 1)