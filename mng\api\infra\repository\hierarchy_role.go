// Package repository is implements DB connection.
package repository

import (
	"context"
	"log/slog"
	"path/filepath"

	"github.com/cockroachdb/errors"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/model"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/repository"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/aws/s3"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/csv"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/db"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/errof"
)

type hierarchyRole struct {
	dbClient  db.Client
	s3Client  s3.S3Client
	csvWirter csv.CsvClient
}

func NewHierarchyRole(dbClient db.Client, s3Client s3.S3Client, csvWirter csv.CsvClient) repository.HierarchyRoleRepository {
	return &hierarchyRole{dbClient, s3Client, csvWirter}
}

func (r *hierarchyRole) Create(ctx context.Context, model model.HierarchyRole) error {
	var input = dbmodels.MNGHierarchyRole{
		EmplrID:        model.EmplrID,
		HierarchyLevel: model.HierarchyLevel,
		JigyosyoCD:     model.JigyosyoCD,
	}

	_, err := r.dbClient.DB.NewInsert().Model(&input).Exec(ctx)
	if err != nil {

		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	err = r.uploadCSVFile(ctx)
	if err != nil {
		// エラーが発生しているのでエラー情報を返す
		return err
	}
	return nil
}

func (r *hierarchyRole) uploadCSVFile(ctx context.Context) error {
	var results = []dbmodels.MNGHierarchyRole{}

	if err := r.dbClient.DB.NewSelect().Model(&results).Where("emplr_id != 999999").Scan(ctx); err != nil {
		return err
	}

	if len(results) == 0 {
		slog.Error("No data found for MNGHierarchyRole")
		return nil
	}
	// resultsを文字列配列に変換する
	var csvData [][]string
	for _, result := range results {
		csvData = append(csvData, result.ToStringArray())
	}
	// tmpに一時ファイルを作成する
	tmpFilePath := filepath.Join("/tmp", "tbl_mng_hierarchy_role.csv")

	// CSVファイルを作成する
	err := r.csvWirter.WriteCSVFile(tmpFilePath, results[0].TableHeaders(), csvData)
	if err != nil {
		return err
	}

	// S3にアップロードする
	err = r.s3Client.PutTableFile(ctx, tmpFilePath, "tbl_mng_hierarchy_role.csv")
	if err != nil {
		return err
	}
	return nil
}

func (r *hierarchyRole) Get(ctx context.Context, id int) (*model.HierarchyRole, error) {
	var result = dbmodels.MNGHierarchyRole{}

	if err := r.dbClient.DB.NewSelect().Model(&result).Where("id = ?", id).Scan(ctx); err != nil {

		// エラーが発生しているのでエラー情報を返す
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	ret := model.NewHierarchyRole(result)
	return &ret, nil
}

func (r *hierarchyRole) GetByEmplrId(ctx context.Context, emplrId int, hierarchyLevel int) (*model.HierarchyRole, error) {
	var result = dbmodels.MNGHierarchyRole{}

	if err := r.dbClient.DB.NewSelect().Model(&result).Where("emplr_id = ?", emplrId).Where("hierarchy_level = ?", hierarchyLevel).Scan(ctx); err != nil {

		// エラーが発生しているのでエラー情報を返す
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	ret := model.NewHierarchyRole(result)
	return &ret, nil
}

func (r *hierarchyRole) GetList(ctx context.Context, emplrId int) ([]model.HierarchyRole, error) {
	var results []dbmodels.MNGHierarchyRole

	if err := r.dbClient.DB.NewSelect().Model(&results).Where("emplr_id = ?", emplrId).Scan(ctx); err != nil {

		// エラーが発生しているのでエラー情報を返す
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	return model.NewHierarchyRoles(results), nil
}
func (r *hierarchyRole) GetAllList(ctx context.Context) ([]model.HierarchyRole, error) {
	var results []dbmodels.MNGHierarchyRole

	if err := r.dbClient.DB.NewSelect().Model(&results).Where("emplr_id != 999999").Scan(ctx); err != nil {

		// エラーが発生しているのでエラー情報を返す
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	return model.NewHierarchyRoles(results), nil
}

func (r *hierarchyRole) GetJigyousyoCDList(ctx context.Context, emplrId int) ([]model.JigyosyoCD, error) {
	var results []dbmodels.MSTJigyosyo
	if err := r.dbClient.DB.NewSelect().Model(&results).Where("emplr_id = ?", emplrId).Where("close_flg = ?", 0).Order("order_num ASC").Scan(ctx); err != nil {
		// エラーが発生しているのでエラー情報を返す
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	return model.NewJigyosyoCDList(results), nil
}

func (r *hierarchyRole) Delete(ctx context.Context, id int) error {
	model := dbmodels.MNGHierarchyRole{
		// ID: id,
	}
	_, err := r.dbClient.DB.NewDelete().Model(&model).WherePK().Exec(ctx)
	if err != nil {
		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	return nil
}
