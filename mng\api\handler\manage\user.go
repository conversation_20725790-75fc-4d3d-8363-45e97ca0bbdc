package handler

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
)

// 組合作成
// (PUT /User)
func (h *API) PutUser(ctx context.Context, request oapi.PutUserRequestObject) (oapi.PutUserResponseObject, error) {

	var description = ""
	if request.Body.Comment != nil {
		description = *request.Body.Comment
	}
	// Auth0にユーザーを作成
	auth0Id, err := h.authClient.CreateUser(ctx, request.Body.Name, string(request.Body.Email), description)
	if err != nil || auth0Id == nil {
		return oapi.PutUser500JSONResponse{
			Message: err.Error(),
		}, err
	}

	// ユーザー情報をDBに登録
	err = h.userUseCase.PutUser(ctx, oapi.PutUserJSONRequestBody(*request.Body), *auth0Id)
	if err != nil {
		return oapi.PutUser500JSONResponse{
			Message: err.Error(),
		}, err
	}
	return oapi.PutUser204Response{}, nil
}

// 組合取得
// (GET /User)
func (h *API) GetUser(ctx context.Context, request oapi.GetUserRequestObject) (oapi.GetUserResponseObject, error) {
	user, err := h.userUseCase.GetUser(ctx, request.Params.Id)
	if err != nil {
		return oapi.GetUser500JSONResponse{
			Message: err.Error(),
		}, err
	}
	return user, nil
}

// 組合更新
// (PATCH /User)
func (h *API) PatchUser(ctx context.Context, request oapi.PatchUserRequestObject) (oapi.PatchUserResponseObject, error) {
	return oapi.PatchUser204Response{}, nil
}

// (GET /User-list)
func (h *API) GetUserList(ctx context.Context, request oapi.GetUserListRequestObject) (oapi.GetUserListResponseObject, error) {
	userList, err := h.userUseCase.GetListUser(ctx)
	if err != nil {
		return oapi.GetUserList500JSONResponse{
			Message: err.Error(),
		}, err
	}
	return userList, nil
}

func (h *API) DeleteUser(ctx context.Context, request oapi.DeleteUserRequestObject) (oapi.DeleteUserResponseObject, error) {
	err := h.userUseCase.DeleteUser(ctx, request.Params.Id)
	if err != nil {
		return oapi.DeleteUser500JSONResponse{
			Message: err.Error(),
		}, err
	}
	return oapi.DeleteUser204Response{}, nil
}
