package service

import (
	"errors"
	"strings"

	"github.com/golang-jwt/jwt/v5"
)

type Auth0TokenService interface {
	ParseAuth0Token(tokenString string) (string, error)
}

type auth0TokenService struct {
	// Add config fields if needed
}

func NewAuth0TokenService() Auth0TokenService {
	return &auth0TokenService{}
}

func (s *auth0TokenService) ParseAuth0Token(tokenString string) (string, error) {
	tokenString = strings.TrimPrefix(tokenString, "Bearer ")

	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
	if err != nil {
		return "", err
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return "", errors.New("invalid token claims")
	}

	sub, ok := claims["sub"].(string)
	if !ok {
		return "", errors.New("sub claim not found or not a string")
	}

	return sub, nil
}
