// Package repository is DB connection.
package repository

import (
	"context"

	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/model"
)

type HierarchyRoleRepository interface {
	Create(context.Context, model.HierarchyRole) error
	Get(context.Context, int) (*model.HierarchyRole, error)
	GetByEmplrId(context.Context, int, int) (*model.HierarchyRole, error)
	GetList(context.Context, int) ([]model.HierarchyRole, error)
	GetJigyousyoCDList(context.Context, int) ([]model.JigyosyoCD, error)
	GetAllList(context.Context) ([]model.HierarchyRole, error)
	Delete(context.Context, int) error
}
