<!DOCTYPE html>
<html>

<head>
  <meta charset="utf8" />
  <title>健助+管理API</title>
  <!-- needed for adaptive design -->
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    body {
      padding: 0;
      margin: 0;
    }
  </style>
  <script src="https://cdn.redocly.com/redoc/v2.2.0/bundles/redoc.standalone.js"></script><style data-styled="true" data-styled-version="6.1.14">.kqlrlV{width:calc(100% - 40%);padding:0 40px;}/*!sc*/
@media print,screen and (max-width: 75rem){.kqlrlV{width:100%;padding:40px 40px;}}/*!sc*/
data-styled.g4[id="sc-gaZyOd"]{content:"kqlrlV,"}/*!sc*/
.dTJVET{padding:40px 0;}/*!sc*/
.dTJVET:last-child{min-height:calc(100vh + 1px);}/*!sc*/
.dTJVET>.dTJVET:last-child{min-height:initial;}/*!sc*/
@media print,screen and (max-width: 75rem){.dTJVET{padding:0;}}/*!sc*/
.iYfDfc{padding:40px 0;position:relative;}/*!sc*/
.iYfDfc:last-child{min-height:calc(100vh + 1px);}/*!sc*/
.iYfDfc>.iYfDfc:last-child{min-height:initial;}/*!sc*/
@media print,screen and (max-width: 75rem){.iYfDfc{padding:0;}}/*!sc*/
.iYfDfc:not(:last-of-type):after{position:absolute;bottom:0;width:100%;display:block;content:'';border-bottom:1px solid rgba(0, 0, 0, 0.2);}/*!sc*/
data-styled.g5[id="sc-dDvxFM"]{content:"dTJVET,iYfDfc,"}/*!sc*/
.jFuLzS{width:40%;color:#ffffff;background-color:#263238;padding:0 40px;}/*!sc*/
@media print,screen and (max-width: 75rem){.jFuLzS{width:100%;padding:40px 40px;}}/*!sc*/
data-styled.g6[id="sc-jqVXSH"]{content:"jFuLzS,"}/*!sc*/
.eYokdz{background-color:#263238;}/*!sc*/
data-styled.g7[id="sc-gTrWKq"]{content:"eYokdz,"}/*!sc*/
.eXZVyu{display:flex;width:100%;padding:0;}/*!sc*/
@media print,screen and (max-width: 75rem){.eXZVyu{flex-direction:column;}}/*!sc*/
data-styled.g8[id="sc-kuACkN"]{content:"eXZVyu,"}/*!sc*/
.gJBEDr{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.85714em;line-height:1.6em;color:#333333;}/*!sc*/
data-styled.g9[id="sc-hWWBcw"]{content:"gJBEDr,"}/*!sc*/
.hKnwYv{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.57143em;line-height:1.6em;color:#333333;margin:0 0 20px;}/*!sc*/
data-styled.g10[id="sc-jHbxoU"]{content:"hKnwYv,"}/*!sc*/
.dKfWLZ{color:#ffffff;}/*!sc*/
data-styled.g12[id="sc-jZCRgm"]{content:"dKfWLZ,"}/*!sc*/
.fjywuK{border-bottom:1px solid rgba(38, 50, 56, 0.3);margin:1em 0 1em 0;color:rgba(38, 50, 56, 0.5);font-weight:normal;text-transform:uppercase;font-size:0.929em;line-height:20px;}/*!sc*/
data-styled.g13[id="sc-cZztgT"]{content:"fjywuK,"}/*!sc*/
.ENkuP{cursor:pointer;margin-left:-20px;padding:0;line-height:1;width:20px;display:inline-block;outline:0;}/*!sc*/
.ENkuP:before{content:'';width:15px;height:15px;background-size:contain;background-image:url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMCIgeT0iMCIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA1MTIgNTEyIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBmaWxsPSIjMDEwMTAxIiBkPSJNNDU5LjcgMjMzLjRsLTkwLjUgOTAuNWMtNTAgNTAtMTMxIDUwLTE4MSAwIC03LjktNy44LTE0LTE2LjctMTkuNC0yNS44bDQyLjEtNDIuMWMyLTIgNC41LTMuMiA2LjgtNC41IDIuOSA5LjkgOCAxOS4zIDE1LjggMjcuMiAyNSAyNSA2NS42IDI0LjkgOTAuNSAwbDkwLjUtOTAuNWMyNS0yNSAyNS02NS42IDAtOTAuNSAtMjQuOS0yNS02NS41LTI1LTkwLjUgMGwtMzIuMiAzMi4yYy0yNi4xLTEwLjItNTQuMi0xMi45LTgxLjYtOC45bDY4LjYtNjguNmM1MC01MCAxMzEtNTAgMTgxIDBDNTA5LjYgMTAyLjMgNTA5LjYgMTgzLjQgNDU5LjcgMjMzLjR6TTIyMC4zIDM4Mi4ybC0zMi4yIDMyLjJjLTI1IDI0LjktNjUuNiAyNC45LTkwLjUgMCAtMjUtMjUtMjUtNjUuNiAwLTkwLjVsOTAuNS05MC41YzI1LTI1IDY1LjUtMjUgOTAuNSAwIDcuOCA3LjggMTIuOSAxNy4yIDE1LjggMjcuMSAyLjQtMS40IDQuOC0yLjUgNi44LTQuNWw0Mi4xLTQyYy01LjQtOS4yLTExLjYtMTgtMTkuNC0yNS44IC01MC01MC0xMzEtNTAtMTgxIDBsLTkwLjUgOTAuNWMtNTAgNTAtNTAgMTMxIDAgMTgxIDUwIDUwIDEzMSA1MCAxODEgMGw2OC42LTY4LjZDMjc0LjYgMzk1LjEgMjQ2LjQgMzkyLjMgMjIwLjMgMzgyLjJ6Ii8+PC9zdmc+Cg==');opacity:0.5;visibility:hidden;display:inline-block;vertical-align:middle;}/*!sc*/
h1:hover>.ENkuP::before,h2:hover>.ENkuP::before,.ENkuP:hover::before{visibility:visible;}/*!sc*/
data-styled.g14[id="sc-jMLmsk"]{content:"ENkuP,"}/*!sc*/
.fIBTiR{height:1.5em;width:1.5em;min-width:1.5em;vertical-align:middle;float:left;transition:transform 0.2s ease-out;transform:rotateZ(-90deg);}/*!sc*/
.fIBTiR polygon{fill:#1d8127;}/*!sc*/
.kzTRIF{height:1.5em;width:1.5em;min-width:1.5em;vertical-align:middle;float:left;transition:transform 0.2s ease-out;transform:rotateZ(-90deg);}/*!sc*/
.kzTRIF polygon{fill:#d41f1c;}/*!sc*/
.hGIvNR{height:20px;width:20px;min-width:20px;vertical-align:middle;float:right;transition:transform 0.2s ease-out;transform:rotateZ(0);}/*!sc*/
.hGIvNR polygon{fill:white;}/*!sc*/
data-styled.g15[id="sc-cMHOsR"]{content:"fIBTiR,kzTRIF,hGIvNR,"}/*!sc*/
.kjmvfQ{border-left:1px solid #7c7cbb;box-sizing:border-box;position:relative;padding:10px 10px 10px 0;}/*!sc*/
@media screen and (max-width: 50rem){.kjmvfQ{display:block;overflow:hidden;}}/*!sc*/
tr:first-of-type>.kjmvfQ,tr.last>.kjmvfQ{border-left-width:0;background-position:top left;background-repeat:no-repeat;background-size:1px 100%;}/*!sc*/
tr:first-of-type>.kjmvfQ{background-image:linear-gradient(
      to bottom,
      transparent 0%,
      transparent 22px,
      #7c7cbb 22px,
      #7c7cbb 100%
    );}/*!sc*/
tr.last>.kjmvfQ{background-image:linear-gradient(
      to bottom,
      #7c7cbb 0%,
      #7c7cbb 22px,
      transparent 22px,
      transparent 100%
    );}/*!sc*/
tr.last+tr>.kjmvfQ{border-left-color:transparent;}/*!sc*/
tr.last:first-child>.kjmvfQ{background:none;border-left-color:transparent;}/*!sc*/
data-styled.g18[id="sc-ldgYGE"]{content:"kjmvfQ,"}/*!sc*/
.gQnAKZ{vertical-align:top;line-height:20px;white-space:nowrap;font-size:13px;font-family:Courier,monospace;}/*!sc*/
.gQnAKZ.deprecated{text-decoration:line-through;color:#707070;}/*!sc*/
data-styled.g20[id="sc-efgocT"]{content:"gQnAKZ,"}/*!sc*/
.iOAozH{border-bottom:1px solid #9fb4be;padding:10px 0;width:75%;box-sizing:border-box;}/*!sc*/
tr.expanded .iOAozH{border-bottom:none;}/*!sc*/
@media screen and (max-width: 50rem){.iOAozH{padding:0 20px;border-bottom:none;border-left:1px solid #7c7cbb;}tr.last>.iOAozH{border-left:none;}}/*!sc*/
data-styled.g21[id="sc-ffiKcS"]{content:"iOAozH,"}/*!sc*/
.bRicla{color:#7c7cbb;font-family:Courier,monospace;margin-right:10px;}/*!sc*/
.bRicla::before{content:'';display:inline-block;vertical-align:middle;width:10px;height:1px;background:#7c7cbb;}/*!sc*/
.bRicla::after{content:'';display:inline-block;vertical-align:middle;width:1px;background:#7c7cbb;height:7px;}/*!sc*/
data-styled.g22[id="sc-gztard"]{content:"bRicla,"}/*!sc*/
.AJjSv{border-collapse:separate;border-radius:3px;font-size:14px;border-spacing:0;width:100%;}/*!sc*/
.AJjSv >tr{vertical-align:middle;}/*!sc*/
@media screen and (max-width: 50rem){.AJjSv{display:block;}.AJjSv >tr,.AJjSv >tbody>tr{display:block;}}/*!sc*/
@media screen and (max-width: 50rem) and (-ms-high-contrast:none){.AJjSv td{float:left;width:100%;}}/*!sc*/
.AJjSv .sc-hzvwrc,.AJjSv .sc-hzvwrc .sc-hzvwrc .sc-hzvwrc,.AJjSv .sc-hzvwrc .sc-hzvwrc .sc-hzvwrc .sc-hzvwrc .sc-hzvwrc{margin:1em;margin-right:0;background:#fafafa;}/*!sc*/
.AJjSv .sc-hzvwrc .sc-hzvwrc,.AJjSv .sc-hzvwrc .sc-hzvwrc .sc-hzvwrc .sc-hzvwrc,.AJjSv .sc-hzvwrc .sc-hzvwrc .sc-hzvwrc .sc-hzvwrc .sc-hzvwrc .sc-hzvwrc{background:#ffffff;}/*!sc*/
data-styled.g24[id="sc-ekQdgj"]{content:"AJjSv,"}/*!sc*/
.eKwgKS >ul{list-style:none;padding:0;margin:0;margin:0 -5px;}/*!sc*/
.eKwgKS >ul >li{padding:5px 10px;display:inline-block;background-color:#11171a;border-bottom:1px solid rgba(0, 0, 0, 0.5);cursor:pointer;text-align:center;outline:none;color:#ccc;margin:0 5px 5px 5px;border:1px solid #07090b;border-radius:5px;min-width:60px;font-size:0.9em;font-weight:bold;}/*!sc*/
.eKwgKS >ul >li.react-tabs__tab--selected{color:#333333;background:#ffffff;}/*!sc*/
.eKwgKS >ul >li.react-tabs__tab--selected:focus{outline:auto;}/*!sc*/
.eKwgKS >ul >li:only-child{flex:none;min-width:100px;}/*!sc*/
.eKwgKS >ul >li.tab-success{color:#1d8127;}/*!sc*/
.eKwgKS >ul >li.tab-redirect{color:#ffa500;}/*!sc*/
.eKwgKS >ul >li.tab-info{color:#87ceeb;}/*!sc*/
.eKwgKS >ul >li.tab-error{color:#d41f1c;}/*!sc*/
.eKwgKS >.react-tabs__tab-panel{background:#11171a;}/*!sc*/
.eKwgKS >.react-tabs__tab-panel>div,.eKwgKS >.react-tabs__tab-panel>pre{padding:20px;margin:0;}/*!sc*/
.eKwgKS >.react-tabs__tab-panel>div>pre{padding:0;}/*!sc*/
data-styled.g30[id="sc-bHCmUC"]{content:"eKwgKS,"}/*!sc*/
.fhLETC code[class*='language-'],.fhLETC pre[class*='language-']{text-shadow:0 -0.1em 0.2em black;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.5;-moz-tab-size:4;-o-tab-size:4;tab-size:4;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none;}/*!sc*/
@media print{.fhLETC code[class*='language-'],.fhLETC pre[class*='language-']{text-shadow:none;}}/*!sc*/
.fhLETC pre[class*='language-']{padding:1em;margin:0.5em 0;overflow:auto;}/*!sc*/
.fhLETC .token.comment,.fhLETC .token.prolog,.fhLETC .token.doctype,.fhLETC .token.cdata{color:hsl(30, 20%, 50%);}/*!sc*/
.fhLETC .token.punctuation{opacity:0.7;}/*!sc*/
.fhLETC .namespace{opacity:0.7;}/*!sc*/
.fhLETC .token.property,.fhLETC .token.tag,.fhLETC .token.number,.fhLETC .token.constant,.fhLETC .token.symbol{color:#4a8bb3;}/*!sc*/
.fhLETC .token.boolean{color:#e64441;}/*!sc*/
.fhLETC .token.selector,.fhLETC .token.attr-name,.fhLETC .token.string,.fhLETC .token.char,.fhLETC .token.builtin,.fhLETC .token.inserted{color:#a0fbaa;}/*!sc*/
.fhLETC .token.selector+a,.fhLETC .token.attr-name+a,.fhLETC .token.string+a,.fhLETC .token.char+a,.fhLETC .token.builtin+a,.fhLETC .token.inserted+a,.fhLETC .token.selector+a:visited,.fhLETC .token.attr-name+a:visited,.fhLETC .token.string+a:visited,.fhLETC .token.char+a:visited,.fhLETC .token.builtin+a:visited,.fhLETC .token.inserted+a:visited{color:#4ed2ba;text-decoration:underline;}/*!sc*/
.fhLETC .token.property.string{color:white;}/*!sc*/
.fhLETC .token.operator,.fhLETC .token.entity,.fhLETC .token.url,.fhLETC .token.variable{color:hsl(40, 90%, 60%);}/*!sc*/
.fhLETC .token.atrule,.fhLETC .token.attr-value,.fhLETC .token.keyword{color:hsl(350, 40%, 70%);}/*!sc*/
.fhLETC .token.regex,.fhLETC .token.important{color:#e90;}/*!sc*/
.fhLETC .token.important,.fhLETC .token.bold{font-weight:bold;}/*!sc*/
.fhLETC .token.italic{font-style:italic;}/*!sc*/
.fhLETC .token.entity{cursor:help;}/*!sc*/
.fhLETC .token.deleted{color:red;}/*!sc*/
data-styled.g32[id="sc-dODueM"]{content:"fhLETC,"}/*!sc*/
.jsYwEy{opacity:0.7;transition:opacity 0.3s ease;text-align:right;}/*!sc*/
.jsYwEy:focus-within{opacity:1;}/*!sc*/
.jsYwEy >button{background-color:transparent;border:0;color:inherit;padding:2px 10px;font-family:Roboto,sans-serif;font-size:14px;line-height:1.5em;cursor:pointer;outline:0;}/*!sc*/
.jsYwEy >button :hover,.jsYwEy >button :focus{background:rgba(255, 255, 255, 0.1);}/*!sc*/
data-styled.g33[id="sc-kYYDBl"]{content:"jsYwEy,"}/*!sc*/
.cqBLnv{position:relative;}/*!sc*/
data-styled.g37[id="sc-dUnjic"]{content:"cqBLnv,"}/*!sc*/
.cIfRxr{margin-left:10px;text-transform:none;font-size:0.929em;color:black;}/*!sc*/
data-styled.g41[id="sc-dHHKhh"]{content:"cIfRxr,"}/*!sc*/
.hIcgHh{font-family:Roboto,sans-serif;font-weight:400;line-height:1.5em;}/*!sc*/
.hIcgHh p:last-child{margin-bottom:0;}/*!sc*/
.hIcgHh h1{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.85714em;line-height:1.6em;color:#32329f;margin-top:0;}/*!sc*/
.hIcgHh h2{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.57143em;line-height:1.6em;color:#333333;}/*!sc*/
.hIcgHh code{color:#e53935;background-color:rgba(38, 50, 56, 0.05);font-family:Courier,monospace;border-radius:2px;border:1px solid rgba(38, 50, 56, 0.1);padding:0 5px;font-size:13px;font-weight:400;word-break:break-word;}/*!sc*/
.hIcgHh pre{font-family:Courier,monospace;white-space:pre;background-color:#11171a;color:white;padding:20px;overflow-x:auto;line-height:normal;border-radius:0;border:1px solid rgba(38, 50, 56, 0.1);}/*!sc*/
.hIcgHh pre code{background-color:transparent;color:white;padding:0;}/*!sc*/
.hIcgHh pre code:before,.hIcgHh pre code:after{content:none;}/*!sc*/
.hIcgHh blockquote{margin:0;margin-bottom:1em;padding:0 15px;color:#777;border-left:4px solid #ddd;}/*!sc*/
.hIcgHh img{max-width:100%;box-sizing:content-box;}/*!sc*/
.hIcgHh ul,.hIcgHh ol{padding-left:2em;margin:0;margin-bottom:1em;}/*!sc*/
.hIcgHh ul ul,.hIcgHh ol ul,.hIcgHh ul ol,.hIcgHh ol ol{margin-bottom:0;margin-top:0;}/*!sc*/
.hIcgHh table{display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all;border-collapse:collapse;border-spacing:0;margin-top:1.5em;margin-bottom:1.5em;}/*!sc*/
.hIcgHh table tr{background-color:#fff;border-top:1px solid #ccc;}/*!sc*/
.hIcgHh table tr:nth-child(2n){background-color:#fafafa;}/*!sc*/
.hIcgHh table th,.hIcgHh table td{padding:6px 13px;border:1px solid #ddd;}/*!sc*/
.hIcgHh table th{text-align:left;font-weight:bold;}/*!sc*/
.hIcgHh .share-link{cursor:pointer;margin-left:-20px;padding:0;line-height:1;width:20px;display:inline-block;outline:0;}/*!sc*/
.hIcgHh .share-link:before{content:'';width:15px;height:15px;background-size:contain;background-image:url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMCIgeT0iMCIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA1MTIgNTEyIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBmaWxsPSIjMDEwMTAxIiBkPSJNNDU5LjcgMjMzLjRsLTkwLjUgOTAuNWMtNTAgNTAtMTMxIDUwLTE4MSAwIC03LjktNy44LTE0LTE2LjctMTkuNC0yNS44bDQyLjEtNDIuMWMyLTIgNC41LTMuMiA2LjgtNC41IDIuOSA5LjkgOCAxOS4zIDE1LjggMjcuMiAyNSAyNSA2NS42IDI0LjkgOTAuNSAwbDkwLjUtOTAuNWMyNS0yNSAyNS02NS42IDAtOTAuNSAtMjQuOS0yNS02NS41LTI1LTkwLjUgMGwtMzIuMiAzMi4yYy0yNi4xLTEwLjItNTQuMi0xMi45LTgxLjYtOC45bDY4LjYtNjguNmM1MC01MCAxMzEtNTAgMTgxIDBDNTA5LjYgMTAyLjMgNTA5LjYgMTgzLjQgNDU5LjcgMjMzLjR6TTIyMC4zIDM4Mi4ybC0zMi4yIDMyLjJjLTI1IDI0LjktNjUuNiAyNC45LTkwLjUgMCAtMjUtMjUtMjUtNjUuNiAwLTkwLjVsOTAuNS05MC41YzI1LTI1IDY1LjUtMjUgOTAuNSAwIDcuOCA3LjggMTIuOSAxNy4yIDE1LjggMjcuMSAyLjQtMS40IDQuOC0yLjUgNi44LTQuNWw0Mi4xLTQyYy01LjQtOS4yLTExLjYtMTgtMTkuNC0yNS44IC01MC01MC0xMzEtNTAtMTgxIDBsLTkwLjUgOTAuNWMtNTAgNTAtNTAgMTMxIDAgMTgxIDUwIDUwIDEzMSA1MCAxODEgMGw2OC42LTY4LjZDMjc0LjYgMzk1LjEgMjQ2LjQgMzkyLjMgMjIwLjMgMzgyLjJ6Ii8+PC9zdmc+Cg==');opacity:0.5;visibility:hidden;display:inline-block;vertical-align:middle;}/*!sc*/
.hIcgHh h1:hover>.share-link::before,.hIcgHh h2:hover>.share-link::before,.hIcgHh .share-link:hover::before{visibility:visible;}/*!sc*/
.hIcgHh a{text-decoration:auto;color:#32329f;}/*!sc*/
.hIcgHh a:visited{color:#32329f;}/*!sc*/
.hIcgHh a:hover{color:#6868cf;text-decoration:auto;}/*!sc*/
.jlTrDX{font-family:Roboto,sans-serif;font-weight:400;line-height:1.5em;}/*!sc*/
.jlTrDX p:last-child{margin-bottom:0;}/*!sc*/
.jlTrDX p:first-child{margin-top:0;}/*!sc*/
.jlTrDX p:last-child{margin-bottom:0;}/*!sc*/
.jlTrDX h1{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.85714em;line-height:1.6em;color:#32329f;margin-top:0;}/*!sc*/
.jlTrDX h2{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.57143em;line-height:1.6em;color:#333333;}/*!sc*/
.jlTrDX code{color:#e53935;background-color:rgba(38, 50, 56, 0.05);font-family:Courier,monospace;border-radius:2px;border:1px solid rgba(38, 50, 56, 0.1);padding:0 5px;font-size:13px;font-weight:400;word-break:break-word;}/*!sc*/
.jlTrDX pre{font-family:Courier,monospace;white-space:pre;background-color:#11171a;color:white;padding:20px;overflow-x:auto;line-height:normal;border-radius:0;border:1px solid rgba(38, 50, 56, 0.1);}/*!sc*/
.jlTrDX pre code{background-color:transparent;color:white;padding:0;}/*!sc*/
.jlTrDX pre code:before,.jlTrDX pre code:after{content:none;}/*!sc*/
.jlTrDX blockquote{margin:0;margin-bottom:1em;padding:0 15px;color:#777;border-left:4px solid #ddd;}/*!sc*/
.jlTrDX img{max-width:100%;box-sizing:content-box;}/*!sc*/
.jlTrDX ul,.jlTrDX ol{padding-left:2em;margin:0;margin-bottom:1em;}/*!sc*/
.jlTrDX ul ul,.jlTrDX ol ul,.jlTrDX ul ol,.jlTrDX ol ol{margin-bottom:0;margin-top:0;}/*!sc*/
.jlTrDX table{display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all;border-collapse:collapse;border-spacing:0;margin-top:1.5em;margin-bottom:1.5em;}/*!sc*/
.jlTrDX table tr{background-color:#fff;border-top:1px solid #ccc;}/*!sc*/
.jlTrDX table tr:nth-child(2n){background-color:#fafafa;}/*!sc*/
.jlTrDX table th,.jlTrDX table td{padding:6px 13px;border:1px solid #ddd;}/*!sc*/
.jlTrDX table th{text-align:left;font-weight:bold;}/*!sc*/
.jlTrDX .share-link{cursor:pointer;margin-left:-20px;padding:0;line-height:1;width:20px;display:inline-block;outline:0;}/*!sc*/
.jlTrDX .share-link:before{content:'';width:15px;height:15px;background-size:contain;background-image:url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMCIgeT0iMCIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA1MTIgNTEyIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBmaWxsPSIjMDEwMTAxIiBkPSJNNDU5LjcgMjMzLjRsLTkwLjUgOTAuNWMtNTAgNTAtMTMxIDUwLTE4MSAwIC03LjktNy44LTE0LTE2LjctMTkuNC0yNS44bDQyLjEtNDIuMWMyLTIgNC41LTMuMiA2LjgtNC41IDIuOSA5LjkgOCAxOS4zIDE1LjggMjcuMiAyNSAyNSA2NS42IDI0LjkgOTAuNSAwbDkwLjUtOTAuNWMyNS0yNSAyNS02NS42IDAtOTAuNSAtMjQuOS0yNS02NS41LTI1LTkwLjUgMGwtMzIuMiAzMi4yYy0yNi4xLTEwLjItNTQuMi0xMi45LTgxLjYtOC45bDY4LjYtNjguNmM1MC01MCAxMzEtNTAgMTgxIDBDNTA5LjYgMTAyLjMgNTA5LjYgMTgzLjQgNDU5LjcgMjMzLjR6TTIyMC4zIDM4Mi4ybC0zMi4yIDMyLjJjLTI1IDI0LjktNjUuNiAyNC45LTkwLjUgMCAtMjUtMjUtMjUtNjUuNiAwLTkwLjVsOTAuNS05MC41YzI1LTI1IDY1LjUtMjUgOTAuNSAwIDcuOCA3LjggMTIuOSAxNy4yIDE1LjggMjcuMSAyLjQtMS40IDQuOC0yLjUgNi44LTQuNWw0Mi4xLTQyYy01LjQtOS4yLTExLjYtMTgtMTkuNC0yNS44IC01MC01MC0xMzEtNTAtMTgxIDBsLTkwLjUgOTAuNWMtNTAgNTAtNTAgMTMxIDAgMTgxIDUwIDUwIDEzMSA1MCAxODEgMGw2OC42LTY4LjZDMjc0LjYgMzk1LjEgMjQ2LjQgMzkyLjMgMjIwLjMgMzgyLjJ6Ii8+PC9zdmc+Cg==');opacity:0.5;visibility:hidden;display:inline-block;vertical-align:middle;}/*!sc*/
.jlTrDX h1:hover>.share-link::before,.jlTrDX h2:hover>.share-link::before,.jlTrDX .share-link:hover::before{visibility:visible;}/*!sc*/
.jlTrDX a{text-decoration:auto;color:#32329f;}/*!sc*/
.jlTrDX a:visited{color:#32329f;}/*!sc*/
.jlTrDX a:hover{color:#6868cf;text-decoration:auto;}/*!sc*/
data-styled.g42[id="sc-eHKghg"]{content:"hIcgHh,jlTrDX,"}/*!sc*/
.ibumEn{display:inline;}/*!sc*/
data-styled.g43[id="sc-enuJcz"]{content:"ibumEn,"}/*!sc*/
.bpcrOQ{position:relative;}/*!sc*/
data-styled.g44[id="sc-fnxfcy"]{content:"bpcrOQ,"}/*!sc*/
.dmQZei:hover>.sc-kYYDBl{opacity:1;}/*!sc*/
data-styled.g49[id="sc-fdNlyR"]{content:"dmQZei,"}/*!sc*/
.hTzIMC{font-family:Courier,monospace;font-size:13px;white-space:pre;contain:content;overflow-x:auto;}/*!sc*/
.hTzIMC .redoc-json code>.collapser{display:none;pointer-events:none;}/*!sc*/
.hTzIMC .callback-function{color:gray;}/*!sc*/
.hTzIMC .collapser:after{content:'-';cursor:pointer;}/*!sc*/
.hTzIMC .collapsed>.collapser:after{content:'+';cursor:pointer;}/*!sc*/
.hTzIMC .ellipsis:after{content:' … ';}/*!sc*/
.hTzIMC .collapsible{margin-left:2em;}/*!sc*/
.hTzIMC .hoverable{padding-top:1px;padding-bottom:1px;padding-left:2px;padding-right:2px;border-radius:2px;}/*!sc*/
.hTzIMC .hovered{background-color:rgba(235, 238, 249, 1);}/*!sc*/
.hTzIMC .collapser{background-color:transparent;border:0;color:#fff;font-family:Courier,monospace;font-size:13px;padding-right:6px;padding-left:6px;padding-top:0;padding-bottom:0;display:flex;align-items:center;justify-content:center;width:15px;height:15px;position:absolute;top:4px;left:-1.5em;cursor:default;user-select:none;-webkit-user-select:none;padding:2px;}/*!sc*/
.hTzIMC .collapser:focus{outline-color:#fff;outline-style:dotted;outline-width:1px;}/*!sc*/
.hTzIMC ul{list-style-type:none;padding:0px;margin:0px 0px 0px 26px;}/*!sc*/
.hTzIMC li{position:relative;display:block;}/*!sc*/
.hTzIMC .hoverable{display:inline-block;}/*!sc*/
.hTzIMC .selected{outline-style:solid;outline-width:1px;outline-style:dotted;}/*!sc*/
.hTzIMC .collapsed>.collapsible{display:none;}/*!sc*/
.hTzIMC .ellipsis{display:none;}/*!sc*/
.hTzIMC .collapsed>.ellipsis{display:inherit;}/*!sc*/
data-styled.g50[id="sc-gdPHyQ"]{content:"hTzIMC,"}/*!sc*/
.ipwpEI{padding:0.9em;background-color:rgba(38,50,56,0.4);margin:0 0 10px 0;display:block;font-family:Montserrat,sans-serif;font-size:0.929em;line-height:1.5em;}/*!sc*/
data-styled.g51[id="sc-bkdIYQ"]{content:"ipwpEI,"}/*!sc*/
.btjQAM{font-family:Montserrat,sans-serif;font-size:12px;position:absolute;z-index:1;top:-11px;left:12px;font-weight:600;color:rgba(255,255,255,0.7);}/*!sc*/
data-styled.g52[id="sc-fzvUyt"]{content:"btjQAM,"}/*!sc*/
.OizcY{position:relative;}/*!sc*/
data-styled.g53[id="sc-bCFcQi"]{content:"OizcY,"}/*!sc*/
.ldAlKZ{margin-top:15px;}/*!sc*/
data-styled.g56[id="sc-ljFCIV"]{content:"ldAlKZ,"}/*!sc*/
.hPYYRX{vertical-align:middle;font-size:13px;line-height:20px;}/*!sc*/
data-styled.g58[id="sc-mLnbr"]{content:"hPYYRX,"}/*!sc*/
.bMbGNb{color:rgba(102,102,102,0.9);}/*!sc*/
data-styled.g59[id="sc-cGjkqA"]{content:"bMbGNb,"}/*!sc*/
.cJfKTo{color:#666;}/*!sc*/
data-styled.g60[id="sc-gVBvQd"]{content:"cJfKTo,"}/*!sc*/
.gKRDZM{color:#d41f1c;font-size:0.9em;font-weight:normal;margin-left:20px;line-height:1;}/*!sc*/
data-styled.g62[id="sc-hXGFlK"]{content:"gKRDZM,"}/*!sc*/
.cktsMe{border-radius:2px;word-break:break-word;background-color:rgba(51,51,51,0.05);color:rgba(51,51,51,0.9);padding:0 5px;border:1px solid rgba(51,51,51,0.1);font-family:Courier,monospace;}/*!sc*/
+{margin-left:0;}/*!sc*/
data-styled.g66[id="sc-csuDXq"]{content:"cktsMe,"}/*!sc*/
.IbGQV{border-radius:2px;background-color:rgba(104,104,207,0.05);color:rgba(50,50,159,0.9);margin:0 5px;padding:0 5px;border:1px solid rgba(50,50,159,0.1);}/*!sc*/
+{margin-left:0;}/*!sc*/
data-styled.g68[id="sc-eMHqlA"]{content:"IbGQV,"}/*!sc*/
.ePXpRe{margin-top:0;margin-bottom:0.5em;}/*!sc*/
data-styled.g91[id="sc-fDEMvA"]{content:"ePXpRe,"}/*!sc*/
.ZzqAm{border:1px solid #32329f;color:#32329f;font-weight:normal;margin-left:0.5em;padding:4px 8px 4px;display:inline-block;text-decoration:none;cursor:pointer;}/*!sc*/
data-styled.g92[id="sc-jSWXVd"]{content:"ZzqAm,"}/*!sc*/
.ipAYcR{width:9ex;display:inline-block;height:13px;line-height:13px;background-color:#333;border-radius:3px;background-repeat:no-repeat;background-position:6px 4px;font-size:7px;font-family:Verdana,sans-serif;color:white;text-transform:uppercase;text-align:center;font-weight:bold;vertical-align:middle;margin-right:6px;margin-top:2px;}/*!sc*/
.ipAYcR.get{background-color:#2F8132;}/*!sc*/
.ipAYcR.post{background-color:#186FAF;}/*!sc*/
.ipAYcR.put{background-color:#95507c;}/*!sc*/
.ipAYcR.options{background-color:#947014;}/*!sc*/
.ipAYcR.patch{background-color:#bf581d;}/*!sc*/
.ipAYcR.delete{background-color:#cc3333;}/*!sc*/
.ipAYcR.basic{background-color:#707070;}/*!sc*/
.ipAYcR.link{background-color:#07818F;}/*!sc*/
.ipAYcR.head{background-color:#A23DAD;}/*!sc*/
.ipAYcR.hook{background-color:#32329f;}/*!sc*/
.ipAYcR.schema{background-color:#707070;}/*!sc*/
data-styled.g99[id="sc-dRiabc"]{content:"ipAYcR,"}/*!sc*/
.jEVUSf{margin:0;padding:0;}/*!sc*/
.jEVUSf:first-child{padding-bottom:32px;}/*!sc*/
.sc-igAlAF .sc-igAlAF{font-size:0.929em;}/*!sc*/
data-styled.g100[id="sc-igAlAF"]{content:"jEVUSf,"}/*!sc*/
.cJcyUf{list-style:none inside none;overflow:hidden;text-overflow:ellipsis;padding:0;}/*!sc*/
data-styled.g101[id="sc-kIxgmw"]{content:"cJcyUf,"}/*!sc*/
.cdumAT{cursor:pointer;color:#333333;margin:0;padding:12.5px 20px;display:flex;justify-content:space-between;font-family:Montserrat,sans-serif;background-color:#fafafa;}/*!sc*/
.cdumAT:hover{color:#32329f;background-color:#ededed;}/*!sc*/
.cdumAT .sc-cMHOsR{height:1.5em;width:1.5em;}/*!sc*/
.cdumAT .sc-cMHOsR polygon{fill:#333333;}/*!sc*/
data-styled.g102[id="sc-bjXbQp"]{content:"cdumAT,"}/*!sc*/
.ijBZNN{display:inline-block;vertical-align:middle;width:calc(100% - 38px);overflow:hidden;text-overflow:ellipsis;}/*!sc*/
data-styled.g103[id="sc-eivjMC"]{content:"ijBZNN,"}/*!sc*/
.iUGZDB{font-size:0.8em;margin-top:10px;text-align:center;position:fixed;width:260px;bottom:0;background:#fafafa;}/*!sc*/
.iUGZDB a,.iUGZDB a:visited,.iUGZDB a:hover{color:#333333!important;padding:5px 0;border-top:1px solid #e1e1e1;text-decoration:none;display:flex;align-items:center;justify-content:center;}/*!sc*/
.iUGZDB img{width:15px;margin-right:5px;}/*!sc*/
@media screen and (max-width: 50rem){.iUGZDB{width:100%;}}/*!sc*/
data-styled.g104[id="sc-fZqOPr"]{content:"iUGZDB,"}/*!sc*/
.gxwpFr{cursor:pointer;position:relative;margin-bottom:5px;}/*!sc*/
data-styled.g110[id="sc-depuAN"]{content:"gxwpFr,"}/*!sc*/
.daOnZV{font-family:Courier,monospace;margin-left:10px;flex:1;overflow-x:hidden;text-overflow:ellipsis;}/*!sc*/
data-styled.g111[id="sc-dItInd"]{content:"daOnZV,"}/*!sc*/
.ciASxA{outline:0;color:inherit;width:100%;text-align:left;cursor:pointer;padding:10px 30px 10px 20px;border-radius:4px 4px 0 0;background-color:#11171a;display:flex;white-space:nowrap;align-items:center;border:1px solid transparent;border-bottom:0;transition:border-color 0.25s ease;}/*!sc*/
.ciASxA ..sc-dItInd{color:#ffffff;}/*!sc*/
.ciASxA:focus{box-shadow:inset 0 2px 2px rgba(0, 0, 0, 0.45),0 2px 0 rgba(128, 128, 128, 0.25);}/*!sc*/
data-styled.g112[id="sc-hUiJjc"]{content:"ciASxA,"}/*!sc*/
.hyzpTe{font-size:0.929em;line-height:20px;background-color:#2F8132;color:#ffffff;padding:3px 10px;text-transform:uppercase;font-family:Montserrat,sans-serif;margin:0;}/*!sc*/
.ixoukX{font-size:0.929em;line-height:20px;background-color:#95507c;color:#ffffff;padding:3px 10px;text-transform:uppercase;font-family:Montserrat,sans-serif;margin:0;}/*!sc*/
.crWMcq{font-size:0.929em;line-height:20px;background-color:#bf581d;color:#ffffff;padding:3px 10px;text-transform:uppercase;font-family:Montserrat,sans-serif;margin:0;}/*!sc*/
.emwLPa{font-size:0.929em;line-height:20px;background-color:#cc3333;color:#ffffff;padding:3px 10px;text-transform:uppercase;font-family:Montserrat,sans-serif;margin:0;}/*!sc*/
.fUSliQ{font-size:0.929em;line-height:20px;background-color:#186FAF;color:#ffffff;padding:3px 10px;text-transform:uppercase;font-family:Montserrat,sans-serif;margin:0;}/*!sc*/
data-styled.g113[id="sc-duJKf"]{content:"hyzpTe,ixoukX,crWMcq,emwLPa,fUSliQ,"}/*!sc*/
.dRMALb{position:absolute;width:100%;z-index:100;background:#fafafa;color:#263238;box-sizing:border-box;box-shadow:0 0 6px rgba(0, 0, 0, 0.33);overflow:hidden;border-bottom-left-radius:4px;border-bottom-right-radius:4px;transition:all 0.25s ease;visibility:hidden;transform:translateY(-50%) scaleY(0);}/*!sc*/
data-styled.g114[id="sc-epjKGe"]{content:"dRMALb,"}/*!sc*/
.RtpSI{padding:10px;}/*!sc*/
data-styled.g115[id="sc-dBXbGZ"]{content:"RtpSI,"}/*!sc*/
.emnlY{padding:5px;border:1px solid #ccc;background:#fff;word-break:break-all;color:#32329f;}/*!sc*/
.emnlY >span{color:#333333;}/*!sc*/
data-styled.g116[id="sc-hNMcCY"]{content:"emnlY,"}/*!sc*/
.kfKSDW{display:block;border:0;width:100%;text-align:left;padding:10px;border-radius:2px;margin-bottom:4px;line-height:1.5em;cursor:pointer;color:#1d8127;background-color:rgba(29,129,39,0.07);}/*!sc*/
.kfKSDW:focus{outline:auto #1d8127;}/*!sc*/
.klixnc{display:block;border:0;width:100%;text-align:left;padding:10px;border-radius:2px;margin-bottom:4px;line-height:1.5em;cursor:pointer;color:#d41f1c;background-color:rgba(212,31,28,0.07);}/*!sc*/
.klixnc:focus{outline:auto #d41f1c;}/*!sc*/
.hzqENU{display:block;border:0;width:100%;text-align:left;padding:10px;border-radius:2px;margin-bottom:4px;line-height:1.5em;cursor:pointer;color:#1d8127;background-color:rgba(29,129,39,0.07);cursor:default;}/*!sc*/
.hzqENU:focus{outline:auto #1d8127;}/*!sc*/
.hzqENU::before{content:"—";font-weight:bold;width:1.5em;text-align:center;display:inline-block;vertical-align:top;}/*!sc*/
.hzqENU:focus{outline:0;}/*!sc*/
data-styled.g119[id="sc-fCMaXB"]{content:"kfKSDW,klixnc,hzqENU,"}/*!sc*/
.bMwheE{vertical-align:top;}/*!sc*/
data-styled.g122[id="sc-bPKaLH"]{content:"bMwheE,"}/*!sc*/
.kJsvpq{font-size:1.3em;padding:0.2em 0;margin:3em 0 1.1em;color:#333333;font-weight:normal;}/*!sc*/
data-styled.g123[id="sc-dCIkRk"]{content:"kJsvpq,"}/*!sc*/
.kgLEAE{user-select:none;width:20px;height:20px;align-self:center;display:flex;flex-direction:column;color:#32329f;}/*!sc*/
data-styled.g129[id="sc-bFVjpq"]{content:"kgLEAE,"}/*!sc*/
.duKCiJ{width:260px;background-color:#fafafa;overflow:hidden;display:flex;flex-direction:column;backface-visibility:hidden;height:100vh;position:sticky;position:-webkit-sticky;top:0;}/*!sc*/
@media screen and (max-width: 50rem){.duKCiJ{position:fixed;z-index:20;width:100%;background:#fafafa;display:none;}}/*!sc*/
@media print{.duKCiJ{display:none;}}/*!sc*/
data-styled.g130[id="sc-dKctwf"]{content:"duKCiJ,"}/*!sc*/
.dMMOLy{outline:none;user-select:none;background-color:#f2f2f2;color:#32329f;display:none;cursor:pointer;position:fixed;right:20px;z-index:100;border-radius:50%;box-shadow:0 0 20px rgba(0, 0, 0, 0.3);bottom:44px;width:60px;height:60px;padding:0 20px;}/*!sc*/
@media screen and (max-width: 50rem){.dMMOLy{display:flex;}}/*!sc*/
.dMMOLy svg{color:#0065FB;}/*!sc*/
@media print{.dMMOLy{display:none;}}/*!sc*/
data-styled.g131[id="sc-eaVcev"]{content:"dMMOLy,"}/*!sc*/
.hmbocG{font-family:Roboto,sans-serif;font-size:14px;font-weight:400;line-height:1.5em;color:#333333;display:flex;position:relative;text-align:left;-webkit-font-smoothing:antialiased;font-smoothing:antialiased;text-rendering:optimizeSpeed!important;tap-highlight-color:rgba(0, 0, 0, 0);text-size-adjust:100%;}/*!sc*/
.hmbocG *{box-sizing:border-box;-webkit-tap-highlight-color:rgba(255, 255, 255, 0);}/*!sc*/
data-styled.g132[id="sc-imKdau"]{content:"hmbocG,"}/*!sc*/
.dUROLw{z-index:1;position:relative;overflow:hidden;width:calc(100% - 260px);contain:layout;}/*!sc*/
@media print,screen and (max-width: 50rem){.dUROLw{width:100%;}}/*!sc*/
data-styled.g133[id="sc-kNdunJ"]{content:"dUROLw,"}/*!sc*/
.iSKrHn{background:#263238;position:absolute;top:0;bottom:0;right:0;width:calc((100% - 260px) * 0.4);}/*!sc*/
@media print,screen and (max-width: 75rem){.iSKrHn{display:none;}}/*!sc*/
data-styled.g134[id="sc-dJwLKM"]{content:"iSKrHn,"}/*!sc*/
.hPTtsA{padding:5px 0;}/*!sc*/
data-styled.g135[id="sc-dUyvyr"]{content:"hPTtsA,"}/*!sc*/
.blzVse{width:calc(100% - 40px);box-sizing:border-box;margin:0 20px;padding:5px 10px 5px 20px;border:0;border-bottom:1px solid #e1e1e1;font-family:Roboto,sans-serif;font-weight:bold;font-size:13px;color:#333333;background-color:transparent;outline:none;}/*!sc*/
data-styled.g136[id="sc-ignwuq"]{content:"blzVse,"}/*!sc*/
.fAePYH{position:absolute;left:20px;height:1.8em;width:0.9em;}/*!sc*/
.fAePYH path{fill:#333333;}/*!sc*/
data-styled.g137[id="sc-kGGNHF"]{content:"fAePYH,"}/*!sc*/
</style>
  <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,700|Roboto:300,400,700" rel="stylesheet">
</head>

<body>
  
      <div id="redoc"><div class="sc-imKdau hmbocG redoc-wrap"><div class="sc-dKctwf duKCiJ menu-content" style="top:0px;height:calc(100vh - 0px)"><div role="search" class="sc-dUyvyr hPTtsA"><svg class="sc-kGGNHF fAePYH search-icon" version="1.1" viewBox="0 0 1000 1000" x="0px" xmlns="http://www.w3.org/2000/svg" y="0px"><path d="M968.2,849.4L667.3,549c83.9-136.5,66.7-317.4-51.7-435.6C477.1-25,252.5-25,113.9,113.4c-138.5,138.3-138.5,362.6,0,501C219.2,730.1,413.2,743,547.6,666.5l301.9,301.4c43.6,43.6,76.9,14.9,104.2-12.4C981,928.3,1011.8,893,968.2,849.4z M524.5,522c-88.9,88.7-233,88.7-321.8,0c-88.9-88.7-88.9-232.6,0-321.3c88.9-88.7,233-88.7,321.8,0C613.4,289.4,613.4,433.3,524.5,522z"></path></svg><input placeholder="Search..." aria-label="Search" type="text" class="sc-ignwuq blzVse search-input" value=""/></div><div class="sc-dUnjic cqBLnv scrollbar-container undefined"><ul role="menu" class="sc-igAlAF jEVUSf"><li tabindex="0" depth="2" data-item-id="/paths/~1master-emplr-list/get" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="get" class="sc-dRiabc ipAYcR operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">組合マスターリストの取得</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1emplr/put" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="put" class="sc-dRiabc ipAYcR operation-type put">put</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">組合作成</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1emplr/patch" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="patch" class="sc-dRiabc ipAYcR operation-type patch">patch</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">組合更新</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1emplr/get" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="get" class="sc-dRiabc ipAYcR operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">組合取得</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1emplr/delete" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="delete" class="sc-dRiabc ipAYcR operation-type delete">del</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">組合削除</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1user/put" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="put" class="sc-dRiabc ipAYcR operation-type put">put</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">ユーザー作成</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1user/patch" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="patch" class="sc-dRiabc ipAYcR operation-type patch">patch</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">ユーザー更新</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1user/get" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="get" class="sc-dRiabc ipAYcR operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">ユーザー取得</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1user/delete" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="delete" class="sc-dRiabc ipAYcR operation-type delete">del</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">ユーザー削除</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1maintenance-info/put" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="put" class="sc-dRiabc ipAYcR operation-type put">put</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">メンテナンス情報作成</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1maintenance-info/get" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="get" class="sc-dRiabc ipAYcR operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">メンテナンス情報取得</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1maintenance-info/patch" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="patch" class="sc-dRiabc ipAYcR operation-type patch">patch</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">メンテナンス情報更新</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1maintenance-info/delete" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="delete" class="sc-dRiabc ipAYcR operation-type delete">del</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">メンテナンス情報削除</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1service-status/get" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="get" class="sc-dRiabc ipAYcR operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">サーバーステータス取得</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1service-status/patch" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="patch" class="sc-dRiabc ipAYcR operation-type patch">patch</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">サーバーステータス更新</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1user-list/get" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="get" class="sc-dRiabc ipAYcR operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">ユーザー一覧取得</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1maintenance-info-list/get" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="get" class="sc-dRiabc ipAYcR operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">メンテナンス情報一覧取得</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1confirm-regist-user-list-csv/post" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="post" class="sc-dRiabc ipAYcR operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">ユーザー登録確認CSV作成</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1regist-user-list/post" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="post" class="sc-dRiabc ipAYcR operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">ユーザー登録</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1confirm-send-mail-user-list/post" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="post" class="sc-dRiabc ipAYcR operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">メール送信確認</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1send-mail-user-list/post" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="post" class="sc-dRiabc ipAYcR operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">メール送信</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1notice-info-list/get" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="get" class="sc-dRiabc ipAYcR operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">お知らせ一覧取得</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1notice-info/get" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="get" class="sc-dRiabc ipAYcR operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">お知らせ取得</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1notice-info/post" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="post" class="sc-dRiabc ipAYcR operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">お知らせ作成</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1notice-info/delete" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="delete" class="sc-dRiabc ipAYcR operation-type delete">del</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">お知らせ削除</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1emplr-list/get" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="get" class="sc-dRiabc ipAYcR operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">組合一覧取得</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1hierarchy-role/get" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="get" class="sc-dRiabc ipAYcR operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">組合階層取得</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1hierarchy-role/put" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="put" class="sc-dRiabc ipAYcR operation-type put">put</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">組合階層追加</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1hierarchy-role/delete" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="delete" class="sc-dRiabc ipAYcR operation-type delete">del</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">組合階層削除</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1hierarchy-role-all-list/get" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="get" class="sc-dRiabc ipAYcR operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">階層情報ロールの一覧取得</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1hierarchy-role-list/get" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="get" class="sc-dRiabc ipAYcR operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">階層情報ロールの一覧取得</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1jigyosyo-cd-list/get" role="menuitem" class="sc-kIxgmw cJcyUf"><label class="sc-bjXbQp cdumAT -depth2"><span type="get" class="sc-dRiabc ipAYcR operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eivjMC ijBZNN">事業所CDリストの取得</span></label></li></ul><div class="sc-fZqOPr iUGZDB"><a target="_blank" rel="noopener noreferrer" href="https://redocly.com/redoc/">API docs by Redocly</a></div></div></div><div class="sc-eaVcev dMMOLy"><div class="sc-bFVjpq kgLEAE"><svg class="" style="transform:translate(2px, -4px) rotate(180deg);transition:transform 0.2s ease" viewBox="0 0 926.23699 573.74994" version="1.1" x="0px" y="0px" width="15" height="15"><g transform="translate(904.92214,-879.1482)"><path d="
          m -673.67664,1221.6502 -231.2455,-231.24803 55.6165,
          -55.627 c 30.5891,-30.59485 56.1806,-55.627 56.8701,-55.627 0.6894,
          0 79.8637,78.60862 175.9427,174.68583 l 174.6892,174.6858 174.6892,
          -174.6858 c 96.079,-96.07721 175.253196,-174.68583 175.942696,
          -174.68583 0.6895,0 26.281,25.03215 56.8701,
          55.627 l 55.6165,55.627 -231.245496,231.24803 c -127.185,127.1864
          -231.5279,231.248 -231.873,231.248 -0.3451,0 -104.688,
          -104.0616 -231.873,-231.248 z
        " fill="currentColor"></path></g></svg><svg class="" style="transform:translate(2px, 4px);transition:transform 0.2s ease" viewBox="0 0 926.23699 573.74994" version="1.1" x="0px" y="0px" width="15" height="15"><g transform="translate(904.92214,-879.1482)"><path d="
          m -673.67664,1221.6502 -231.2455,-231.24803 55.6165,
          -55.627 c 30.5891,-30.59485 56.1806,-55.627 56.8701,-55.627 0.6894,
          0 79.8637,78.60862 175.9427,174.68583 l 174.6892,174.6858 174.6892,
          -174.6858 c 96.079,-96.07721 175.253196,-174.68583 175.942696,
          -174.68583 0.6895,0 26.281,25.03215 56.8701,
          55.627 l 55.6165,55.627 -231.245496,231.24803 c -127.185,127.1864
          -231.5279,231.248 -231.873,231.248 -0.3451,0 -104.688,
          -104.0616 -231.873,-231.248 z
        " fill="currentColor"></path></g></svg></div></div><div class="sc-kNdunJ dUROLw api-content"><div class="sc-dDvxFM dTJVET"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV api-info"><h1 class="sc-hWWBcw sc-fDEMvA gJBEDr ePXpRe">健助+管理API<!-- --> <span>(<!-- -->1.0.0<!-- -->)</span></h1><p>Download OpenAPI specification<!-- -->:<a download="openapi.json" target="_blank" class="sc-jSWXVd ZzqAm">Download</a></p><div class="sc-dODueM sc-eHKghg fhLETC hIcgHh"></div><div data-role="redoc-summary" html="" class="sc-dODueM sc-eHKghg fhLETC hIcgHh"></div><div data-role="redoc-description" html="" class="sc-dODueM sc-eHKghg fhLETC hIcgHh"></div></div></div></div><div id="/paths/~1master-emplr-list/get" data-section-id="/paths/~1master-emplr-list/get" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1master-emplr-list/get" aria-label="/paths/~1master-emplr-list/get"></a>組合マスターリストの取得<!-- --> </h2><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB kfKSDW"><svg class="sc-cMHOsR fIBTiR" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">200<!-- --> </strong><div html="&lt;p&gt;成功&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>成功</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="get" class="sc-duJKf hyzpTe http-verb get">get</span><span class="sc-dItInd daOnZV">/master-emplr-list</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/master-emplr-list</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/master-emplr-list</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:R9p0q:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9p0q:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:R9p0q:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9p0q:1" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9p0q:0" aria-labelledby="tab:R9p0q:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable "><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token number">100001</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"cd"</span>: <span class="token string">&quot;JMD&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"secondaryUseProperty"</span>: <span class="token number">0</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9p0q:1" aria-labelledby="tab:R9p0q:1"></div></div></div></div></div></div><div id="/paths/~1emplr/put" data-section-id="/paths/~1emplr/put" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1emplr/put" aria-label="/paths/~1emplr/put"></a>組合作成<!-- --> </h2><h5 class="sc-cZztgT fjywuK">Request Body schema: <span class="sc-dHHKhh cIfRxr">application/json</span></h5><div html="" class="sc-dODueM sc-eHKghg fhLETC hIcgHh"></div><table class="sc-ekQdgj AJjSv"><tbody><tr class=""><td kind="field" title="id" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">id</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span><span> <span class="sc-mLnbr sc-eMHqlA hPYYRX IbGQV"> <!-- -->= 5 characters<!-- --> </span></span></div> <div><div html="&lt;p&gt;組合ID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>組合ID</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="cd" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">cd</span></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span></div> <div><div html="" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"></div></div></div></td></tr><tr class=""><td kind="field" title="customEmplrName" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">customEmplrName</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span></div> <div><div html="&lt;p&gt;組合名&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>組合名</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="groupId" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">groupId</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span></div> <div><div html="&lt;p&gt;組合区分 1: 単一 2: 組合 3: 自治体&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>組合区分 1: 単一 2: 組合 3: 自治体</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB hzqENU" disabled=""><strong class="sc-bPKaLH bMwheE">201<!-- --> </strong><div html="&lt;p&gt;Created&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Created</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">400<!-- --> </strong><div html="&lt;p&gt;Bad Request&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Bad Request</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">409<!-- --> </strong><div html="&lt;p&gt;Conflict&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Conflict</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="put" class="sc-duJKf ixoukX http-verb put">put</span><span class="sc-dItInd daOnZV">/emplr</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/emplr</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/emplr</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Request samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab:R991a:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R991a:0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R991a:0" aria-labelledby="tab:R991a:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token number">100010</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"cd"</span>: <span class="token string">&quot;JMD&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"customEmplrName"</span>: <span class="token string">&quot;北海道健保&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"groupId"</span>: <span class="token number">1</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-error react-tabs__tab--selected" role="tab" id="tab:R9p1a:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9p1a:0" tabindex="0" data-rttab="true">400</li><li class="tab-error" role="tab" id="tab:R9p1a:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9p1a:1" data-rttab="true">409</li><li class="tab-error" role="tab" id="tab:R9p1a:2" aria-selected="false" aria-disabled="false" aria-controls="panel:R9p1a:2" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9p1a:0" aria-labelledby="tab:R9p1a:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9p1a:1" aria-labelledby="tab:R9p1a:1"></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9p1a:2" aria-labelledby="tab:R9p1a:2"></div></div></div></div></div></div><div id="/paths/~1emplr/patch" data-section-id="/paths/~1emplr/patch" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1emplr/patch" aria-label="/paths/~1emplr/patch"></a>組合更新<!-- --> </h2><h5 class="sc-cZztgT fjywuK">Request Body schema: <span class="sc-dHHKhh cIfRxr">application/json</span></h5><div html="" class="sc-dODueM sc-eHKghg fhLETC hIcgHh"></div><table class="sc-ekQdgj AJjSv"><tbody><tr class=""><td kind="field" title="id" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">id</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span><span> <span class="sc-mLnbr sc-eMHqlA hPYYRX IbGQV"> <!-- -->= 5 characters<!-- --> </span></span></div> <div><div html="&lt;p&gt;組合ID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>組合ID</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="cd" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">cd</span></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span></div> <div><div html="" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"></div></div></div></td></tr><tr class=""><td kind="field" title="customEmplrName" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">customEmplrName</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span></div> <div><div html="&lt;p&gt;組合名&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>組合名</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="groupId" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">groupId</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span></div> <div><div html="&lt;p&gt;組合区分 1: 単一 2: 組合 3: 自治体&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>組合区分 1: 単一 2: 組合 3: 自治体</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB hzqENU" disabled=""><strong class="sc-bPKaLH bMwheE">204<!-- --> </strong><div html="&lt;p&gt;No Content&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>No Content</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">400<!-- --> </strong><div html="&lt;p&gt;Bad Request&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Bad Request</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="patch" class="sc-duJKf crWMcq http-verb patch">patch</span><span class="sc-dItInd daOnZV">/emplr</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/emplr</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/emplr</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Request samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab:R991q:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R991q:0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R991q:0" aria-labelledby="tab:R991q:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token number">100010</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"cd"</span>: <span class="token string">&quot;JMD&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"customEmplrName"</span>: <span class="token string">&quot;北海道健保&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"groupId"</span>: <span class="token number">1</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-error react-tabs__tab--selected" role="tab" id="tab:R9p1q:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9p1q:0" tabindex="0" data-rttab="true">400</li><li class="tab-error" role="tab" id="tab:R9p1q:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9p1q:1" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9p1q:0" aria-labelledby="tab:R9p1q:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9p1q:1" aria-labelledby="tab:R9p1q:1"></div></div></div></div></div></div><div id="/paths/~1emplr/get" data-section-id="/paths/~1emplr/get" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1emplr/get" aria-label="/paths/~1emplr/get"></a>組合取得<!-- --> </h2><div><h5 class="sc-cZztgT fjywuK">query<!-- --> Parameters</h5><table class="sc-ekQdgj AJjSv"><tbody><tr class="last "><td kind="field" title="id" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">id</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span></div> <div><span class="sc-mLnbr hPYYRX"> <!-- -->Example:<!-- --> </span> <span class="sc-mLnbr sc-csuDXq hPYYRX cktsMe">id=100001</span></div><div><div html="&lt;p&gt;組合ID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>組合ID</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB kfKSDW"><svg class="sc-cMHOsR fIBTiR" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">200<!-- --> </strong><div html="&lt;p&gt;成功&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>成功</p>
</div></button></div><div><button class="sc-fCMaXB hzqENU" disabled=""><strong class="sc-bPKaLH bMwheE">204<!-- --> </strong><div html="&lt;p&gt;No Content&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>No Content</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">400<!-- --> </strong><div html="&lt;p&gt;Bad Request&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Bad Request</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="get" class="sc-duJKf hyzpTe http-verb get">get</span><span class="sc-dItInd daOnZV">/emplr</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/emplr</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/emplr</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:R9p2a:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9p2a:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:R9p2a:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9p2a:1" data-rttab="true">400</li><li class="tab-error" role="tab" id="tab:R9p2a:2" aria-selected="false" aria-disabled="false" aria-controls="panel:R9p2a:2" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9p2a:0" aria-labelledby="tab:R9p2a:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token number">100010</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"cd"</span>: <span class="token string">&quot;JMD&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"customEmplrName"</span>: <span class="token string">&quot;北海道健保&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"groupId"</span>: <span class="token number">1</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9p2a:1" aria-labelledby="tab:R9p2a:1"></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9p2a:2" aria-labelledby="tab:R9p2a:2"></div></div></div></div></div></div><div id="/paths/~1emplr/delete" data-section-id="/paths/~1emplr/delete" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1emplr/delete" aria-label="/paths/~1emplr/delete"></a>組合削除<!-- --> </h2><div><h5 class="sc-cZztgT fjywuK">query<!-- --> Parameters</h5><table class="sc-ekQdgj AJjSv"><tbody><tr class="last "><td kind="field" title="id" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">id</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span></div> <div><span class="sc-mLnbr hPYYRX"> <!-- -->Example:<!-- --> </span> <span class="sc-mLnbr sc-csuDXq hPYYRX cktsMe">id=100001</span></div><div><div html="&lt;p&gt;組合ID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>組合ID</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB hzqENU" disabled=""><strong class="sc-bPKaLH bMwheE">204<!-- --> </strong><div html="&lt;p&gt;No Content&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>No Content</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">400<!-- --> </strong><div html="&lt;p&gt;Bad Request&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Bad Request</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="delete" class="sc-duJKf emwLPa http-verb delete">delete</span><span class="sc-dItInd daOnZV">/emplr</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/emplr</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/emplr</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-error react-tabs__tab--selected" role="tab" id="tab:R9p2q:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9p2q:0" tabindex="0" data-rttab="true">400</li><li class="tab-error" role="tab" id="tab:R9p2q:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9p2q:1" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9p2q:0" aria-labelledby="tab:R9p2q:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9p2q:1" aria-labelledby="tab:R9p2q:1"></div></div></div></div></div></div><div id="/paths/~1user/put" data-section-id="/paths/~1user/put" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1user/put" aria-label="/paths/~1user/put"></a>ユーザー作成<!-- --> </h2><h5 class="sc-cZztgT fjywuK">Request Body schema: <span class="sc-dHHKhh cIfRxr">application/json</span></h5><div html="" class="sc-dODueM sc-eHKghg fhLETC hIcgHh"></div><table class="sc-ekQdgj AJjSv"><tbody><tr class=""><td kind="field" title="id" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">id</span></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span></div> <div><div html="&lt;p&gt;ユーザーID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>ユーザーID</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="name" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">name</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo"> <!-- -->&lt;<!-- -->string<!-- -->&gt;<!-- --> </span></div> <div><div html="&lt;p&gt;ユーザー名&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>ユーザー名</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="email" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">email</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo"> <!-- -->&lt;<!-- -->email<!-- -->&gt;<!-- --> </span></div> <div><div html="&lt;p&gt;メールアドレス&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>メールアドレス</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="hierarchyRoleId" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">hierarchyRoleId</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span></div> <div><div html="&lt;p&gt;階層情報ロールID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>階層情報ロールID</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="isAdmin" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">isAdmin</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">boolean</span></div> <div><div html="&lt;p&gt;組合ID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>組合ID</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="comment" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">comment</span></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span></div> <div><div html="&lt;p&gt;備考&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>備考</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB hzqENU" disabled=""><strong class="sc-bPKaLH bMwheE">204<!-- --> </strong><div html="&lt;p&gt;No Content&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>No Content</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">400<!-- --> </strong><div html="&lt;p&gt;Bad Request&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Bad Request</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">401<!-- --> </strong><div html="&lt;p&gt;Unauthorized&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Unauthorized</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="put" class="sc-duJKf ixoukX http-verb put">put</span><span class="sc-dItInd daOnZV">/user</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/user</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/user</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Request samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab:R993a:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R993a:0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R993a:0" aria-labelledby="tab:R993a:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"name"</span>: <span class="token string">&quot;山田太郎&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"email"</span>: <span class="token string">&quot;<EMAIL>&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"hierarchyRoleId"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"isAdmin"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"comment"</span>: <span class="token string">&quot;備考&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-error react-tabs__tab--selected" role="tab" id="tab:R9p3a:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9p3a:0" tabindex="0" data-rttab="true">400</li><li class="tab-error" role="tab" id="tab:R9p3a:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9p3a:1" data-rttab="true">401</li><li class="tab-error" role="tab" id="tab:R9p3a:2" aria-selected="false" aria-disabled="false" aria-controls="panel:R9p3a:2" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9p3a:0" aria-labelledby="tab:R9p3a:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9p3a:1" aria-labelledby="tab:R9p3a:1"></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9p3a:2" aria-labelledby="tab:R9p3a:2"></div></div></div></div></div></div><div id="/paths/~1user/patch" data-section-id="/paths/~1user/patch" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1user/patch" aria-label="/paths/~1user/patch"></a>ユーザー更新<!-- --> </h2><h5 class="sc-cZztgT fjywuK">Request Body schema: <span class="sc-dHHKhh cIfRxr">application/json</span></h5><div html="" class="sc-dODueM sc-eHKghg fhLETC hIcgHh"></div><table class="sc-ekQdgj AJjSv"><tbody><tr class=""><td kind="field" title="id" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">id</span></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span></div> <div><div html="&lt;p&gt;ユーザーID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>ユーザーID</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="name" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">name</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo"> <!-- -->&lt;<!-- -->string<!-- -->&gt;<!-- --> </span></div> <div><div html="&lt;p&gt;ユーザー名&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>ユーザー名</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="email" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">email</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo"> <!-- -->&lt;<!-- -->email<!-- -->&gt;<!-- --> </span></div> <div><div html="&lt;p&gt;メールアドレス&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>メールアドレス</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="hierarchyRoleId" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">hierarchyRoleId</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span></div> <div><div html="&lt;p&gt;階層情報ロールID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>階層情報ロールID</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="isAdmin" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">isAdmin</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">boolean</span></div> <div><div html="&lt;p&gt;組合ID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>組合ID</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="comment" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">comment</span></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span></div> <div><div html="&lt;p&gt;備考&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>備考</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB hzqENU" disabled=""><strong class="sc-bPKaLH bMwheE">204<!-- --> </strong><div html="&lt;p&gt;No Content&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>No Content</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="patch" class="sc-duJKf crWMcq http-verb patch">patch</span><span class="sc-dItInd daOnZV">/user</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/user</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/user</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Request samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab:R993q:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R993q:0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R993q:0" aria-labelledby="tab:R993q:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"name"</span>: <span class="token string">&quot;山田太郎&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"email"</span>: <span class="token string">&quot;<EMAIL>&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"hierarchyRoleId"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"isAdmin"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"comment"</span>: <span class="token string">&quot;備考&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-error react-tabs__tab--selected" role="tab" id="tab:R9p3q:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9p3q:0" tabindex="0" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9p3q:0" aria-labelledby="tab:R9p3q:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="/paths/~1user/get" data-section-id="/paths/~1user/get" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1user/get" aria-label="/paths/~1user/get"></a>ユーザー取得<!-- --> </h2><div><h5 class="sc-cZztgT fjywuK">query<!-- --> Parameters</h5><table class="sc-ekQdgj AJjSv"><tbody><tr class="last "><td kind="field" title="id" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">id</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span></div> <div><span class="sc-mLnbr hPYYRX"> <!-- -->Example:<!-- --> </span> <span class="sc-mLnbr sc-csuDXq hPYYRX cktsMe">id=1</span></div><div><div html="&lt;p&gt;ユーザーID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>ユーザーID</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB kfKSDW"><svg class="sc-cMHOsR fIBTiR" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">200<!-- --> </strong><div html="&lt;p&gt;成功&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>成功</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="get" class="sc-duJKf hyzpTe http-verb get">get</span><span class="sc-dItInd daOnZV">/user</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/user</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/user</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:R9p4a:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9p4a:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:R9p4a:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9p4a:1" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9p4a:0" aria-labelledby="tab:R9p4a:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;jmdc-yamada&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"name"</span>: <span class="token string">&quot;山田太郎&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"email"</span>: <span class="token string">&quot;<EMAIL>&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"emplrCd"</span>: <span class="token string">&quot;emplr-123&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"emplrName"</span>: <span class="token string">&quot;emplr-123&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"isAdmin"</span>: <span class="token boolean">false</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"comment"</span>: <span class="token string">&quot;備考&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"auth0UserId"</span>: <span class="token string">&quot;auth0|123456789&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"hierarchyLevel"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"jigyosyoCd"</span>: <span class="token number">23</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"isBlocked"</span>: <span class="token boolean">false</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"isMfa"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"lastLogin"</span>: <span class="token string">&quot;2020-01-31T23:59:59+09:00&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9p4a:1" aria-labelledby="tab:R9p4a:1"></div></div></div></div></div></div><div id="/paths/~1user/delete" data-section-id="/paths/~1user/delete" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1user/delete" aria-label="/paths/~1user/delete"></a>ユーザー削除<!-- --> </h2><div><h5 class="sc-cZztgT fjywuK">query<!-- --> Parameters</h5><table class="sc-ekQdgj AJjSv"><tbody><tr class="last "><td kind="field" title="id" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">id</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span></div> <div><span class="sc-mLnbr hPYYRX"> <!-- -->Example:<!-- --> </span> <span class="sc-mLnbr sc-csuDXq hPYYRX cktsMe">id=1</span></div><div><div html="&lt;p&gt;ユーザーID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>ユーザーID</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB hzqENU" disabled=""><strong class="sc-bPKaLH bMwheE">204<!-- --> </strong><div html="&lt;p&gt;No Content&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>No Content</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="delete" class="sc-duJKf emwLPa http-verb delete">delete</span><span class="sc-dItInd daOnZV">/user</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/user</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/user</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-error react-tabs__tab--selected" role="tab" id="tab:R9p4q:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9p4q:0" tabindex="0" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9p4q:0" aria-labelledby="tab:R9p4q:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="/paths/~1maintenance-info/put" data-section-id="/paths/~1maintenance-info/put" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1maintenance-info/put" aria-label="/paths/~1maintenance-info/put"></a>メンテナンス情報作成<!-- --> </h2><h5 class="sc-cZztgT fjywuK">Request Body schema: <span class="sc-dHHKhh cIfRxr">application/json</span></h5><div html="" class="sc-dODueM sc-eHKghg fhLETC hIcgHh"></div><table class="sc-ekQdgj AJjSv"><tbody><tr class=""><td kind="field" title="id" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">id</span></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo"> <!-- -->&lt;<!-- -->int64<!-- -->&gt;<!-- --> </span></div> <div><div html="&lt;p&gt;お知らせID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>お知らせID</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="releaseDate" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">releaseDate</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo"> <!-- -->&lt;<!-- -->date-time<!-- -->&gt;<!-- --> </span></div> <div><div html="&lt;p&gt;日付&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>日付</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="title" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">title</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span></div> <div><div html="&lt;p&gt;タイトル&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>タイトル</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="comment" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">comment</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span></div> <div><div html="&lt;p&gt;内容&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>内容</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB hzqENU" disabled=""><strong class="sc-bPKaLH bMwheE">204<!-- --> </strong><div html="&lt;p&gt;No Content&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>No Content</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="put" class="sc-duJKf ixoukX http-verb put">put</span><span class="sc-dItInd daOnZV">/maintenance-info</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/maintenance-info</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/maintenance-info</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Request samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab:R995a:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R995a:0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R995a:0" aria-labelledby="tab:R995a:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"releaseDate"</span>: <span class="token string">&quot;2023-10-01T12:00:00Z&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"title"</span>: <span class="token string">&quot;メンテナンスタイトル&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"comment"</span>: <span class="token string">&quot;メンテナンスの内容&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-error react-tabs__tab--selected" role="tab" id="tab:R9p5a:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9p5a:0" tabindex="0" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9p5a:0" aria-labelledby="tab:R9p5a:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="/paths/~1maintenance-info/get" data-section-id="/paths/~1maintenance-info/get" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1maintenance-info/get" aria-label="/paths/~1maintenance-info/get"></a>メンテナンス情報取得<!-- --> </h2><div><h5 class="sc-cZztgT fjywuK">query<!-- --> Parameters</h5><table class="sc-ekQdgj AJjSv"><tbody><tr class="last "><td kind="field" title="id" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">id</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo"> <!-- -->&lt;<!-- -->int64<!-- -->&gt;<!-- --> </span></div> <div><span class="sc-mLnbr hPYYRX"> <!-- -->Example:<!-- --> </span> <span class="sc-mLnbr sc-csuDXq hPYYRX cktsMe">id=1</span></div><div><div html="&lt;p&gt;メンテナンスID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>メンテナンスID</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB kfKSDW"><svg class="sc-cMHOsR fIBTiR" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">200<!-- --> </strong><div html="&lt;p&gt;メンテナンス情報を取得&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>メンテナンス情報を取得</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="get" class="sc-duJKf hyzpTe http-verb get">get</span><span class="sc-dItInd daOnZV">/maintenance-info</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/maintenance-info</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/maintenance-info</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:R9p5q:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9p5q:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:R9p5q:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9p5q:1" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9p5q:0" aria-labelledby="tab:R9p5q:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"releaseDate"</span>: <span class="token string">&quot;2023-10-01T12:00:00Z&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"title"</span>: <span class="token string">&quot;メンテナンスタイトル&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"comment"</span>: <span class="token string">&quot;メンテナンスの内容&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9p5q:1" aria-labelledby="tab:R9p5q:1"></div></div></div></div></div></div><div id="/paths/~1maintenance-info/patch" data-section-id="/paths/~1maintenance-info/patch" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1maintenance-info/patch" aria-label="/paths/~1maintenance-info/patch"></a>メンテナンス情報更新<!-- --> </h2><h5 class="sc-cZztgT fjywuK">Request Body schema: <span class="sc-dHHKhh cIfRxr">application/json</span></h5><div html="" class="sc-dODueM sc-eHKghg fhLETC hIcgHh"></div><table class="sc-ekQdgj AJjSv"><tbody><tr class=""><td kind="field" title="id" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">id</span></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo"> <!-- -->&lt;<!-- -->int64<!-- -->&gt;<!-- --> </span></div> <div><div html="&lt;p&gt;お知らせID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>お知らせID</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="releaseDate" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">releaseDate</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo"> <!-- -->&lt;<!-- -->date-time<!-- -->&gt;<!-- --> </span></div> <div><div html="&lt;p&gt;日付&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>日付</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="title" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">title</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span></div> <div><div html="&lt;p&gt;タイトル&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>タイトル</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="comment" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">comment</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span></div> <div><div html="&lt;p&gt;内容&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>内容</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB hzqENU" disabled=""><strong class="sc-bPKaLH bMwheE">204<!-- --> </strong><div html="&lt;p&gt;No Content&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>No Content</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="patch" class="sc-duJKf crWMcq http-verb patch">patch</span><span class="sc-dItInd daOnZV">/maintenance-info</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/maintenance-info</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/maintenance-info</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Request samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab:R996a:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R996a:0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R996a:0" aria-labelledby="tab:R996a:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"releaseDate"</span>: <span class="token string">&quot;2023-10-01T12:00:00Z&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"title"</span>: <span class="token string">&quot;メンテナンスタイトル&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"comment"</span>: <span class="token string">&quot;メンテナンスの内容&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-error react-tabs__tab--selected" role="tab" id="tab:R9p6a:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9p6a:0" tabindex="0" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9p6a:0" aria-labelledby="tab:R9p6a:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="/paths/~1maintenance-info/delete" data-section-id="/paths/~1maintenance-info/delete" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1maintenance-info/delete" aria-label="/paths/~1maintenance-info/delete"></a>メンテナンス情報削除<!-- --> </h2><div><h5 class="sc-cZztgT fjywuK">query<!-- --> Parameters</h5><table class="sc-ekQdgj AJjSv"><tbody><tr class="last "><td kind="field" title="id" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">id</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo"> <!-- -->&lt;<!-- -->int64<!-- -->&gt;<!-- --> </span></div> <div><span class="sc-mLnbr hPYYRX"> <!-- -->Example:<!-- --> </span> <span class="sc-mLnbr sc-csuDXq hPYYRX cktsMe">id=1</span></div><div><div html="&lt;p&gt;メンテナンスID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>メンテナンスID</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB hzqENU" disabled=""><strong class="sc-bPKaLH bMwheE">204<!-- --> </strong><div html="&lt;p&gt;No Content&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>No Content</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="delete" class="sc-duJKf emwLPa http-verb delete">delete</span><span class="sc-dItInd daOnZV">/maintenance-info</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/maintenance-info</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/maintenance-info</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-error react-tabs__tab--selected" role="tab" id="tab:R9p6q:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9p6q:0" tabindex="0" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9p6q:0" aria-labelledby="tab:R9p6q:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="/paths/~1service-status/get" data-section-id="/paths/~1service-status/get" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1service-status/get" aria-label="/paths/~1service-status/get"></a>サーバーステータス取得<!-- --> </h2><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB kfKSDW"><svg class="sc-cMHOsR fIBTiR" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">200<!-- --> </strong><div html="&lt;p&gt;成功&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>成功</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="get" class="sc-duJKf hyzpTe http-verb get">get</span><span class="sc-dItInd daOnZV">/service-status</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/service-status</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/service-status</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:R9p7a:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9p7a:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:R9p7a:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9p7a:1" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9p7a:0" aria-labelledby="tab:R9p7a:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token number">0</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9p7a:1" aria-labelledby="tab:R9p7a:1"></div></div></div></div></div></div><div id="/paths/~1service-status/patch" data-section-id="/paths/~1service-status/patch" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1service-status/patch" aria-label="/paths/~1service-status/patch"></a>サーバーステータス更新<!-- --> </h2><h5 class="sc-cZztgT fjywuK">Request Body schema: <span class="sc-dHHKhh cIfRxr">application/json</span></h5><div html="" class="sc-dODueM sc-eHKghg fhLETC hIcgHh"></div><table class="sc-ekQdgj AJjSv"><tbody><tr class="last "><td kind="field" title="status" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">status</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span></div> <div><div html="&lt;p&gt;サーバーステータス&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>サーバーステータス</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB hzqENU" disabled=""><strong class="sc-bPKaLH bMwheE">204<!-- --> </strong><div html="&lt;p&gt;No Content&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>No Content</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="patch" class="sc-duJKf crWMcq http-verb patch">patch</span><span class="sc-dItInd daOnZV">/service-status</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/service-status</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/service-status</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Request samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab:R997q:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R997q:0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R997q:0" aria-labelledby="tab:R997q:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token number">0</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-error react-tabs__tab--selected" role="tab" id="tab:R9p7q:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9p7q:0" tabindex="0" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9p7q:0" aria-labelledby="tab:R9p7q:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="/paths/~1user-list/get" data-section-id="/paths/~1user-list/get" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1user-list/get" aria-label="/paths/~1user-list/get"></a>ユーザー一覧取得<!-- --> </h2><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB kfKSDW"><svg class="sc-cMHOsR fIBTiR" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">200<!-- --> </strong><div html="&lt;p&gt;ユーザー一覧を取得する&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>ユーザー一覧を取得する</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="get" class="sc-duJKf hyzpTe http-verb get">get</span><span class="sc-dItInd daOnZV">/user-list</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/user-list</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/user-list</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:R9p8a:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9p8a:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:R9p8a:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9p8a:1" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9p8a:0" aria-labelledby="tab:R9p8a:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable "><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;jmdc-yamada&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;山田太郎&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"email"</span>: <span class="token string">&quot;<EMAIL>&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"emplrName"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"isAdmin"</span>: <span class="token boolean">true</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9p8a:1" aria-labelledby="tab:R9p8a:1"></div></div></div></div></div></div><div id="/paths/~1maintenance-info-list/get" data-section-id="/paths/~1maintenance-info-list/get" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1maintenance-info-list/get" aria-label="/paths/~1maintenance-info-list/get"></a>メンテナンス情報一覧取得<!-- --> </h2><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB kfKSDW"><svg class="sc-cMHOsR fIBTiR" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">200<!-- --> </strong><div html="&lt;p&gt;メンテナンス情報一覧を取得する&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>メンテナンス情報一覧を取得する</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="get" class="sc-duJKf hyzpTe http-verb get">get</span><span class="sc-dItInd daOnZV">/maintenance-info-list</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/maintenance-info-list</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/maintenance-info-list</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:R9p8q:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9p8q:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:R9p8q:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9p8q:1" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9p8q:0" aria-labelledby="tab:R9p8q:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable "><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"releaseDate"</span>: <span class="token string">&quot;2023-10-01T12:00:00Z&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"title"</span>: <span class="token string">&quot;メンテナンスタイトル&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"comment"</span>: <span class="token string">&quot;メンテナンスの内容&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9p8q:1" aria-labelledby="tab:R9p8q:1"></div></div></div></div></div></div><div id="/paths/~1confirm-regist-user-list-csv/post" data-section-id="/paths/~1confirm-regist-user-list-csv/post" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1confirm-regist-user-list-csv/post" aria-label="/paths/~1confirm-regist-user-list-csv/post"></a>ユーザー登録確認CSV作成<!-- --> </h2><h5 class="sc-cZztgT fjywuK">Request Body schema: <span class="sc-dHHKhh cIfRxr">application/json</span></h5><div html="" class="sc-dODueM sc-eHKghg fhLETC hIcgHh"></div><table class="sc-ekQdgj AJjSv"><tbody><tr class="last "><td kind="field" title="file" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">file</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo"> <!-- -->&lt;<!-- -->binary<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"></div></div></div></td></tr></tbody></table><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB kfKSDW"><svg class="sc-cMHOsR fIBTiR" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">200<!-- --> </strong><div html="&lt;p&gt;成功&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>成功</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="post" class="sc-duJKf fUSliQ http-verb post">post</span><span class="sc-dItInd daOnZV">/confirm-regist-user-list-csv</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/confirm-regist-user-list-csv</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/confirm-regist-user-list-csv</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Request samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab:R999a:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R999a:0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R999a:0" aria-labelledby="tab:R999a:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"file"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:R9p9a:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9p9a:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:R9p9a:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9p9a:1" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9p9a:0" aria-labelledby="tab:R9p9a:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token string">&quot;ok&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;ok&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9p9a:1" aria-labelledby="tab:R9p9a:1"></div></div></div></div></div></div><div id="/paths/~1regist-user-list/post" data-section-id="/paths/~1regist-user-list/post" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1regist-user-list/post" aria-label="/paths/~1regist-user-list/post"></a>ユーザー登録<!-- --> </h2><h5 class="sc-cZztgT fjywuK">Request Body schema: <span class="sc-dHHKhh cIfRxr">application/json</span></h5><div html="" class="sc-dODueM sc-eHKghg fhLETC hIcgHh"></div><table class="sc-ekQdgj AJjSv"><tbody><tr class="last "><td kind="field" title="file" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">file</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo"> <!-- -->&lt;<!-- -->binary<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"></div></div></div></td></tr></tbody></table><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB kfKSDW"><svg class="sc-cMHOsR fIBTiR" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">200<!-- --> </strong><div html="&lt;p&gt;成功&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>成功</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="post" class="sc-duJKf fUSliQ http-verb post">post</span><span class="sc-dItInd daOnZV">/regist-user-list</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/regist-user-list</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/regist-user-list</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Request samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab:R999q:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R999q:0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R999q:0" aria-labelledby="tab:R999q:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"file"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:R9p9q:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9p9q:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:R9p9q:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9p9q:1" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9p9q:0" aria-labelledby="tab:R9p9q:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token string">&quot;ok&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;ok&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9p9q:1" aria-labelledby="tab:R9p9q:1"></div></div></div></div></div></div><div id="/paths/~1confirm-send-mail-user-list/post" data-section-id="/paths/~1confirm-send-mail-user-list/post" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1confirm-send-mail-user-list/post" aria-label="/paths/~1confirm-send-mail-user-list/post"></a>メール送信確認<!-- --> </h2><h5 class="sc-cZztgT fjywuK">Request Body schema: <span class="sc-dHHKhh cIfRxr">application/json</span></h5><div html="" class="sc-dODueM sc-eHKghg fhLETC hIcgHh"></div><table class="sc-ekQdgj AJjSv"><tbody><tr class="last "><td kind="field" title="file" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">file</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo"> <!-- -->&lt;<!-- -->binary<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"></div></div></div></td></tr></tbody></table><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB kfKSDW"><svg class="sc-cMHOsR fIBTiR" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">200<!-- --> </strong><div html="&lt;p&gt;成功&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>成功</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="post" class="sc-duJKf fUSliQ http-verb post">post</span><span class="sc-dItInd daOnZV">/confirm-send-mail-user-list</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/confirm-send-mail-user-list</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/confirm-send-mail-user-list</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Request samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab:R99aa:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R99aa:0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R99aa:0" aria-labelledby="tab:R99aa:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"file"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:R9paa:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9paa:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:R9paa:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9paa:1" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9paa:0" aria-labelledby="tab:R9paa:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token string">&quot;ok&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;ok&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9paa:1" aria-labelledby="tab:R9paa:1"></div></div></div></div></div></div><div id="/paths/~1send-mail-user-list/post" data-section-id="/paths/~1send-mail-user-list/post" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1send-mail-user-list/post" aria-label="/paths/~1send-mail-user-list/post"></a>メール送信<!-- --> </h2><h5 class="sc-cZztgT fjywuK">Request Body schema: <span class="sc-dHHKhh cIfRxr">application/json</span></h5><div html="" class="sc-dODueM sc-eHKghg fhLETC hIcgHh"></div><table class="sc-ekQdgj AJjSv"><tbody><tr class="last "><td kind="field" title="file" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">file</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo"> <!-- -->&lt;<!-- -->binary<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"></div></div></div></td></tr></tbody></table><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB kfKSDW"><svg class="sc-cMHOsR fIBTiR" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">200<!-- --> </strong><div html="&lt;p&gt;成功&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>成功</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="post" class="sc-duJKf fUSliQ http-verb post">post</span><span class="sc-dItInd daOnZV">/send-mail-user-list</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/send-mail-user-list</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/send-mail-user-list</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Request samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab:R99aq:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R99aq:0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R99aq:0" aria-labelledby="tab:R99aq:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"file"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:R9paq:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9paq:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:R9paq:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9paq:1" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9paq:0" aria-labelledby="tab:R9paq:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token string">&quot;ok&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;ok&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9paq:1" aria-labelledby="tab:R9paq:1"></div></div></div></div></div></div><div id="/paths/~1notice-info-list/get" data-section-id="/paths/~1notice-info-list/get" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1notice-info-list/get" aria-label="/paths/~1notice-info-list/get"></a>お知らせ一覧取得<!-- --> </h2><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB kfKSDW"><svg class="sc-cMHOsR fIBTiR" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">200<!-- --> </strong><div html="&lt;p&gt;お知らせ一覧を取得する&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>お知らせ一覧を取得する</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="get" class="sc-duJKf hyzpTe http-verb get">get</span><span class="sc-dItInd daOnZV">/notice-info-list</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/notice-info-list</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/notice-info-list</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:R9pba:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9pba:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:R9pba:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9pba:1" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9pba:0" aria-labelledby="tab:R9pba:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable "><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"releaseDate"</span>: <span class="token string">&quot;2023-10-01T12:00:00Z&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"title"</span>: <span class="token string">&quot;メンテナンスタイトル&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"category"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"comment"</span>: <span class="token string">&quot;メンテナンスの内容&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"fileName"</span>: <span class="token string">&quot;sample.pdf&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9pba:1" aria-labelledby="tab:R9pba:1"></div></div></div></div></div></div><div id="/paths/~1notice-info/get" data-section-id="/paths/~1notice-info/get" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1notice-info/get" aria-label="/paths/~1notice-info/get"></a>お知らせ取得<!-- --> </h2><div><h5 class="sc-cZztgT fjywuK">query<!-- --> Parameters</h5><table class="sc-ekQdgj AJjSv"><tbody><tr class="last "><td kind="field" title="id" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">id</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo"> <!-- -->&lt;<!-- -->int64<!-- -->&gt;<!-- --> </span></div> <div><span class="sc-mLnbr hPYYRX"> <!-- -->Example:<!-- --> </span> <span class="sc-mLnbr sc-csuDXq hPYYRX cktsMe">id=1</span></div><div><div html="&lt;p&gt;お知らせID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>お知らせID</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB kfKSDW"><svg class="sc-cMHOsR fIBTiR" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">200<!-- --> </strong><div html="&lt;p&gt;成功&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>成功</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">400<!-- --> </strong><div html="&lt;p&gt;Bad Request&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Bad Request</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="get" class="sc-duJKf hyzpTe http-verb get">get</span><span class="sc-dItInd daOnZV">/notice-info</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/notice-info</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/notice-info</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:R9pbq:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9pbq:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:R9pbq:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9pbq:1" data-rttab="true">400</li><li class="tab-error" role="tab" id="tab:R9pbq:2" aria-selected="false" aria-disabled="false" aria-controls="panel:R9pbq:2" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9pbq:0" aria-labelledby="tab:R9pbq:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"releaseDate"</span>: <span class="token string">&quot;2023-10-01T12:00:00Z&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"title"</span>: <span class="token string">&quot;メンテナンスタイトル&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"category"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"comment"</span>: <span class="token string">&quot;メンテナンスの内容&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"fileName"</span>: <span class="token string">&quot;sample.pdf&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9pbq:1" aria-labelledby="tab:R9pbq:1"></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9pbq:2" aria-labelledby="tab:R9pbq:2"></div></div></div></div></div></div><div id="/paths/~1notice-info/post" data-section-id="/paths/~1notice-info/post" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1notice-info/post" aria-label="/paths/~1notice-info/post"></a>お知らせ作成<!-- --> </h2><h5 class="sc-cZztgT fjywuK">Request Body schema: <span class="sc-dHHKhh cIfRxr">multipart/form-data</span></h5><div html="" class="sc-dODueM sc-eHKghg fhLETC hIcgHh"></div><table class="sc-ekQdgj AJjSv"><tbody><tr class=""><td kind="field" title="id" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">id</span></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span></div> <div><div html="&lt;p&gt;お知らせID（新規作成の場合のみ）&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>お知らせID（新規作成の場合のみ）</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="releaseDate" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">releaseDate</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo"> <!-- -->&lt;<!-- -->date<!-- -->&gt;<!-- --> </span></div> <div><div html="&lt;p&gt;日付&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>日付</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="title" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">title</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span></div> <div><div html="&lt;p&gt;タイトル&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>タイトル</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="comment" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">comment</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span></div> <div><div html="&lt;p&gt;コメント&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>コメント</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="file" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">file</span></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo"> <!-- -->&lt;<!-- -->binary<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"></div></div></div></td></tr></tbody></table><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB hzqENU" disabled=""><strong class="sc-bPKaLH bMwheE">204<!-- --> </strong><div html="&lt;p&gt;No Content&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>No Content</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="post" class="sc-duJKf fUSliQ http-verb post">post</span><span class="sc-dItInd daOnZV">/notice-info</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/notice-info</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/notice-info</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-error react-tabs__tab--selected" role="tab" id="tab:R9pca:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9pca:0" tabindex="0" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9pca:0" aria-labelledby="tab:R9pca:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="/paths/~1notice-info/delete" data-section-id="/paths/~1notice-info/delete" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1notice-info/delete" aria-label="/paths/~1notice-info/delete"></a>お知らせ削除<!-- --> </h2><div><h5 class="sc-cZztgT fjywuK">query<!-- --> Parameters</h5><table class="sc-ekQdgj AJjSv"><tbody><tr class="last "><td kind="field" title="id" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">id</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo"> <!-- -->&lt;<!-- -->int64<!-- -->&gt;<!-- --> </span></div> <div><span class="sc-mLnbr hPYYRX"> <!-- -->Example:<!-- --> </span> <span class="sc-mLnbr sc-csuDXq hPYYRX cktsMe">id=1</span></div><div><div html="&lt;p&gt;お知らせID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>お知らせID</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB hzqENU" disabled=""><strong class="sc-bPKaLH bMwheE">204<!-- --> </strong><div html="&lt;p&gt;No Content&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>No Content</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="delete" class="sc-duJKf emwLPa http-verb delete">delete</span><span class="sc-dItInd daOnZV">/notice-info</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/notice-info</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/notice-info</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-error react-tabs__tab--selected" role="tab" id="tab:R9pcq:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9pcq:0" tabindex="0" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9pcq:0" aria-labelledby="tab:R9pcq:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="/paths/~1emplr-list/get" data-section-id="/paths/~1emplr-list/get" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1emplr-list/get" aria-label="/paths/~1emplr-list/get"></a>組合一覧取得<!-- --> </h2><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB kfKSDW"><svg class="sc-cMHOsR fIBTiR" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">200<!-- --> </strong><div html="&lt;p&gt;組合一覧を取得する&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>組合一覧を取得する</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="get" class="sc-duJKf hyzpTe http-verb get">get</span><span class="sc-dItInd daOnZV">/emplr-list</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/emplr-list</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/emplr-list</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:R9pda:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9pda:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:R9pda:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9pda:1" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9pda:0" aria-labelledby="tab:R9pda:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable "><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token number">100010</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"cd"</span>: <span class="token string">&quot;JMD&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"customEmplrName"</span>: <span class="token string">&quot;北海道健保&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"groupId"</span>: <span class="token number">1</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9pda:1" aria-labelledby="tab:R9pda:1"></div></div></div></div></div></div><div id="/paths/~1hierarchy-role/get" data-section-id="/paths/~1hierarchy-role/get" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1hierarchy-role/get" aria-label="/paths/~1hierarchy-role/get"></a>組合階層取得<!-- --> </h2><div><h5 class="sc-cZztgT fjywuK">query<!-- --> Parameters</h5><table class="sc-ekQdgj AJjSv"><tbody><tr class="last "><td kind="field" title="id" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">id</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span></div> <div><span class="sc-mLnbr hPYYRX"> <!-- -->Example:<!-- --> </span> <span class="sc-mLnbr sc-csuDXq hPYYRX cktsMe">id=1</span></div><div><div html="&lt;p&gt;階層情報ロールID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>階層情報ロールID</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB kfKSDW"><svg class="sc-cMHOsR fIBTiR" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">200<!-- --> </strong><div html="&lt;p&gt;組合階層を取得する&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>組合階層を取得する</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="get" class="sc-duJKf hyzpTe http-verb get">get</span><span class="sc-dItInd daOnZV">/hierarchy-role</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/hierarchy-role</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/hierarchy-role</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:R9pdq:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9pdq:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:R9pdq:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9pdq:1" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9pdq:0" aria-labelledby="tab:R9pdq:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"hierarchyRoleId"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"emplrId"</span>: <span class="token number">100010</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"hierarchyLevel"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"jigyosyoCd"</span>: <span class="token number">23</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9pdq:1" aria-labelledby="tab:R9pdq:1"></div></div></div></div></div></div><div id="/paths/~1hierarchy-role/put" data-section-id="/paths/~1hierarchy-role/put" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1hierarchy-role/put" aria-label="/paths/~1hierarchy-role/put"></a>組合階層追加<!-- --> </h2><h5 class="sc-cZztgT fjywuK">Request Body schema: <span class="sc-dHHKhh cIfRxr">application/json</span></h5><div html="" class="sc-dODueM sc-eHKghg fhLETC hIcgHh"></div><table class="sc-ekQdgj AJjSv"><tbody><tr class=""><td kind="field" title="hierarchyRoleId" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">hierarchyRoleId</span></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span></div> <div><div html="&lt;p&gt;階層情報ロールID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>階層情報ロールID</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="emplrId" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">emplrId</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span><span> <span class="sc-mLnbr sc-eMHqlA hPYYRX IbGQV"> <!-- -->= 5 characters<!-- --> </span></span></div> <div><div html="&lt;p&gt;組合ID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>組合ID</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="hierarchyLevel" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">hierarchyLevel</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span><span> <span class="sc-mLnbr sc-eMHqlA hPYYRX IbGQV"> <!-- -->&lt;= 4<!-- --> </span></span></div> <div><div html="&lt;p&gt;階層レベル&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>階層レベル</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="jigyosyoCd" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">jigyosyoCd</span></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">string</span></div> <div><div html="&lt;p&gt;事業所コード&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>事業所コード</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB hzqENU" disabled=""><strong class="sc-bPKaLH bMwheE">204<!-- --> </strong><div html="&lt;p&gt;No Content&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>No Content</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="put" class="sc-duJKf ixoukX http-verb put">put</span><span class="sc-dItInd daOnZV">/hierarchy-role</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/hierarchy-role</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/hierarchy-role</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Request samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab:R99ea:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R99ea:0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R99ea:0" aria-labelledby="tab:R99ea:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"hierarchyRoleId"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"emplrId"</span>: <span class="token number">100010</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"hierarchyLevel"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"jigyosyoCd"</span>: <span class="token number">23</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-error react-tabs__tab--selected" role="tab" id="tab:R9pea:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9pea:0" tabindex="0" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9pea:0" aria-labelledby="tab:R9pea:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="/paths/~1hierarchy-role/delete" data-section-id="/paths/~1hierarchy-role/delete" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1hierarchy-role/delete" aria-label="/paths/~1hierarchy-role/delete"></a>組合階層削除<!-- --> </h2><div><h5 class="sc-cZztgT fjywuK">query<!-- --> Parameters</h5><table class="sc-ekQdgj AJjSv"><tbody><tr class="last "><td kind="field" title="id" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">id</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span></div> <div><span class="sc-mLnbr hPYYRX"> <!-- -->Example:<!-- --> </span> <span class="sc-mLnbr sc-csuDXq hPYYRX cktsMe">id=1</span></div><div><div html="&lt;p&gt;階層情報ロールID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>階層情報ロールID</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB hzqENU" disabled=""><strong class="sc-bPKaLH bMwheE">204<!-- --> </strong><div html="&lt;p&gt;No Content&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>No Content</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="delete" class="sc-duJKf emwLPa http-verb delete">delete</span><span class="sc-dItInd daOnZV">/hierarchy-role</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/hierarchy-role</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/hierarchy-role</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-error react-tabs__tab--selected" role="tab" id="tab:R9peq:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9peq:0" tabindex="0" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9peq:0" aria-labelledby="tab:R9peq:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="/paths/~1hierarchy-role-all-list/get" data-section-id="/paths/~1hierarchy-role-all-list/get" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1hierarchy-role-all-list/get" aria-label="/paths/~1hierarchy-role-all-list/get"></a>階層情報ロールの一覧取得<!-- --> </h2><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB kfKSDW"><svg class="sc-cMHOsR fIBTiR" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">200<!-- --> </strong><div html="&lt;p&gt;組合階層を全て取得する&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>組合階層を全て取得する</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="get" class="sc-duJKf hyzpTe http-verb get">get</span><span class="sc-dItInd daOnZV">/hierarchy-role-all-list</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/hierarchy-role-all-list</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/hierarchy-role-all-list</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:R9pfa:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9pfa:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:R9pfa:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9pfa:1" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9pfa:0" aria-labelledby="tab:R9pfa:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable "><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"hierarchyRoleId"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"emplrId"</span>: <span class="token number">100010</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"hierarchyLevel"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"jigyosyoCd"</span>: <span class="token number">23</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9pfa:1" aria-labelledby="tab:R9pfa:1"></div></div></div></div></div></div><div id="/paths/~1hierarchy-role-list/get" data-section-id="/paths/~1hierarchy-role-list/get" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1hierarchy-role-list/get" aria-label="/paths/~1hierarchy-role-list/get"></a>階層情報ロールの一覧取得<!-- --> </h2><div><h5 class="sc-cZztgT fjywuK">query<!-- --> Parameters</h5><table class="sc-ekQdgj AJjSv"><tbody><tr class="last "><td kind="field" title="emplrId" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">emplrId</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span></div> <div><span class="sc-mLnbr hPYYRX"> <!-- -->Example:<!-- --> </span> <span class="sc-mLnbr sc-csuDXq hPYYRX cktsMe">emplrId=100001</span></div><div><div html="&lt;p&gt;組合ID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>組合ID</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB kfKSDW"><svg class="sc-cMHOsR fIBTiR" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">200<!-- --> </strong><div html="&lt;p&gt;組合階層を取得する&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>組合階層を取得する</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="get" class="sc-duJKf hyzpTe http-verb get">get</span><span class="sc-dItInd daOnZV">/hierarchy-role-list</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/hierarchy-role-list</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/hierarchy-role-list</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:R9pfq:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9pfq:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:R9pfq:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9pfq:1" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9pfq:0" aria-labelledby="tab:R9pfq:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable "><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"hierarchyRoleId"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"emplrId"</span>: <span class="token number">100010</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"hierarchyLevel"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"jigyosyoCd"</span>: <span class="token number">23</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9pfq:1" aria-labelledby="tab:R9pfq:1"></div></div></div></div></div></div><div id="/paths/~1jigyosyo-cd-list/get" data-section-id="/paths/~1jigyosyo-cd-list/get" class="sc-dDvxFM iYfDfc"><div class="sc-kuACkN eXZVyu"><div class="sc-gaZyOd kqlrlV"><h2 class="sc-jHbxoU hKnwYv"><a class="sc-jMLmsk ENkuP" href="#/paths/~1jigyosyo-cd-list/get" aria-label="/paths/~1jigyosyo-cd-list/get"></a>事業所CDリストの取得<!-- --> </h2><div><h5 class="sc-cZztgT fjywuK">query<!-- --> Parameters</h5><table class="sc-ekQdgj AJjSv"><tbody><tr class="last "><td kind="field" title="id" class="sc-ldgYGE sc-efgocT kjmvfQ gQnAKZ"><span class="sc-gztard bRicla"></span><span class="property-name">id</span><div class="sc-mLnbr sc-hXGFlK hPYYRX gKRDZM">required</div></td><td class="sc-ffiKcS iOAozH"><div><div><span class="sc-mLnbr sc-cGjkqA hPYYRX bMbGNb"></span><span class="sc-mLnbr sc-gVBvQd hPYYRX cJfKTo">integer</span></div> <div><span class="sc-mLnbr hPYYRX"> <!-- -->Example:<!-- --> </span> <span class="sc-mLnbr sc-csuDXq hPYYRX cktsMe">id=100001</span></div><div><div html="&lt;p&gt;組合ID&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>組合ID</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dCIkRk kJsvpq">Responses</h3><div><button class="sc-fCMaXB kfKSDW"><svg class="sc-cMHOsR fIBTiR" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">200<!-- --> </strong><div html="&lt;p&gt;事業所CDリストを取得する&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>事業所CDリストを取得する</p>
</div></button></div><div><button class="sc-fCMaXB klixnc"><svg class="sc-cMHOsR kzTRIF" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bPKaLH bMwheE">500<!-- --> </strong><div html="&lt;p&gt;Internal Server Error&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC hIcgHh sc-enuJcz ibumEn"><p>Internal Server Error</p>
</div></button></div></div></div><div class="sc-jqVXSH sc-gTrWKq jFuLzS eYokdz"><div class="sc-depuAN gxwpFr"><button class="sc-hUiJjc ciASxA"><span type="get" class="sc-duJKf hyzpTe http-verb get">get</span><span class="sc-dItInd daOnZV">/jigyosyo-cd-list</span><svg class="sc-cMHOsR hGIvNR" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-epjKGe dRMALb"><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;プロダクション API&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>プロダクション API</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>https://example.com/api</span>/jigyosyo-cd-list</div></div></div><div class="sc-dBXbGZ RtpSI"><div html="&lt;p&gt;開発用&lt;/p&gt;
" class="sc-dODueM sc-eHKghg fhLETC jlTrDX"><p>開発用</p>
</div><div tabindex="0" role="button"><div class="sc-hNMcCY emnlY"><span>http://{host}:{port}</span>/jigyosyo-cd-list</div></div></div></div></div><div><h3 class="sc-jZCRgm dKfWLZ"> <!-- -->Response samples<!-- --> </h3><div class="sc-bHCmUC eKwgKS" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab:R9pga:0" aria-selected="true" aria-disabled="false" aria-controls="panel:R9pga:0" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="tab:R9pga:1" aria-selected="false" aria-disabled="false" aria-controls="panel:R9pga:1" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel:R9pga:0" aria-labelledby="tab:R9pga:0"><div><div class="sc-bCFcQi OizcY"><span class="sc-fzvUyt btjQAM">Content type</span><div class="sc-bkdIYQ ipwpEI">application/json</div></div><div class="sc-ljFCIV ldAlKZ"><div class="sc-fdNlyR dmQZei"><div class="sc-kYYDBl jsYwEy"><button><div class="sc-fnxfcy bpcrOQ">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div class="sc-dODueM fhLETC sc-gdPHyQ hTzIMC"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable "><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"jigyosyoCd"</span>: <span class="token number">23</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"jigyosyoName"</span>: <span class="token string">&quot;北海道健保事業所名&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel:R9pga:1" aria-labelledby="tab:R9pga:1"></div></div></div></div></div></div></div><div class="sc-dJwLKM iSKrHn"></div></div></div>
      <script>
      const __redoc_state = {"menu":{"activeItemIdx":-1},"spec":{"data":{"openapi":"3.0.3","info":{"title":"健助+管理API","version":"1.0.0"},"servers":[{"url":"https://example.com/api","description":"プロダクション API"},{"url":"http://{host}:{port}","description":"開発用","variables":{"host":{"default":"localhost"},"port":{"default":"3000"}}}],"components":{"schemas":{"EmplrMaster":{"description":"組合マスタ","type":"object","required":["id","cd","name","secondaryUseProperty"],"properties":{"id":{"type":"integer","description":"組合ID","example":100001},"cd":{"type":"string","description":"組合CD","example":"JMD"},"name":{"type":"string"},"secondaryUseProperty":{"type":"integer"}}},"Emplr":{"description":"健康保険組合","type":"object","required":["id","customEmplrName","groupId"],"properties":{"id":{"type":"integer","description":"組合ID","maxLength":5,"minLength":5,"example":100010},"cd":{"type":"string","example":"JMD"},"customEmplrName":{"type":"string","description":"組合名","example":"北海道健保"},"groupId":{"type":"integer","description":"組合区分 1: 単一 2: 組合 3: 自治体","example":1}}},"HierarchyRole":{"description":"階層情報ロール","type":"object","required":["emplrId","hierarchyLevel"],"properties":{"hierarchyRoleId":{"type":"integer","description":"階層情報ロールID","example":1},"emplrId":{"type":"integer","description":"組合ID","maxLength":5,"minLength":5,"example":100010},"hierarchyLevel":{"type":"integer","description":"階層レベル","example":1,"maximum":4},"jigyosyoCd":{"type":"string","description":"事業所コード","example":23}}},"JigyosyoInfo":{"description":"事業所情報","type":"object","required":["jigyosyoCd","jigyosyoName"],"properties":{"jigyosyoCd":{"type":"string","description":"事業所コード","example":23},"jigyosyoName":{"type":"string","description":"事業所名","example":"北海道健保事業所名"}}},"User":{"type":"object","required":["name","email","hierarchyRoleId","isAdmin"],"properties":{"id":{"type":"integer","description":"ユーザーID"},"name":{"type":"string","format":"string","description":"ユーザー名","example":"山田太郎"},"email":{"type":"string","format":"email","example":"<EMAIL>","description":"メールアドレス"},"hierarchyRoleId":{"type":"integer","description":"階層情報ロールID","example":1},"isAdmin":{"type":"boolean","description":"組合ID","example":true},"comment":{"type":"string","description":"備考","example":"備考"}}},"UserSimple":{"type":"object","required":["id","name","email","emplrName","isAdmin"],"properties":{"id":{"type":"integer","description":"ユーザーID","example":"jmdc-yamada"},"name":{"type":"string","format":"string","description":"ユーザー名","example":"山田太郎"},"email":{"type":"string","format":"email","example":"<EMAIL>","description":"メールアドレス"},"emplrName":{"type":"string","description":"階層情報ロールID","example":1},"isAdmin":{"type":"boolean","description":"組合ID","example":true}}},"UserDetail":{"type":"object","properties":{"id":{"type":"integer","description":"ユーザーID","example":"jmdc-yamada"},"name":{"type":"string","format":"string","description":"ユーザー名","example":"山田太郎"},"email":{"type":"string","format":"email","example":"<EMAIL>","description":"メールアドレス"},"emplrCd":{"type":"string","description":"組合ID","example":"emplr-123"},"emplrName":{"type":"string","description":"組合ID","example":"emplr-123"},"isAdmin":{"type":"boolean","description":"権限","example":false},"comment":{"type":"string","description":"備考","example":"備考"},"auth0UserId":{"type":"string","description":"Auth0のユーザーID","example":"auth0|123456789"},"hierarchyLevel":{"type":"integer","description":"階層レベル","example":1},"jigyosyoCd":{"type":"string","description":"事業所コード","example":23},"isBlocked":{"type":"boolean","description":"ブロック状態","example":false},"isMfa":{"type":"boolean","description":"MFA状態","example":true},"lastLogin":{"type":"string","format":"date-time","description":"最終ログイン日時","example":"2020-01-31T23:59:59+09:00"}},"required":["id","name","email","emplrCd","emplrName","isAdmin","comment","hierarchyLevel","jigyosyoCd","auth0UserId","isBlocked","isMfa","lastLogin"]},"NoticeInfo":{"type":"object","required":["id"],"properties":{"id":{"type":"integer","format":"int64","description":"お知らせID","example":1},"releaseDate":{"type":"string","format":"date-time","description":"日付","example":"2023-10-01T12:00:00Z"},"title":{"type":"string","description":"タイトル","example":"メンテナンスタイトル"},"category":{"type":"integer"},"comment":{"type":"string","description":"内容","example":"メンテナンスの内容"},"fileName":{"type":"string","description":"ファイル名","example":"sample.pdf"}}},"MaintenanceInfo":{"type":"object","required":["releaseDate","title","comment"],"properties":{"id":{"type":"integer","format":"int64","description":"お知らせID","example":1},"releaseDate":{"type":"string","format":"date-time","description":"日付","example":"2023-10-01T12:00:00Z"},"title":{"type":"string","description":"タイトル","example":"メンテナンスタイトル"},"comment":{"type":"string","description":"内容","example":"メンテナンスの内容"}}},"ErrorResponse":{"type":"object","properties":{"message":{"type":"string","description":"エラーメッセージ"}},"required":["message"]}}},"paths":{"/master-emplr-list":{"get":{"summary":"組合マスターリストの取得","responses":{"200":{"description":"成功","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/EmplrMaster"}}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}}},"/emplr":{"put":{"summary":"組合作成","requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/Emplr"}}}},"responses":{"201":{"description":"Created"},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}},"409":{"description":"Conflict","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}},"patch":{"summary":"組合更新","requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/Emplr"}}}},"responses":{"204":{"description":"No Content"},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}},"get":{"summary":"組合取得","parameters":[{"in":"query","name":"id","required":true,"schema":{"type":"integer","description":"組合ID","example":100001}}],"responses":{"200":{"description":"成功","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Emplr"}}}},"204":{"description":"No Content"},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}},"delete":{"summary":"組合削除","parameters":[{"in":"query","name":"id","required":true,"schema":{"type":"integer","description":"組合ID","example":100001}}],"responses":{"204":{"description":"No Content"},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}}},"/user":{"put":{"summary":"ユーザー作成","requestBody":{"content":{"application/json":{"schema":{"type":"object","$ref":"#/components/schemas/User"}}}},"responses":{"204":{"description":"No Content"},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}},"patch":{"summary":"ユーザー更新","requestBody":{"content":{"application/json":{"schema":{"type":"object","$ref":"#/components/schemas/User"}}}},"responses":{"204":{"description":"No Content"},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}},"get":{"summary":"ユーザー取得","parameters":[{"in":"query","name":"id","required":true,"schema":{"type":"integer","description":"ユーザーID","example":1}}],"responses":{"200":{"description":"成功","content":{"application/json":{"schema":{"type":"object","$ref":"#/components/schemas/UserDetail"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}},"delete":{"summary":"ユーザー削除","parameters":[{"in":"query","name":"id","required":true,"schema":{"type":"integer","description":"ユーザーID","example":1}}],"responses":{"204":{"description":"No Content"},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}}},"/maintenance-info":{"put":{"summary":"メンテナンス情報作成","requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/MaintenanceInfo"}}}},"responses":{"204":{"description":"No Content"},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}},"get":{"summary":"メンテナンス情報取得","parameters":[{"in":"query","name":"id","required":true,"schema":{"type":"integer","format":"int64","description":"メンテナンスID","example":1}}],"responses":{"200":{"description":"メンテナンス情報を取得","content":{"application/json":{"schema":{"$ref":"#/components/schemas/MaintenanceInfo"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}},"patch":{"summary":"メンテナンス情報更新","requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/MaintenanceInfo"}}}},"responses":{"204":{"description":"No Content"},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}},"delete":{"summary":"メンテナンス情報削除","parameters":[{"in":"query","name":"id","required":true,"schema":{"type":"integer","format":"int64","description":"メンテナンスID","example":1}}],"responses":{"204":{"description":"No Content"},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}}},"/service-status":{"get":{"summary":"サーバーステータス取得","responses":{"200":{"description":"成功","content":{"application/json":{"schema":{"type":"object","required":["status"],"properties":{"status":{"type":"integer","description":"サーバーステータス","example":0}}}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}},"patch":{"summary":"サーバーステータス更新","requestBody":{"content":{"application/json":{"schema":{"type":"object","required":["status"],"properties":{"status":{"type":"integer","description":"サーバーステータス","example":0}}}}}},"responses":{"204":{"description":"No Content"},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}}},"/user-list":{"get":{"summary":"ユーザー一覧取得","responses":{"200":{"description":"ユーザー一覧を取得する","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/UserSimple"}}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}}},"/maintenance-info-list":{"get":{"summary":"メンテナンス情報一覧取得","responses":{"200":{"description":"メンテナンス情報一覧を取得する","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/MaintenanceInfo"}}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}}},"/confirm-regist-user-list-csv":{"post":{"summary":"ユーザー登録確認CSV作成","requestBody":{"content":{"application/json":{"schema":{"type":"object","required":["file"],"properties":{"file":{"type":"string","format":"binary"}}}}}},"responses":{"200":{"description":"成功","content":{"application/json":{"schema":{"type":"object","properties":{"status":{"type":"string","example":"ok"},"message":{"type":"string","example":"ok"}}}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}}},"/regist-user-list":{"post":{"summary":"ユーザー登録","requestBody":{"content":{"application/json":{"schema":{"type":"object","required":["file"],"properties":{"file":{"type":"string","format":"binary"}}}}}},"responses":{"200":{"description":"成功","content":{"application/json":{"schema":{"type":"object","properties":{"status":{"type":"string","example":"ok"},"message":{"type":"string","example":"ok"}}}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}}},"/confirm-send-mail-user-list":{"post":{"summary":"メール送信確認","requestBody":{"content":{"application/json":{"schema":{"type":"object","required":["file"],"properties":{"file":{"type":"string","format":"binary"}}}}}},"responses":{"200":{"description":"成功","content":{"application/json":{"schema":{"type":"object","properties":{"status":{"type":"string","example":"ok"},"message":{"type":"string","example":"ok"}}}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}}},"/send-mail-user-list":{"post":{"summary":"メール送信","requestBody":{"content":{"application/json":{"schema":{"type":"object","required":["file"],"properties":{"file":{"type":"string","format":"binary"}}}}}},"responses":{"200":{"description":"成功","content":{"application/json":{"schema":{"type":"object","properties":{"status":{"type":"string","example":"ok"},"message":{"type":"string","example":"ok"}}}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}}},"/notice-info-list":{"get":{"summary":"お知らせ一覧取得","responses":{"200":{"description":"お知らせ一覧を取得する","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/NoticeInfo"}}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}}},"/notice-info":{"get":{"summary":"お知らせ取得","parameters":[{"in":"query","name":"id","required":true,"schema":{"type":"integer","format":"int64","description":"お知らせID","example":1}}],"responses":{"200":{"description":"成功","content":{"application/json":{"schema":{"$ref":"#/components/schemas/NoticeInfo"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}},"post":{"summary":"お知らせ作成","requestBody":{"content":{"multipart/form-data":{"schema":{"type":"object","required":["releaseDate","title","comment"],"properties":{"id":{"type":"integer","description":"お知らせID（新規作成の場合のみ）","example":1},"releaseDate":{"type":"string","format":"date","description":"日付","example":"2024-10-01"},"title":{"type":"string","description":"タイトル","example":"アップデート情報"},"comment":{"type":"string","description":"コメント","example":"メンテナンスは明日です"},"file":{"type":"string","format":"binary"}}}}}},"responses":{"204":{"description":"No Content"},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}},"delete":{"summary":"お知らせ削除","parameters":[{"in":"query","name":"id","required":true,"schema":{"type":"integer","format":"int64","description":"お知らせID","example":1}}],"responses":{"204":{"description":"No Content"},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}}},"/emplr-list":{"get":{"summary":"組合一覧取得","responses":{"200":{"description":"組合一覧を取得する","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/Emplr"}}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}}},"/hierarchy-role":{"get":{"summary":"組合階層取得","parameters":[{"in":"query","name":"id","required":true,"schema":{"type":"integer","description":"階層情報ロールID","example":1}}],"responses":{"200":{"description":"組合階層を取得する","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HierarchyRole"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}},"put":{"summary":"組合階層追加","requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/HierarchyRole"}}}},"responses":{"204":{"description":"No Content"},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}},"delete":{"summary":"組合階層削除","parameters":[{"in":"query","name":"id","required":true,"schema":{"type":"integer","description":"階層情報ロールID","example":1}}],"responses":{"204":{"description":"No Content"},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}}},"/hierarchy-role-all-list":{"get":{"summary":"階層情報ロールの一覧取得","responses":{"200":{"description":"組合階層を全て取得する","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/HierarchyRole"}}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}}},"/hierarchy-role-list":{"get":{"summary":"階層情報ロールの一覧取得","parameters":[{"in":"query","name":"emplrId","required":true,"schema":{"type":"integer","description":"組合ID","example":100001}}],"responses":{"200":{"description":"組合階層を取得する","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/HierarchyRole"}}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}}},"/jigyosyo-cd-list":{"get":{"summary":"事業所CDリストの取得","parameters":[{"in":"query","name":"id","required":true,"schema":{"type":"integer","description":"組合ID","example":100001}}],"responses":{"200":{"description":"事業所CDリストを取得する","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/JigyosyoInfo"}}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ErrorResponse"}}}}}}}}}},"searchIndex":{"store":["/paths/~1master-emplr-list/get","/paths/~1emplr/put","/paths/~1emplr/patch","/paths/~1emplr/get","/paths/~1emplr/delete","/paths/~1user/put","/paths/~1user/patch","/paths/~1user/get","/paths/~1user/delete","/paths/~1maintenance-info/put","/paths/~1maintenance-info/get","/paths/~1maintenance-info/patch","/paths/~1maintenance-info/delete","/paths/~1service-status/get","/paths/~1service-status/patch","/paths/~1user-list/get","/paths/~1maintenance-info-list/get","/paths/~1confirm-regist-user-list-csv/post","/paths/~1regist-user-list/post","/paths/~1confirm-send-mail-user-list/post","/paths/~1send-mail-user-list/post","/paths/~1notice-info-list/get","/paths/~1notice-info/get","/paths/~1notice-info/post","/paths/~1notice-info/delete","/paths/~1emplr-list/get","/paths/~1hierarchy-role/get","/paths/~1hierarchy-role/put","/paths/~1hierarchy-role/delete","/paths/~1hierarchy-role-all-list/get","/paths/~1hierarchy-role-list/get","/paths/~1jigyosyo-cd-list/get"],"index":{"version":"2.3.9","fields":["title","description"],"fieldVectors":[["title/0",[0,0.079]],["description/0",[1,3.091]],["title/1",[0,0.079]],["description/1",[2,1.992]],["title/2",[0,0.079]],["description/2",[2,1.992]],["title/3",[0,0.079]],["description/3",[2,1.992]],["title/4",[0,0.079]],["description/4",[2,1.992]],["title/5",[0,0.079]],["description/5",[3,1.992]],["title/6",[0,0.079]],["description/6",[3,1.992]],["title/7",[0,0.079]],["description/7",[3,1.992]],["title/8",[0,0.079]],["description/8",[3,1.992]],["title/9",[0,0.079]],["description/9",[4,1.992]],["title/10",[0,0.079]],["description/10",[4,1.992]],["title/11",[0,0.079]],["description/11",[4,1.992]],["title/12",[0,0.079]],["description/12",[4,1.992]],["title/13",[0,0.079]],["description/13",[5,2.58]],["title/14",[0,0.079]],["description/14",[5,2.58]],["title/15",[0,0.079]],["description/15",[6,3.091]],["title/16",[0,0.079]],["description/16",[7,3.091]],["title/17",[8,3.091]],["description/17",[9,3.091]],["title/18",[0,0.079]],["description/18",[10,3.091]],["title/19",[0,0.079]],["description/19",[11,3.091]],["title/20",[0,0.079]],["description/20",[12,3.091]],["title/21",[0,0.079]],["description/21",[13,3.091]],["title/22",[0,0.079]],["description/22",[14,2.244]],["title/23",[0,0.079]],["description/23",[14,2.244]],["title/24",[0,0.079]],["description/24",[14,2.244]],["title/25",[0,0.079]],["description/25",[15,3.091]],["title/26",[0,0.079]],["description/26",[16,2.244]],["title/27",[0,0.079]],["description/27",[16,2.244]],["title/28",[0,0.079]],["description/28",[16,2.244]],["title/29",[0,0.079]],["description/29",[17,3.091]],["title/30",[0,0.079]],["description/30",[18,3.091]],["title/31",[19,3.091]],["description/31",[20,3.091]]],"invertedIndex":[["",{"_index":0,"title":{"0":{},"1":{},"2":{},"3":{},"4":{},"5":{},"6":{},"7":{},"8":{},"9":{},"10":{},"11":{},"12":{},"13":{},"14":{},"15":{},"16":{},"18":{},"19":{},"20":{},"21":{},"22":{},"23":{},"24":{},"25":{},"26":{},"27":{},"28":{},"29":{},"30":{}},"description":{}}],["cd",{"_index":19,"title":{"31":{}},"description":{}}],["confirm-regist-user-list-csv",{"_index":9,"title":{},"description":{"17":{}}}],["confirm-send-mail-user-list",{"_index":11,"title":{},"description":{"19":{}}}],["csv",{"_index":8,"title":{"17":{}},"description":{}}],["emplr",{"_index":2,"title":{},"description":{"1":{},"2":{},"3":{},"4":{}}}],["emplr-list",{"_index":15,"title":{},"description":{"25":{}}}],["hierarchy-rol",{"_index":16,"title":{},"description":{"26":{},"27":{},"28":{}}}],["hierarchy-role-all-list",{"_index":17,"title":{},"description":{"29":{}}}],["hierarchy-role-list",{"_index":18,"title":{},"description":{"30":{}}}],["jigyosyo-cd-list",{"_index":20,"title":{},"description":{"31":{}}}],["maintenance-info",{"_index":4,"title":{},"description":{"9":{},"10":{},"11":{},"12":{}}}],["maintenance-info-list",{"_index":7,"title":{},"description":{"16":{}}}],["master-emplr-list",{"_index":1,"title":{},"description":{"0":{}}}],["notice-info",{"_index":14,"title":{},"description":{"22":{},"23":{},"24":{}}}],["notice-info-list",{"_index":13,"title":{},"description":{"21":{}}}],["regist-user-list",{"_index":10,"title":{},"description":{"18":{}}}],["send-mail-user-list",{"_index":12,"title":{},"description":{"20":{}}}],["service-statu",{"_index":5,"title":{},"description":{"13":{},"14":{}}}],["user",{"_index":3,"title":{},"description":{"5":{},"6":{},"7":{},"8":{}}}],["user-list",{"_index":6,"title":{},"description":{"15":{}}}]],"pipeline":[]}},"options":{}};

      var container = document.getElementById('redoc');
      Redoc.hydrate(__redoc_state, container);

      </script>
</body>

</html>
