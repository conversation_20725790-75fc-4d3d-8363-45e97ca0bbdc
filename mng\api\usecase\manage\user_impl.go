package usecase

import (
	"context"

	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/model"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/repository"
	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

type User struct {
	userRepo repository.UserRepository
}

func NewUser(UserRepo repository.UserRepository) UserUseCase {
	return &User{
		UserRepo,
	}
}

func (u User) PutUser(ctx context.Context, inputUser oapi.User, auth0Id string) error {
	role := 0
	if inputUser.IsAdmin {
		role = 99
	}
	err := u.userRepo.Create(ctx, model.User{
		Name:            inputUser.Name,
		HierarchyRoleId: inputUser.HierarchyRoleId,
		Auth0Id:         auth0Id,
		Email:           string(inputUser.Email),
		Role:            role,
	})
	if err != nil {
		return err
	}
	return nil
}

func (u User) GetUser(ctx context.Context, id int) (oapi.GetUserResponseObject, error) {
	user, err := u.userRepo.GetUserDetail(ctx, id)
	if err != nil {
		return nil, err
	}
	userDetail := oapi.UserDetail{
		Id:          user.UserID,
		Name:        user.Name,
		Email:       openapi_types.Email(user.Email),
		Auth0UserId: user.Auth0Id,
		Comment:     user.Comment,

		IsAdmin: user.Role == 1,

		IsBlocked: user.IsBlocked,
		IsMfa:     user.IsMfa,
		LastLogin: user.LastLogin,

		EmplrCd:   user.EmplrCD,
		EmplrName: user.CustomEmplrName,

		HierarchyLevel: user.HierarchyLevel,
		JigyosyoCd:     user.JigyosyoCD,
	}

	return oapi.GetUser200JSONResponse(userDetail), nil
}

func (u User) GetListUser(ctx context.Context) (oapi.GetUserListResponseObject, error) {
	UserList, err := u.userRepo.GetList(ctx)
	if err != nil {
		return nil, err
	}

	results := make([]oapi.UserSimple, 0, len(UserList))
	for _, row := range UserList {
		maintenanceInfo := oapi.UserSimple{
			Id:        row.UserID,
			Name:      row.Name,
			Email:     openapi_types.Email(row.Email),
			EmplrName: row.EmplrName,
			IsAdmin:   row.Role == 1,
		}
		results = append(results, maintenanceInfo)
	}

	return oapi.GetUserList200JSONResponse(results), nil
}

func (u User) PatchUser(ctx context.Context, request oapi.PatchUserRequestObject) error {
	user := oapi.PatchUserJSONRequestBody(*request.Body)
	return u.userRepo.Update(ctx, model.User{
		Name: user.Name,
		// GroupID: 0,
	})
}

func (u User) DeleteUser(ctx context.Context, id int) error {
	return u.userRepo.Delete(ctx, id)
}
