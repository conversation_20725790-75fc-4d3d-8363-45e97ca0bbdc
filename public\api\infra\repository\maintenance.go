package repository

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/db"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/errof"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/model"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/repository"
)

type maintenance struct {
	dbClient db.Client
}

func NewMaintenance(dbClient db.Client) repository.MaintenanceRepository {
	return &maintenance{dbClient}
}

func (r *maintenance) GetMaintenanceInfo(ctx context.Context, filter model.MaintenanceListFilter) ([]*model.MaintenanceInfo, int, error) {
	var maintenance []dbmodels.MNGMaintenanceInfo

	query := r.dbClient.DB.NewSelect().Model(&maintenance)

	if filter.Keyword != nil && *filter.Keyword != "" {
		keyword := "%" + *filter.Keyword + "%"
		query = query.Where("title ILIKE ? OR comment ILIKE ?", keyword, keyword)
	}

	total, err := query.Count(ctx)
	if err != nil {
		return nil, 0, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	offset := (filter.Page - 1) * filter.Limit
	query = query.Limit(filter.Limit).Offset(offset).Order("release_date DESC")

	err = query.Scan(ctx)
	if err != nil {
		return nil, 0, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	result := make([]*model.MaintenanceInfo, 0, len(maintenance))
	for _, m := range maintenance {
		maintenanceInfo := &model.MaintenanceInfo{
			ID:          int(m.ID),
			Title:       m.Title,
			Comment:     m.Comment,
			ReleaseDate: m.Date,
			RegUser:     m.RegUser,
			RegDate:     m.Date,
			UpdUser:     m.UpdUser,
			UpdDate:     m.Date,
		}
		result = append(result, maintenanceInfo)
	}

	return result, total, nil
}

func (r *maintenance) GetMaintenanceInfoByID(ctx context.Context, id int) (*model.MaintenanceInfo, error) {
	var maintenance dbmodels.MNGMaintenanceInfo

	err := r.dbClient.DB.NewSelect().
		Model(&maintenance).
		Where("id = ?", id).
		Scan(ctx)

	if err != nil {
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	result := &model.MaintenanceInfo{
		ID:          int(maintenance.ID),
		Title:       maintenance.Title,
		Comment:     maintenance.Comment,
		ReleaseDate: maintenance.Date,
		RegUser:     maintenance.RegUser,
		RegDate:     maintenance.Date,
		UpdUser:     maintenance.UpdUser,
		UpdDate:     maintenance.Date,
	}

	return result, nil
}
