// Package parameterstore
package parameterstore

import (
	"context"
	"log"
	"log/slog"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/ssm"
)

func GetParameterStoreString(ctx context.Context, key string, withDecription bool) string {
	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("ap-northeast-1"))
	if err != nil {
		log.Fatalf("unable to load SDK config, %v", err)
	}

	// SSMクライアントの作成
	ssmClient := ssm.NewFromConfig(cfg)

	// パラメータの取得
	paramInput := &ssm.GetParameterInput{
		Name:           aws.String(key),
		WithDecryption: aws.Bool(true),
	}

	result, err := ssmClient.GetParameter(ctx, paramInput)
	if err != nil {
		slog.Error("unable to load SDK config", "error", err)
	}

	return *result.Parameter.Value
}
