openapi: 3.0.3
info:
  title: 健助+API
  version: "1.0.0"

servers:
  - url: https://example.com/api
    description: プロダクション API
  - url: http://{host}:{port}
    description: 開発用
    variables:
      host:
        default: localhost
      port:
        default: '8080'
components:
  schemas:
    Emplr:
      description: 健康保険組合
      type: object
      required:
        - id
        - customEmplrName
        - groupId
      properties:
        id:
          type: integer
          description: 組合ID
          maxLength: 5
          minLength: 5
          example: 100010
        cd:
          type: string
          example: JMD
        customEmplrName:
          type: string
          description: "組合名"
          example: 北海道健保
        groupId:
          type: integer
          description: "組合区分 1: 単一 2: 組合 3: 自治体"
          example: 1


paths:
  /emplr:
    put:
      summary: 組合作成
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Emplr"
      responses:
        '201':
          description: Created
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
        '409':
          description: Conflict
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"

    patch:
      summary: 組合更新
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Emplr"
      responses:
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"

    get:
      summary: 組合取得
      parameters:
      - in: query
        name: id
        required: true
        schema:
          type: integer
          description: 組合ID
          example: 100001
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Emplr"
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"

    delete:
      summary: 組合削除
      parameters:
      - in: query
        name: id
        required: true
        schema:
          type: integer
          description: 組合ID
          example: 100001
      responses:
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "./schemas/common/ErrorResponse.yml"
