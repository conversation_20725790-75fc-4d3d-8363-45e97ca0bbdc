// Package repository is implements DB connection.
package repository

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/model"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/repository"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/authenticator"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/db"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/errof"
)

type user struct {
	dbClient    db.Client
	auth0Client authenticator.Authenticator
}

func NewUser(dbClient db.Client, auth0Client authenticator.Authenticator) repository.UserRepository {
	return &user{dbClient, auth0Client}
}

func (r *user) Create(ctx context.Context, model model.User) error {
	var input = dbmodels.MNGUser{
		Auth0Id:         model.Auth0Id,
		HierarchyRoleID: model.HierarchyRoleId,
		Name:            model.Name,
		Email:           model.Email,
		Role:            model.Role,
	}

	_, err := r.dbClient.DB.NewInsert().Model(&input).Exec(ctx)
	if err != nil {
		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	return nil
}

func (r *user) Get(ctx context.Context, id int) (*model.User, error) {
	var result = dbmodels.MNGUser{}

	if err := r.dbClient.DB.NewSelect().Model(&result).Where("user_id = ?", id).Scan(ctx); err != nil {

		// エラーが発生しているのでエラー情報を返す
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	ret := model.NewUser(result)
	return &ret, nil
}
func (r *user) GetUserDetail(ctx context.Context, id int) (*model.UserDetail, error) {
	var result = dbmodels.MNGUserDetail{}

	if err := r.dbClient.DB.NewSelect().Model(&result).Where("user_id = ?", id).
		Column("user_id").
		Column("auth0_id").
		Column("name").
		Column("email").
		Column("role").
		Column("comment").
		Column("e.custom_emplr_name").
		Column("e.emplr_cd").
		Column("h.hierarchy_level").
		Column("h.jigyosyo_cd").
		Join("JOIN kensuke_plus.tbl_mng_hierarchy_role as h").
		JoinOn("h.hierarchy_role_id = tmu.hierarchy_role_id").
		Join("JOIN kensuke_plus.tbl_mng_emplr as e").
		JoinOn("e.emplr_id = h.emplr_id").Scan(ctx); err != nil {

		// エラーが発生しているのでエラー情報を返す
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	ret := model.NewUserDetail(result)
	return &ret, nil
}

func (r *user) GetUserByEmailExist(ctx context.Context, email string) (bool, error) {
	var result = dbmodels.MNGUser{}

	exists, err := r.dbClient.DB.NewSelect().Model(&result).Where("email = ?", email).Exists(ctx)
	if err != nil {
		return false, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	return exists, nil
}
func (r *user) GetUserByEmail(ctx context.Context, email string) (*model.User, error) {
	var result = dbmodels.MNGUser{}

	err := r.dbClient.DB.NewSelect().Model(&result).Where("email = ?", email).Scan(ctx)
	if err != nil {
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	user := model.NewUser(result)
	return &user, nil
}
func (r *user) GetList(ctx context.Context) ([]model.UserSimple, error) {
	var results []dbmodels.MNGUserSimple

	if err := r.dbClient.DB.NewSelect().Model(&results).
		Column("user_id", "name", "email", "role").
		Column("e.custom_emplr_name", "e.emplr_cd").
		Column("h.hierarchy_level").
		Join("JOIN kensuke_plus.tbl_mng_hierarchy_role as h").
		JoinOn("h.hierarchy_role_id = tmu.hierarchy_role_id").
		Join("JOIN kensuke_plus.tbl_mng_emplr as e").
		JoinOn("e.emplr_id = h.emplr_id").Scan(ctx); err != nil {

		// エラーが発生しているのでエラー情報を返す
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	return model.NewUserSimples(results), nil
}

func (r *user) Update(ctx context.Context, model model.User) error {
	var input = dbmodels.MNGUser{
		// EmplrID:         model.EmplrID,
		// CustomEmplrName: model.CustomEmplrName,
		// GroupID:         model.GroupID,
	}
	_, err := r.dbClient.DB.NewUpdate().Model(&input).Where("id = ?", model.UserID).Exec(ctx)
	if err != nil {
		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	return nil
}

func (r *user) Delete(ctx context.Context, id int) error {
	model := dbmodels.MNGEmplr{
		EmplrID: id,
	}
	_, err := r.dbClient.DB.NewDelete().Model(&model).WherePK().Exec(ctx)
	if err != nil {
		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	return nil
}
