// Package usecase is UseCase definition and implements.
package usecase

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"log/slog"
	"mime/multipart"
	"strconv"

	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/model"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/repository"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/authenticator"
	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
	openapi_types "github.com/oapi-codegen/runtime/types"
	"github.com/pkg/errors"
)

type csvImport struct {
	emplrRepo     repository.EmplrRepository
	hierarchyRepo repository.HierarchyRoleRepository
	userRepo      repository.UserRepository
	auth0Client   authenticator.Authenticator
}

func NewCsvImport(emplrRepo repository.EmplrRepository, hierarchyRepo repository.HierarchyRoleRepository, userRepo repository.UserRepository, auth0Client authenticator.Authenticator) CsvImportUseCase {
	return &csvImport{
		emplrRepo,
		hierarchyRepo,
		userRepo,
		auth0Client,
	}
}

func (u csvImport) ConfirmRegistUserList(ctx context.Context, request oapi.PostConfirmRegistUserListRequestObject) ([]oapi.RegistCsvResult, []string, error) {
	records, resultErrors, err := u.Open(request.Body)
	if err != nil {
		slog.Error("CSVファイルのオープンに失敗", "error", err)
		return nil, nil, errors.Wrap(err, "CSVファイルのオープンに失敗しました")
	}
	if len(resultErrors) > 0 {
		slog.Error("CSVファイルのフォーマットチェックに失敗", "errors", resultErrors)
		return nil, resultErrors, errors.New("CSVファイルのフォーマットチェックに失敗しました")
	}
	slog.Info("CSVファイルのフォーマットチェック成功", "record", records)

	resultData, resultErrors := u.checkData(ctx, records)
	if len(resultErrors) > 0 {
		slog.Error("CSVファイルのフォーマットチェックに失敗", "errors", resultErrors)
		return nil, resultErrors, errors.New("CSVファイルのフォーマットチェックに失敗しました")
	}
	return resultData, resultErrors, err
}

func (u csvImport) ConfirmSendMailUserList(_ context.Context, _ oapi.PostConfirmSendMailUserListRequestObject) ([]oapi.RegistCsvResult, []string, error) {
	return nil, nil, nil
}
func (u csvImport) RegistUserList(ctx context.Context, request oapi.PostRegistUserListRequestObject) ([]oapi.RegistCsvResult, []string, error) {
	records, resultErrors, err := u.Open(request.Body)
	if err != nil {
		slog.Error("CSVファイルのオープンに失敗", "error", err)
		return nil, nil, errors.Wrap(err, "CSVファイルのオープンに失敗しました")
	}
	if len(resultErrors) > 0 {
		slog.Error("CSVファイルのフォーマットチェックに失敗", "errors", resultErrors)
		return nil, resultErrors, errors.New("CSVファイルのフォーマットチェックに失敗しました")
	}
	slog.Info("CSVファイルのフォーマットチェック成功", "record", records)

	resultData, resultErrors := u.checkData(ctx, records)
	if len(resultErrors) > 0 {
		slog.Error("CSVファイルのフォーマットチェックに失敗", "errors", resultErrors)
		return nil, resultErrors, errors.New("CSVファイルのフォーマットチェックに失敗しました")
	}
	// record loop
	for i, result := range resultData {
		role := 0
		if result.IsAdmin {
			role = 99
		}
		auth0Id, err := u.auth0Client.CreateUser(ctx, result.Name, string(result.Email), result.Memo)
		if err != nil || auth0Id == nil {
			slog.Error("Auth0ユーザーの作成に失敗", "error", err, "user", result)
			resultErrors = append(resultErrors, fmt.Sprintf("Auth0ユーザーの作成に失敗: 行 %d: %v, エラー: %v", i+2, result, err))
			continue
		}

		err = u.userRepo.Create(ctx, model.User{
			Auth0Id:         *auth0Id,
			Name:            result.Name,
			HierarchyRoleId: result.HierarchyRoleId,
			Email:           string(result.Email),
			Comment:         result.Memo,
			Role:            role,
		})
		if err != nil {
			slog.Error("DBユーザーの作成に失敗", "error", err, "user", result)
			resultErrors = append(resultErrors, fmt.Sprintf("DBユーザーの作成に失敗: 行 %d: %v, エラー: %v", i+2, result, err))
			continue
		}
		slog.Info("ユーザーの登録に成功", "user", result)
	}

	return resultData, resultErrors, err
}

func (u csvImport) SendMailUserList(ctx context.Context, request oapi.PostSendMailUserListRequestObject) ([]oapi.RegistCsvResult, []string, error) {
	records, resultErrors, err := u.Open(request.Body)
	if err != nil {
		slog.Error("CSVファイルのオープンに失敗", "error", err)
		return nil, nil, errors.Wrap(err, "CSVファイルのオープンに失敗しました")
	}
	if len(resultErrors) > 0 {
		slog.Error("CSVファイルのフォーマットチェックに失敗", "errors", resultErrors)
		return nil, resultErrors, errors.New("CSVファイルのフォーマットチェックに失敗しました")
	}
	slog.Info("CSVファイルのフォーマットチェック成功", "record", records)

	for i, record := range records {
		isExist, err := u.userRepo.GetUserByEmailExist(ctx, record[0])
		if err != nil || !isExist {
			slog.Error("ユーザーが存在しないため、メール送信できません", "error", err, "user", record[0])
			resultErrors = append(resultErrors, fmt.Sprintf("ユーザーが存在しないため、メール送信できません: 行 %d: %v, エラー: %v", i+2, records, err))
			continue
		}
	}

	if len(resultErrors) > 0 {
		slog.Error("CSVファイルのフォーマットチェックに失敗", "errors", resultErrors)
		return nil, resultErrors, errors.New("CSVファイルのフォーマットチェックに失敗しました")
	}

	// record loop
	for i, record := range records {
		user, err := u.userRepo.GetUserByEmail(ctx, record[0])
		if err != nil {
			slog.Error("ユーザーの取得に失敗", "error", err, "user", record)
			resultErrors = append(resultErrors, fmt.Sprintf("ユーザーの取得に失敗: 行 %d: %v, エラー: %v", i+2, records, err))
			continue
		}

		err = u.auth0Client.ChangePassword(ctx, user.Auth0Id)
		if err != nil {
			slog.Error("Auth0のメール送信に失敗", "error", err, "user", record)
			resultErrors = append(resultErrors, fmt.Sprintf("Auth0のメール送信に失敗: 行 %d: %v, エラー: %v", i+2, records, err))
			continue
		}
	}

	return nil, nil, nil
}

// Open : multipart.Reader OPEN関数
func (u csvImport) Open(body *multipart.Reader) ([][]string, []string, error) {
	// multipart.Readerのファイルを開く
	form, err := body.ReadForm(10 << 20) // max memory: 10MB
	if err != nil {
		return nil, nil, errors.Wrap(err, "multipart formの読み込みに失敗しました")
	}
	files, exists := form.File["file"]
	var file multipart.File = nil
	if exists && 0 < len(files) {
		file, err = files[0].Open()
		if err != nil {
			return nil, nil, errors.Wrap(err, "ファイルのオープンに失敗しました")
		}
		defer func() {
			if closeErr := file.Close(); closeErr != nil {
				slog.Error("file close error", "error", closeErr)
			}
		}()
	} else {
		return nil, nil, errors.New("ファイルが指定されていません")
	}
	// CSVファイルのフォーマットチェック
	records, resultErrors, err := u.checkFileFormat(file)
	if err != nil {
		return nil, nil, errors.Wrap(err, "CSVファイルのフォーマットチェックに失敗しました")
	}
	slog.Info("CSVファイルのフォーマットチェック完了", "errors", resultErrors, "records", records)
	// フォーマットチェックの結果を返す
	if len(resultErrors) > 0 {
		return nil, resultErrors, nil
	}
	return records, nil, nil
}

func (u csvImport) checkFileFormat(file io.Reader) ([][]string, []string, error) {
	errors := make([]string, 0)
	// CSVファイルをパース
	reader := csv.NewReader(file)
	// CSVのヘッダーをスキップ
	if _, err := reader.Read(); err != nil {
		return nil, errors, err
	}
	// CSVの内容を読み込む
	records, err := reader.ReadAll()
	if err != nil {
		errors = append(errors, fmt.Sprintf("CSVの読み込みエラー: %v", err))
		return nil, errors, err
	}
	if len(records) == 0 {
		errors = append(errors, "CSVの内容が空です")
	}
	// ここでフォーマットチェック
	for i, record := range records {
		if len(record) < 5 {
			errors = append(errors, fmt.Sprintf("CSVのフォーマットエラー: 行 %d: %v, エラー: フィールド数が足りません", i+2, record))
			continue
		}
		// 各フィールドのフォーマットチェック
		if err := checkMailAddressFormat(record[0]); err != nil {
			errors = append(errors, fmt.Sprintf("メールアドレスのフォーマットエラー: 行 %d: %v, エラー: %v", i+2, record, err))
			continue
		}
		if err := checkUserNameFormat(record[1]); err != nil {
			errors = append(errors, fmt.Sprintf("ユーザー名のフォーマットエラー: 行 %d: %v, エラー: %v", i+2, record, err))
			continue
		}
		if err := checkUserMemoFormat(record[2]); err != nil {
			errors = append(errors, fmt.Sprintf("ユーザーメモのフォーマットエラー: 行 %d: %v, エラー: %v", i+2, record, err))
			continue
		}
		if err := checkEmplrIDFormat(record[3]); err != nil {
			errors = append(errors, fmt.Sprintf("EmplrIDのフォーマットエラー: 行 %d: %v, エラー: %v", i+2, record, err))
			continue
		}
		if err := checkRoleFormat(record[4]); err != nil {
			errors = append(errors, fmt.Sprintf("ロールのフォーマットエラー: 行 %d: %v, エラー: %v", i+2, record, err))
			continue
		}
		_, err := strconv.Atoi(record[3])
		if err != nil {
			errors = append(errors, fmt.Sprintf("EmplrIDの変換エラー: 行 %d: %v, エラー: %v", i+2, record, err))
			continue
		}
	}
	return records, errors, nil

}
func (u csvImport) checkData(ctx context.Context, records [][]string) ([]oapi.RegistCsvResult, []string) {
	slog.Info("CSVファイルのフォーマットチェック成功", "record", records)
	results := make([]oapi.RegistCsvResult, 0)
	errors := make([]string, 0)
	// ここでフォーマットチェック
	for i, record := range records {
		mailAddress := openapi_types.Email(record[0])

		emplrId, err := strconv.Atoi(record[3])
		if err != nil {
			errors = append(errors, fmt.Sprintf("EmplrIDの取得エラー: 行 %d: %v, エラー: %v", i+2, record, err))
			continue
		}
		// EmplrIDが存在するかチェックする
		emplr, err := u.emplrRepo.Get(ctx, emplrId)
		if err != nil || emplr == nil {
			errors = append(errors, fmt.Sprintf("EmplrIDの取得エラー: 行 %d: %v, エラー: %v", i+2, record, err))
			continue
		}

		// 既に登録されているかチェックする
		userExist, err := u.userRepo.GetUserByEmailExist(ctx, record[0])
		if err != nil {
			errors = append(errors, fmt.Sprintf("ユーザー詳細の取得エラー: 行 %d: %v, エラー: %v", i+2, record, err))
			continue
		}
		hierarchyLevel := 2
		if emplrId == 999999 {
			// EmplrIDが999999の場合、hierarchyLevelを1に設定
			hierarchyLevel = 1
		}
		hierarchyRole, err := u.hierarchyRepo.GetByEmplrId(ctx, emplrId, hierarchyLevel)
		if err != nil {
			errors = append(errors, fmt.Sprintf("HierarchyRoleの取得エラー: 行 %d: %v, エラー: %v", i+2, record, err))
			continue
		}

		// roleが"admin"の場合、変数にtrueを設定
		isAdmin := false
		if record[4] == "admin" {
			isAdmin = true
		}
		result := oapi.RegistCsvResult{
			Email:           mailAddress,
			Name:            record[1],
			Memo:            record[2],
			EmplrName:       emplr.CustomEmplrName,
			EmplrId:         emplrId,
			IsAdmin:         isAdmin,
			HierarchyRoleId: hierarchyRole.ID,
			IsRegist:        userExist,
		}
		results = append(results, result)
		slog.Info("CSV record", "row", i, "data", record)
	}
	return results, errors
}

// MailAddress 文字列フォーマットチェック
func checkMailAddressFormat(mailAddress string) error {
	if mailAddress == "" {
		return errors.New("メールアドレスが空です")
	}
	if len(mailAddress) > 256 {
		return errors.New("メールアドレスが長すぎます")
	}
	// 簡易的なメールアドレスのフォーマットチェック
	if !isValidEmail(mailAddress) {
		return errors.New("メールアドレスのフォーマットが不正です")
	}
	return nil
}

// isValidEmail 簡易的なメールアドレスのフォーマットチェック
func isValidEmail(email string) bool {
	// 正規表現を使ってメールアドレスのフォーマットをチェック
	// ここでは簡易的なチェックを行う
	if len(email) < 3 || len(email) > 254 {
		return false
	}
	at := 0
	for i, c := range email {
		if c == '@' {
			at++
			if at > 1 || i == 0 || i == len(email)-1 {
				return false // '@'が複数ある、または先頭・末尾にある場合は不正
			}
		}
	}
	return at == 1 // '@'が1つだけある場合は正しい
}

// UserName 文字列フォーマットチェック
func checkUserNameFormat(userName string) error {
	if userName == "" {
		return errors.New("ユーザー名が空です")
	}
	if len(userName) > 50 {
		return errors.New("ユーザー名が長すぎます")
	}
	return nil
}

// UserMemo 文字列フォーマットチェック
func checkUserMemoFormat(userMemo string) error {
	if len(userMemo) > 500 {
		return errors.New("ユーザーメモが長すぎます")
	}
	return nil
}

// EmplrtID 文字列フォーマットチェック
func checkEmplrIDFormat(emplrID string) error {
	if emplrID == "" {
		return errors.New("EmplrIDが空です")
	}
	if len(emplrID) > 7 {
		return errors.New("従業員IDが長すぎます")
	}
	// 従業員IDのフォーマットチェック（例: 数字のみ）
	for _, c := range emplrID {
		if c < '0' || c > '9' {
			return errors.New("従業員IDは数字のみでなければなりません")
		}
	}
	return nil
}

// ROLE 文字列フォーマットチェック
func checkRoleFormat(role string) error {
	if role == "" {
		return nil
	}
	validRoles := []string{"admin"}
	for _, validRole := range validRoles {
		if role == validRole {
			return nil
		}
	}
	return fmt.Errorf("無効なロール: %s", role)
}
