package usecase

import (
	"context"
	"fmt"
	"log/slog"
	"mime/multipart"
	"time"

	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/model"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/repository"
	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
	"github.com/pkg/errors"
)

type noticeInfo struct {
	noticeInfoRepo repository.NoticeInfoRepository
}

func NewNoticeInfo(repo repository.NoticeInfoRepository) NoticeInfoUseCase {
	return &noticeInfo{
		repo,
	}
}

func (u noticeInfo) PostNoticeInfo(ctx context.Context, request oapi.PostNoticeInfoRequestObject) error {
	form, err := request.Body.ReadForm(10 << 20) // max memory: 10MB
	if err != nil {
		return err
	}
	files, exists := form.File["file"]
	fileName := ""
	var file multipart.File = nil
	if exists && 0 < len(files) {
		fileName = files[0].Filename
		file, err = files[0].Open()
		if err != nil {
			return errors.Wrap(err, "ファイルのオープンに失敗しました。")
		}
		defer func() {
			if closeErr := file.Close(); closeErr != nil {
				slog.Error("file close error", "error", closeErr)
			}
		}()
	}
	dateStr := form.Value["releaseDate"][0]
	// 2025-04-24T15:00:00.000Z
	date, err := time.Parse(time.RFC3339, dateStr)
	if err != nil {
		panic(fmt.Sprintf("invalid date format: %v", err))
	}
	noticeInfo := model.NoticeInfo{
		Date:     date,
		Title:    form.Value["title"][0],
		Comment:  form.Value["comment"][0],
		FileName: fileName,
	}

	err = u.noticeInfoRepo.Create(ctx, noticeInfo, file)
	if err != nil {
		return err
	}
	return nil
}

func (u noticeInfo) GetNoticeInfo(ctx context.Context, id int64) (oapi.GetNoticeInfoResponseObject, error) {

	noticeInfo, err := u.noticeInfoRepo.Get(ctx, id)
	if err != nil {
		return nil, err
	}
	return oapi.GetNoticeInfo200JSONResponse{
		Id:          noticeInfo.ID,
		Title:       &noticeInfo.Title,
		ReleaseDate: &noticeInfo.Date,
		Comment:     &noticeInfo.Comment,
		FileName:    &noticeInfo.FileName,
	}, nil
}
func (u noticeInfo) GetListNoticeInfo(ctx context.Context) (oapi.GetNoticeInfoListResponseObject, error) {
	noticeInfoList, err := u.noticeInfoRepo.GetList(ctx)
	if err != nil {
		return nil, err
	}
	results := make([]oapi.NoticeInfo, 0, len(noticeInfoList))
	for _, row := range noticeInfoList {
		noticeInfo := oapi.NoticeInfo{
			Id:          row.ID,
			Title:       &row.Title,
			ReleaseDate: &row.Date,
			Comment:     &row.Comment,
			FileName:    &row.FileName,
		}
		results = append(results, noticeInfo)
	}
	return oapi.GetNoticeInfoList200JSONResponse(results), nil
}

func (u noticeInfo) DeleteNoticeInfo(ctx context.Context, id int64) error {
	return u.noticeInfoRepo.Delete(ctx, id)
}
