package handler

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/public"
)

// メンテナンス情報一覧取得（ページング付き）
// (GET /api/maintenance-info)
func (h *API) GetApiMaintenanceInfo(ctx context.Context, request oapi.GetApiMaintenanceInfoRequestObject) (oapi.GetApiMaintenanceInfoResponseObject, error) {
	page := 1
	if request.Params.Page != nil {
		page = *request.Params.Page
	}

	limit := 10
	if request.Params.Limit != nil {
		limit = *request.Params.Limit
	}

	var keyword *string
	if request.Params.Keyword != nil {
		keyword = request.Params.Keyword
	}

	return h.maintenanceUseCase.GetMaintenanceInfo(ctx, page, limit, keyword)
}

// メンテナンス情報詳細取得
// (GET /api/maintenance-info/{id})
func (h *API) GetApiMaintenanceInfoById(ctx context.Context, request oapi.GetApiMaintenanceInfoByIdRequestObject) (oapi.GetApiMaintenanceInfoByIdResponseObject, error) {
	return h.maintenanceUseCase.GetMaintenanceInfoByID(ctx, request.Id)
}
