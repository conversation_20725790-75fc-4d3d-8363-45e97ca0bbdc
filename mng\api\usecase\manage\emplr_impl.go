package usecase

import (
	"context"

	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/model"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/repository"
	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
)

type emplr struct {
	emplrRepo repository.EmplrRepository
}

func NewEmplr(emplrRepo repository.EmplrRepository) EmplrUseCase {
	return &emplr{
		emplrRepo,
	}
}

func (u emplr) PutEmplr(ctx context.Context, request oapi.PutEmplrRequestObject) error {
	inputEmplr := oapi.PutEmplrJSONRequestBody(*request.Body)

	err := u.emplrRepo.Create(ctx, model.Emplr{
		EmplrID:         inputEmplr.Id,
		CustomEmplrName: inputEmplr.CustomEmplrName,
		GroupID:         inputEmplr.GroupId,
	})
	if err != nil {
		return err
	}
	return nil
}

func (u emplr) GetEmplr(ctx context.Context, id int) (oapi.GetEmplrResponseObject, error) {
	emplr, err := u.emplrRepo.Get(ctx, id)
	if err != nil {
		return nil, err
	}
	return oapi.GetEmplr200JSONResponse{
		Id:              emplr.EmplrID,
		CustomEmplrName: emplr.CustomEmplrName,
		GroupId:         emplr.GroupID,
	}, nil
}

func (u emplr) GetListEmplr(ctx context.Context) (oapi.GetEmplrListResponseObject, error) {
	emplrList, err := u.emplrRepo.GetList(ctx)
	if err != nil {
		return nil, err
	}

	results := make([]oapi.Emplr, 0, len(emplrList))
	for _, row := range emplrList {
		maintenanceInfo := oapi.Emplr{
			Id:              row.EmplrID,
			CustomEmplrName: row.CustomEmplrName,
			GroupId:         row.GroupID,
		}
		results = append(results, maintenanceInfo)
	}

	return oapi.GetEmplrList200JSONResponse(results), nil
}

func (u emplr) PatchEmplr(ctx context.Context, request oapi.PatchEmplrRequestObject) error {
	emplr := oapi.PatchEmplrJSONRequestBody(*request.Body)
	return u.emplrRepo.Update(ctx, model.Emplr{
		EmplrID:         emplr.Id,
		CustomEmplrName: emplr.CustomEmplrName,
		GroupID:         emplr.GroupId,
	})
}

func (u emplr) DeleteEmplr(ctx context.Context, id int) error {
	return u.emplrRepo.Delete(ctx, id)
}

func (u emplr) GetRegistPossibleEmplrList(ctx context.Context) (oapi.GetMasterEmplrListResponseObject, error) {
	emplrList, err := u.emplrRepo.GetRegistPossibleEmplrList(ctx)
	if err != nil {
		return nil, err
	}

	results := make([]oapi.EmplrMaster, 0, len(emplrList))
	for _, row := range emplrList {
		maintenanceInfo := oapi.EmplrMaster{
			Id:                   row.EmplrID,
			Cd:                   row.EmplrCD,
			Name:                 row.EmplrName,
			SecondaryUseProperty: row.SecondaryUseProperty,
		}
		results = append(results, maintenanceInfo)
	}

	return oapi.GetMasterEmplrList200JSONResponse(results), nil
}
