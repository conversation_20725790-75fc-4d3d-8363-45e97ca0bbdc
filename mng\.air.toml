root = "."
tmp_dir = "tmp"

[build]
  # Just plain old shell command. You could use `make` as well.
  cmd = "go build -o bootstrap cmd/api/main.go"
  # Binary file yields from `cmd`.
  bin = "bootstrap"
  # Customize binary, can setup environment variables when run your app.
  full_bin = "dlv --listen=:2345 --headless=true --api-version=2 --accept-multiclient exec --continue bootstrap"
  # Watch these filename extensions.
  include_ext = ["go"]
  # Ignore these filename extensions or directories.
  exclude_dir = ["build", "openapi"]
  # Watch these directories if you specified.
  include_dir = []
  # Exclude files.
  exclude_file = []
  # Exclude specific regular expressions.
  exclude_regex = ["_test.go"]
  # Exclude unchanged files.
  exclude_unchanged = true
  # Follow symlink for directories
  follow_symlink = true
  # This log file places in your tmp_dir.
  log = "air.log"
  # It's not necessary to trigger build each time file changes if it's too frequent.
  delay = 1000 # ms
  # Stop running old binary when build errors occur.
  stop_on_error = true
  # Send Interrupt signal before killing process (windows does not support this feature)
  send_interrupt = false
  # Delay after sending Interrupt signal
  kill_delay = 500 # ms
  # Add additional arguments when running binary (bin/full_bin). Will run './tmp/main hello world'.
  args_bin = []
  # Poll files for changes instead of using fsnotify.
  poll = true
  # Poll interval (defaults to the minimum interval of 500ms).
  poll_interval = 100 # ms

[color]
  app = ""
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"

[log]
  main_only = false
  silent = false
  time = false

[misc]
  clean_on_exit = false

[proxy]
  app_port = 0
  enabled = false
  proxy_port = 0

[screen]
  clear_on_rebuild = false
  keep_scroll = true
