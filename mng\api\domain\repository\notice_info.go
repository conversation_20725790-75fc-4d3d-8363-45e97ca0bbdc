// Package repository is DB connection.
package repository

import (
	"context"
	"io"

	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/model"
)

type NoticeInfoRepository interface {
	Create(context.Context, model.NoticeInfo, io.Reader) error
	Get(context.Context, int64) (*model.NoticeInfo, error)
	GetList(context.Context) ([]model.NoticeInfo, error)
	Update(context.Context, model.NoticeInfo) error
	Delete(context.Context, int64) error
}
