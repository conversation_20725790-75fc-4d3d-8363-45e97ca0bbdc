// Package dbmodels is models from database tables.
package dbmodels

import (
	"time"

	"github.com/uptrace/bun"
)

type MNGNoticeInfo struct {
	bun.BaseModel `bun:"kensuke_plus.tbl_mng_notice_info"`
	ID            int64     `bun:"id,pk,autoincrement"`
	Date          time.Time `bun:"release_date,notnull"`
	Title         string    `bun:"title,notnull"`
	Comment       string    `bun:"comment,notnull"`
	FileName      string    `bun:"file_name"`
	RegUser       string    `bun:"reg_user"`
	UpdUser       string    `bun:"upd_user"`
}
