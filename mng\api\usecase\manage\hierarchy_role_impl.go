package usecase

import (
	"context"

	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/model"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/repository"
	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
)

type hierarchyRole struct {
	hierarchyRoleRepo repository.HierarchyRoleRepository
}

func NewHierarchyRole(repo repository.HierarchyRoleRepository) HierarchyRoleUseCase {
	return &hierarchyRole{
		repo,
	}
}

func (u hierarchyRole) PutHierarchyRole(ctx context.Context, request oapi.PutHierarchyRoleRequestObject) error {
	inputHierarchyRole := oapi.PutHierarchyRoleJSONRequestBody(*request.Body)
	JigyosyoCD := ""
	if inputHierarchyRole.JigyosyoCd != nil {
		JigyosyoCD = *inputHierarchyRole.JigyosyoCd
	}

	err := u.hierarchyRoleRepo.Create(ctx, model.HierarchyRole{
		EmplrID:        inputHierarchyRole.EmplrId,
		HierarchyLevel: inputHierarchyRole.HierarchyLevel,
		JigyosyoCD:     JigyosyoCD,
	})
	if err != nil {
		return err
	}
	return nil
}

func (u hierarchyRole) GetHierarchyRole(ctx context.Context, id int) (oapi.GetHierarchyRoleResponseObject, error) {
	hierarchyRole, err := u.hierarchyRoleRepo.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return oapi.GetHierarchyRole200JSONResponse{
		HierarchyRoleId: &hierarchyRole.ID,
		EmplrId:         hierarchyRole.EmplrID,
		HierarchyLevel:  hierarchyRole.HierarchyLevel,
		JigyosyoCd:      &hierarchyRole.JigyosyoCD,
	}, nil
}

func (u hierarchyRole) GetListHierarchyRole(ctx context.Context, id int) (oapi.GetHierarchyRoleListResponseObject, error) {
	hierarchyRoleList, err := u.hierarchyRoleRepo.GetList(ctx, id)
	if err != nil {
		return nil, err
	}

	results := make([]oapi.HierarchyRole, 0, len(hierarchyRoleList))
	for _, row := range hierarchyRoleList {
		maintenanceInfo := oapi.HierarchyRole{
			HierarchyRoleId: &row.ID,
			EmplrId:         row.EmplrID,
			HierarchyLevel:  row.HierarchyLevel,
			JigyosyoCd:      &row.JigyosyoCD,
		}
		results = append(results, maintenanceInfo)
	}

	return oapi.GetHierarchyRoleList200JSONResponse(results), nil
}
func (u hierarchyRole) GetAllListHierarchyRole(ctx context.Context) (oapi.GetHierarchyRoleAllListResponseObject, error) {
	hierarchyRoleList, err := u.hierarchyRoleRepo.GetAllList(ctx)
	if err != nil {
		return nil, err
	}

	results := make([]oapi.HierarchyRole, 0, len(hierarchyRoleList))
	for _, row := range hierarchyRoleList {
		maintenanceInfo := oapi.HierarchyRole{
			HierarchyRoleId: &row.ID,
			EmplrId:         row.EmplrID,
			HierarchyLevel:  row.HierarchyLevel,
			JigyosyoCd:      &row.JigyosyoCD,
		}
		results = append(results, maintenanceInfo)
	}

	return oapi.GetHierarchyRoleAllList200JSONResponse(results), nil
}

func (u hierarchyRole) DeleteHierarchyRole(ctx context.Context, id int) error {
	return u.hierarchyRoleRepo.Delete(ctx, id)
}

func (u hierarchyRole) GetJigyosyoCdList(ctx context.Context, id int) (oapi.GetJigyosyoCdListResponseObject, error) {
	jigyousyoCDList, err := u.hierarchyRoleRepo.GetJigyousyoCDList(ctx, id)
	if err != nil {
		return nil, err
	}
	results := make([]oapi.JigyosyoInfo, 0, len(jigyousyoCDList))
	for _, row := range jigyousyoCDList {
		maintenanceInfo := oapi.JigyosyoInfo{
			JigyosyoCd:   row.JigyosyoCD,
			JigyosyoName: row.JigyosyoName,
		}
		results = append(results, maintenanceInfo)
	}

	return oapi.GetJigyosyoCdList200JSONResponse(results), nil
}
