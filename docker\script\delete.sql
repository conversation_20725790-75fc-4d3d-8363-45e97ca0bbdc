-- トリガーの削除
DROP TRIGGER IF EXISTS UPDATE_TBL_MNG_EMPLR_TRIGGER ON kensuke_plus.TBL_MNG_EMPLR;
DROP TRIGGER IF EXISTS UPDATE_TBL_MNG_USER_TRIGGER ON kensuke_plus.TBL_MNG_USER;
DROP TRIGGER IF EXISTS UPDATE_TBL_MNG_SERVICE_STATUS_TRIGGER ON kensuke_plus.TBL_MNG_SERVICE_STATUS;
DROP TRIGGER IF EXISTS UPDATE_TBL_MNG_MAINTENANCE_INFO_TRIGGER ON kensuke_plus.TBL_MNG_MAINTENANCE_INFO;
DROP TRIGGER IF EXISTS UPDATE_TBL_MNG_NOTICE_INFO_TRIGGER ON kensuke_plus.TBL_MNG_NOTICE_INFO;
DROP TRIGGER IF EXISTS UPDATE_TBL_MNG_HIERARCHY_ROLE_TRIGGER ON kensuke_plus.TBL_MNG_HIERARCHY_ROLE;

-- 関数の削除
DROP FUNCTION IF EXISTS kensuke_plus.set_update_time;

-- テーブルの削除
DROP TABLE IF EXISTS kensuke_plus.TBL_MST_EMPLR;
DROP TABLE IF EXISTS kensuke_plus.TBL_MST_JIGYOSYO;
DROP TABLE IF EXISTS kensuke_plus.TBL_MNG_EMPLR;
DROP TABLE IF EXISTS kensuke_plus.TBL_MNG_USER;
DROP TABLE IF EXISTS kensuke_plus.TBL_MNG_SERVICE_STATUS;
DROP TABLE IF EXISTS kensuke_plus.TBL_MNG_MAINTENANCE_INFO;
DROP TABLE IF EXISTS kensuke_plus.TBL_MNG_NOTICE_INFO;
DROP TABLE IF EXISTS kensuke_plus.TBL_MNG_HIERARCHY_ROLE;




