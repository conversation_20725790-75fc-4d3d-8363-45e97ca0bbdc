// Package s3 is used to authenticate our users.
package s3

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strconv"

	awsconfig "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/config"
)

// S3 is used to authenticate our users.
type S3Client struct {
	client                   *s3.Client
	attachedFileBucketName   string
	shareSnowFlakeBucketName string
}

// New インスタンスの生成
func New(config config.AWS) S3Client {
	cfg, err := awsconfig.LoadDefaultConfig(context.Background(), awsconfig.WithRegion("ap-northeast-1"))
	if err != nil {
		panic(err)
	}

	client := s3.NewFromConfig(cfg, func(o *s3.Options) {
		o.Region = "ap-northeast-1"
	})

	return S3Client{
		client:                   client,
		attachedFileBucketName:   config.AttachedFileBucketName,
		shareSnowFlakeBucketName: config.ShareSnowFlakeBucketName,
	}
}

// PutFile ファイルをアップロードする
func (a *S3Client) PutFile(ctx context.Context, id int64, fileName string, file io.Reader) error {
	key := filepath.Join(strconv.FormatInt(id, 10), fileName)
	input := &s3.PutObjectInput{
		Bucket: aws.String(a.attachedFileBucketName),
		Key:    aws.String(key),
		Body:   file,
	}
	_, err := a.client.PutObject(ctx, input)
	if err != nil {
		return err
	}
	return nil
}

// GetFile ファイルをダウンロードする
func (a *S3Client) PutMaintenanceJsonFile(ctx context.Context, body io.Reader) error {
	input := &s3.PutObjectInput{
		Bucket: aws.String(a.attachedFileBucketName),
		Key:    aws.String("maintenance_info.json"),
		Body:   body,
	}
	_, err := a.client.PutObject(ctx, input)
	if err != nil {
		return err
	}
	return err
}

// PutTableFile ファイルをアップロードする
func (a *S3Client) PutTableFile(ctx context.Context, inputFileName string, outputFileName string) error {

	file, err := os.Open(filepath.Clean(inputFileName))
	if err != nil {
		return err
	}

	input := &s3.PutObjectInput{
		Bucket: aws.String(a.shareSnowFlakeBucketName),
		Key:    aws.String(outputFileName),
		Body:   file,
	}
	_, err = a.client.PutObject(ctx, input)
	if err != nil {
		return err
	}
	return nil
}

// DeleteFile ファイルを削除する
func (a *S3Client) DeleteFile(ctx context.Context, id int64, fileName string) error {
	key := fmt.Sprintf("%d/%s", id, fileName)
	input := &s3.DeleteObjectInput{
		Bucket: aws.String(a.attachedFileBucketName),
		Key:    aws.String(key),
	}
	_, err := a.client.DeleteObject(ctx, input)
	if err != nil {
		return err
	}
	return nil
}
