@startuml メールの送信に失敗した時のパターン
actor 管理者 as admin
participant "健助Plus管理サーバー" as mngserver
participant "Auth0サーバー" as auth0
participant "SendGrid" as sendgrid
actor いないユーザー as user



admin -> mngserver: ユーザー登録の案内送信
activate mngserver
note left
　メールの送信に失敗した時のパターン
　※メール送信失敗時には、
　特にAuth0側で検知することができないので、
　管理者が手動で再送信する必要がある
end note
mngserver -> sendgrid:UserIdとログインURLの通知
activate sendgrid
sendgrid -> user:UserIdとログインURLの通知
deactivate sendgrid

mngserver -> auth0: change_password
activate auth0
auth0 -> sendgrid:パスワードの変更Linkの送信
activate sendgrid
sendgrid -> user:パスワードの変更Linkの送信
deactivate sendgrid
auth0 --> mngserver:
deactivate auth0
mngserver --> admin:
deactivate mngserver

alt メール送信失敗（Error）
sendgrid -> sendgrid: メール送信失敗（受信ボックス）
else メール送信失敗（Mail Delivery Subsystem）
user -> sendgrid: メール送信失敗（受信ボックス）
end

sendgrid -> admin: メール送信失敗通知

admin -> mngserver: メールアドレスの確認・修正
activate mngserver
mngserver --> admin:
deactivate mngserver

admin -> mngserver: ユーザー登録の案内送信に戻る

@enduml