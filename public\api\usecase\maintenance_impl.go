package usecase

import (
	"context"
	"math"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/public"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/model"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/repository"
)

type Maintenance struct {
	MaintenanceRepo repository.MaintenanceRepository
}

func NewMaintenance(MaintenanceRepo repository.MaintenanceRepository) MaintenanceUseCase {
	return &Maintenance{
		MaintenanceRepo: MaintenanceRepo,
	}
}

func (m Maintenance) GetMaintenanceInfo(ctx context.Context, page, limit int, keyword *string) (oapi.GetApiMaintenanceInfoResponseObject, error) {
	filter := model.MaintenanceListFilter{
		Page:    page,
		Limit:   limit,
		Keyword: keyword,
	}

	maintenanceInfo, total, err := m.MaintenanceRepo.GetMaintenanceInfo(ctx, filter)
	if err != nil {
		message := err.Error()
		return oapi.GetApiMaintenanceInfo500JSONResponse{
			Message: message,
		}, err
	}

	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	var maintenanceDetails []oapi.MaintenanceInfo
	for _, maintenance := range maintenanceInfo {
		maintenanceDetail := oapi.MaintenanceInfo{
			Id:          maintenance.ID,
			Title:       maintenance.Title,
			Comment:     maintenance.Comment,
			ReleaseDate: maintenance.ReleaseDate,
			RegUser:     maintenance.RegUser,
			RegDate:     maintenance.RegDate,
			UpdUser:     maintenance.UpdUser,
			UpdDate:     maintenance.UpdDate,
		}
		maintenanceDetails = append(maintenanceDetails, maintenanceDetail)
	}

	return oapi.GetApiMaintenanceInfo200JSONResponse{
		MaintenanceInfo: maintenanceDetails,
		Total:           total,
		Page:            page,
		Limit:           limit,
		TotalPages:      totalPages,
	}, nil
}

func (m Maintenance) GetMaintenanceInfoByID(ctx context.Context, id int) (oapi.GetApiMaintenanceInfoByIdResponseObject, error) {
	maintenance, err := m.MaintenanceRepo.GetMaintenanceInfoByID(ctx, id)
	if err != nil {
		message := err.Error()
		return oapi.GetApiMaintenanceInfoById404JSONResponse{
			Message: message,
		}, err
	}

	maintenanceDetail := oapi.MaintenanceInfo{
		Id:          maintenance.ID,
		Title:       maintenance.Title,
		Comment:     maintenance.Comment,
		ReleaseDate: maintenance.ReleaseDate,
		RegUser:     maintenance.RegUser,
		RegDate:     maintenance.RegDate,
		UpdUser:     maintenance.UpdUser,
		UpdDate:     maintenance.UpdDate,
	}

	return oapi.GetApiMaintenanceInfoById200JSONResponse(maintenanceDetail), nil
}
