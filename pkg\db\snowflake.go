package db

import (
	"database/sql"
	"errors"
	"fmt"

	"github.com/jmdc-inc/kensuke-plus-server/pkg/config"
	_ "github.com/snowflakedb/gosnowflake" // Snowflake driver
)

type SnowflakeDB *sql.DB

// NewSnowflakeDB creates a new Snowflake database connection
func NewSnowflakeDB(cfg config.SnowflakeDB) SnowflakeDB {
	// DSN format: "user:password@account/dbname/schemaname?warehouse=warehousename&role=rolename"
	// Ensure all necessary parameters are present, otherwise the connection might fail silently or with unhelpful errors.
	if cfg.User == "" || cfg.Account == "" {
		panic(errors.New("\"snowflake user and account must be provided"))
	}

	dsn := fmt.Sprintf("%s:%s@%s/%s/%s?warehouse=%s&role=%s",
		cfg.User,
		cfg.Password,
		cfg.Account,
		cfg.Database,
		cfg.Schema,
		cfg.Warehouse,
		cfg.Role,
	)

	db, err := sql.Open("snowflake", dsn)
	if err != nil {
		panic(err)
	}

	err = db.Ping()
	if err != nil {
		panic(err)
	}

	return db
}
