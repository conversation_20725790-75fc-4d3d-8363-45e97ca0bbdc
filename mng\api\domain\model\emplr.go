// Package model is model definition.
package model

import (
	"fmt"

	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
)

type Emplr struct {
	EmplrID         int
	CustomEmplrName string
	GroupID         int
}

func (e *Emplr) NameWithLabel() string {
	return fmt.Sprintf("%d %s", e.EmplrID, e.CustomEmplrName)
}

func NewEmplr(row dbmodels.MNGEmplr) Emplr {
	return Emplr{
		EmplrID:         row.EmplrID,
		CustomEmplrName: row.CustomEmplrName,
		GroupID:         row.GroupID,
	}
}

func NewEmplrs(rows []dbmodels.MNGEmplr) []Emplr {
	emplrs := make([]Emplr, 0, len(rows))
	for _, row := range rows {
		emplrs = append(emplrs, NewEmplr(row))
	}
	return emplrs
}
