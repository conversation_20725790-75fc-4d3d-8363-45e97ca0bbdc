// Package model is model definition.
package model

import (
	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
)

type User struct {
	UserID          int
	Name            string
	HierarchyRoleId int
	Auth0Id         string
	Email           string
	Role            int
	Comment         string
}

func (e *User) NameWithLabel() string {
	return e.Name
}

func NewUser(row dbmodels.MNGUser) User {
	return User{
		UserID:          row.UserID,
		Name:            row.Name,
		HierarchyRoleId: row.HierarchyRoleID,
		Auth0Id:         row.Auth0Id,
		Email:           row.Email,
		Role:            row.Role,
		Comment:         row.Comment,
	}
}
