package repository

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/db"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/errof"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/model"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/repository"
	"github.com/uptrace/bun"
)

type subscriberInfoRepository struct {
	dbClient db.Client
}

func NewSubscriberInfoRepository(dbClient db.Client) repository.SubscriberInfoRepository {
	return &subscriberInfoRepository{
		dbClient: dbClient,
	}
}

func (r *subscriberInfoRepository) GetUniqueJmdcBrdgIds(ctx context.Context, filter model.SubscriberInfoFilter) ([]int, error) {
	var results []struct {
		JmdcBrdgID int `bun:"JMDC_BRDG_ID"`
	}
	err := r.dbClient.SnowflakeDB.NewSelect().
		Distinct().
		Column("JMDC_BRDG_ID").
		Table("KENSUKE_DEV.BI_WORK.SUBSCRIBER_INFO").
		Apply(func(q *bun.SelectQuery) *bun.SelectQuery {
			if len(filter.EmplrIDs) > 0 {
				q.Where("EMPLR_ID IN (?)", bun.In(filter.EmplrIDs))
			}

			if len(filter.JigyosyoCDs) > 0 {
				q.Where("JIGYOSYO_CD IN (?)", bun.In(filter.JigyosyoCDs))
			}

			if len(filter.FilterCDs) > 0 {
				q.Where("FILTER_CD IN (?)", bun.In(filter.FilterCDs))
			}

			if len(filter.FilterNames) > 0 {
				q.Where("FILTER_NAME IN (?)", bun.In(filter.FilterNames))
			}

			if len(filter.FilterTypes) > 0 {
				q.Where("FILTER_TYPE IN (?)", bun.In(filter.FilterTypes))
			}

			if len(filter.AggregationCDs) > 0 {
				q.Where("AGGREGATION_CD IN (?)", bun.In(filter.AggregationCDs))
			}

			if len(filter.FiscalYears) > 0 {
				q.Where("FISCAL_YEAR IN (?)", bun.In(filter.FiscalYears))
			}

			if len(filter.GenderFamilyKBNs) > 0 {
				q.Where("GENDER_FAMILY_KBN IN (?)", bun.In(filter.GenderFamilyKBNs))
			}

			if len(filter.Ages) > 0 {
				q.Where("AGE IN (?)", bun.In(filter.Ages))
			}

			if len(filter.AgeGroupsIncrements5s) > 0 {
				q.Where("AGE_GROUPS_INCREMENTS_5 IN (?)", bun.In(filter.AgeGroupsIncrements5s))
			}

			if len(filter.AgeGroupsIncrements10s) > 0 {
				q.Where("AGE_GROUPS_INCREMENTS_10 IN (?)", bun.In(filter.AgeGroupsIncrements10s))
			}
			return q
		}).
		Order("JMDC_BRDG_ID").
		Scan(ctx, &results)

	if err != nil {
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	jmdcBrdgIDs := make([]int, len(results))
	for i, r := range results {
		jmdcBrdgIDs[i] = r.JmdcBrdgID
	}

	return jmdcBrdgIDs, nil
}
