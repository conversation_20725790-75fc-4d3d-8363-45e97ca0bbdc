package handler

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
)

// 組合作成
// (PUT /emplr)
func (h *API) PutEmplr(ctx context.Context, request oapi.PutEmplrRequestObject) (oapi.PutEmplrResponseObject, error) {
	err := h.emplrUseCase.PutEmplr(ctx, request)
	if err != nil {
		return oapi.PutEmplr500JSONResponse{
			Message: err.Error(),
		}, err
	}
	return oapi.PutEmplr201Response{}, nil
}

// 組合取得
// (GET /emplr)
func (h *API) GetEmplr(ctx context.Context, request oapi.GetEmplrRequestObject) (oapi.GetEmplrResponseObject, error) {
	emplr, err := h.emplrUseCase.GetEmplr(ctx, request.Params.Id)
	if err != nil {
		return oapi.GetEmplr500JSONResponse{
			Message: err.Error(),
		}, err
	}
	return emplr, nil
}

// 組合更新
// (PATCH /emplr)
func (h *API) PatchEmplr(ctx context.Context, request oapi.PatchEmplrRequestObject) (oapi.PatchEmplrResponseObject, error) {
	err := h.emplrUseCase.PatchEmplr(ctx, request)
	if err != nil {
		return oapi.PatchEmplr500JSONResponse{
			Message: err.Error(),
		}, err

	}
	return oapi.PatchEmplr204Response{}, nil
}

// 組合一覧取得
// (GET /emplr-list)
func (h *API) GetEmplrList(ctx context.Context, request oapi.GetEmplrListRequestObject) (oapi.GetEmplrListResponseObject, error) {

	emplrList, err := h.emplrUseCase.GetListEmplr(ctx)
	if err != nil {
		return oapi.GetEmplrList500JSONResponse{
			Message: err.Error(),
		}, err

	}
	return emplrList, nil
}

// 組合CD取得
// (GET /emplr-cd)
func (h *API) GetMasterEmplrList(ctx context.Context, request oapi.GetMasterEmplrListRequestObject) (oapi.GetMasterEmplrListResponseObject, error) {
	mstEmplrList, err := h.emplrUseCase.GetRegistPossibleEmplrList(ctx)
	if err != nil {
		return oapi.GetMasterEmplrList500JSONResponse{
			Message: err.Error(),
		}, err
	}
	return mstEmplrList, nil
}

func (h *API) DeleteEmplr(ctx context.Context, request oapi.DeleteEmplrRequestObject) (oapi.DeleteEmplrResponseObject, error) {
	err := h.emplrUseCase.DeleteEmplr(ctx, request.Params.Id)
	if err != nil {
		return oapi.DeleteEmplr500JSONResponse{
			Message: err.Error(),
		}, err
	}

	return oapi.DeleteEmplr204Response{}, nil
}
