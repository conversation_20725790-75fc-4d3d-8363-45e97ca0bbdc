// Package router provides the router for the API.
package router

import (
	"encoding/gob"
	"log/slog"
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/cors"
	handler "github.com/jmdc-inc/kensuke-plus-server/mng/api/handler/manage"
	injector "github.com/jmdc-inc/kensuke-plus-server/mng/api/injector/manage"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/config"
	oapi_manage "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
)

// New registers the routes and returns the router.
func NewPublicRouter(appLogger *slog.Logger, appConfig config.AppConfig) *chi.Mux {

	gob.Register(map[string]interface{}{})

	// chi ルーターを作成
	router := chi.NewRouter()

	router.Use(newCORSHandler(appConfig.CORS))

	return router
}

func NewManageRouter(appLogger *slog.Logger, appConfig config.AppConfig) *chi.Mux {
	// To store custom types in our cookies,
	// we must first register them using gob.Register

	apiHandler := injector.InitializeAPIHandler(appLogger, appConfig.Database, appConfig.Auth0, appConfig.AWS, appConfig)

	coreHandler := oapi_manage.NewStrictHandlerWithOptions(
		apiHandler,
		[]oapi_manage.StrictMiddlewareFunc{},
		oapi_manage.StrictHTTPServerOptions{
			RequestErrorHandlerFunc:  handler.RequestErrorHandlerFunc(),
			ResponseErrorHandlerFunc: handler.ResponseErrorHandlerFunc(appLogger),
		},
	)

	gob.Register(map[string]interface{}{})

	// chi ルーターを作成
	router := chi.NewRouter()

	router.Use(newCORSHandler(appConfig.CORS))
	oapi_manage.HandlerFromMux(coreHandler, router)

	return router
}
func newCORSHandler(corsConfig config.CORS) func(http.Handler) http.Handler {
	corsOptions := cors.Options{}
	corsOptions.AllowCredentials = true
	if len(corsConfig.AllowOrigins) > 0 {
		corsOptions.AllowedOrigins = corsConfig.AllowOrigins
	}
	corsOptions.AllowedOrigins = []string{"*"}
	corsOptions.AllowedMethods = []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"}
	corsOptions.AllowedHeaders = []string{"Accept", "Authorization", "Content-Type", "X-CSRF-Token"}
	corsOptions.ExposedHeaders = []string{"Link"}
	corsOptions.Debug = corsConfig.ShouldDebug
	return cors.New(corsOptions).Handler
}
