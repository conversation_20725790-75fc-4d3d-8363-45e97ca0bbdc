// Package dbmodels is models from database tables.
package dbmodels

import "github.com/uptrace/bun"

type MSTJigyosyo struct {
	bun.BaseModel `bun:"kensuke_plus.tbl_mst_jigyosyo"`
	EmplrID       int    `bun:"emplr_id,pk"`
	JigyosyoCD    string `bun:"jigyosyo_cd,notnull"`
	OrderNum      int    `bun:"order_num,notnull"`
	JigyosyoName  string `bun:"jigyosyo_name"`
	CloseFlg      int    `bun:"close_flg,notnull"`
}
