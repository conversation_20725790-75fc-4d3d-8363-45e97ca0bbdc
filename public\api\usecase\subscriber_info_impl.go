package usecase

import (
	"context"

	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/model"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/repository"
)

type subscriberInfoUseCase struct {
	subscriberInfoRepository repository.SubscriberInfoRepository
}

func NewSubscriberInfoUseCase(subscriberInfoRepository repository.SubscriberInfoRepository) SubscriberInfoUseCase {
	return &subscriberInfoUseCase{
		subscriberInfoRepository: subscriberInfoRepository,
	}
}

func (u *subscriberInfoUseCase) GetUniqueJmdcBrdgIds(ctx context.Context, filter model.SubscriberInfoFilter) (*model.UniqueJmdcResponse, error) {
	jmdcBrdgIds, err := u.subscriberInfoRepository.GetUniqueJmdcBrdgIds(ctx, filter)
	if err != nil {
		return nil, err
	}

	return &model.UniqueJmdcResponse{
		JmdcBrdgIDs: jmdcBrdgIds,
		Total:       len(jmdcBrdgIds),
	}, nil
}
