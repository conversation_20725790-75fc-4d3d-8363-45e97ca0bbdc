// Package model is model definition.
package model

import (
	"fmt"
	"mime/multipart"
	"time"

	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
)

type NoticeInfo struct {
	ID       int64
	Date     time.Time
	Title    string
	Comment  string
	FileName string
	RegUser  string
	UpdUser  string
}

func (s *NoticeInfo) NameWithLabel() string {
	return fmt.Sprintf("%d:%s:%s", s.ID, s.Title, s.Comment)
}

func NewNoticeInfo(noticeInfo dbmodels.MNGNoticeInfo) NoticeInfo {
	return NoticeInfo{
		ID:       noticeInfo.ID,
		Date:     noticeInfo.Date,
		Title:    noticeInfo.Title,
		Comment:  noticeInfo.Comment,
		FileName: noticeInfo.FileName,
		RegUser:  noticeInfo.RegUser,
		UpdUser:  noticeInfo.UpdUser,
	}
}
func NewNoticeInfoList(rows []dbmodels.MNGNoticeInfo) []NoticeInfo {
	noticeInfoList := make([]NoticeInfo, 0, len(rows))
	for _, row := range rows {
		noticeInfoList = append(noticeInfoList, NewNoticeInfo(row))
	}
	return noticeInfoList
}

func NewCreateNoticeInfo(form *multipart.Form) NoticeInfo {
	files, exists := form.File["file"]
	fileName := ""
	if exists && 0 < len(files) {
		fileName = files[0].Filename
	}
	dateStr := form.Value["releaseDate"][0]
	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		panic(fmt.Sprintf("invalid date format: %v", err))
	}
	return NoticeInfo{
		Date:     date,
		Title:    form.Value["title"][0],
		Comment:  form.Value["comment"][0],
		FileName: fileName,
	}
}
