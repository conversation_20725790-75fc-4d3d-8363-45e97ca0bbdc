// Package errof is for error types and messages
package errof

import (
	"net/http"

	"github.com/cockroachdb/errors"
)

type (
	// UserError : 400系のユーザエラー
	UserError string

	// InternalError : 500系の内部エラー
	InternalError string

	// VerifyError : 認証エラー（HTTP Status 403）
	VerifyError string
)

func (e UserError) Error() string {
	return string(e)
}

func (e InternalError) Error() string {
	return string(e)
}

func (e VerifyError) Error() string {
	return string(e)
}

func HTTPStatusOf(err error) int {
	switch err := errors.Cause(err); {
	case errors.As(err, ErrPtr[UserError]()):
		return http.StatusBadRequest
	case errors.As(err, ErrPtr[VerifyError]()):
		return http.StatusUnauthorized
	default:
		return http.StatusInternalServerError
	}
}

func ErrPtr[T error]() *T {
	var err T
	return &err
}
