// Package usecase is UseCase definition and implements.
package usecase

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
)

type EmplrUseCase interface {
	PutEmplr(ctx context.Context, request oapi.PutEmplrRequestObject) error
	GetEmplr(ctx context.Context, id int) (oapi.GetEmplrResponseObject, error)
	GetListEmplr(ctx context.Context) (oapi.GetEmplrListResponseObject, error)
	PatchEmplr(ctx context.Context, request oapi.PatchEmplrRequestObject) error
	DeleteEmplr(ctx context.Context, id int) error
	GetRegistPossibleEmplrList(ctx context.Context) (oapi.GetMasterEmplrListResponseObject, error)
}
