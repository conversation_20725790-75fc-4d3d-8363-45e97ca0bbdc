// Package model is model definition.
package model

import (
	"fmt"

	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
)

type JigyosyoCD struct {
	JigyosyoCD   string
	JigyosyoName string
}

func (e *JigyosyoCD) NameWithLabel() string {
	return fmt.Sprintf("%s %s", e.JigyosyoCD, e.JigyosyoName)
}

func NewJigyosyoCD(row dbmodels.MSTJigyosyo) JigyosyoCD {
	return JigyosyoCD{
		JigyosyoCD:   row.JigyosyoCD,
		JigyosyoName: row.JigyosyoName,
	}
}

func NewJigyosyoCDList(rows []dbmodels.MSTJigyosyo) []JigyosyoCD {
	jigyosyoCDList := make([]JigyosyoCD, 0, len(rows))
	for _, row := range rows {
		jigyosyoCDList = append(jigyosyoCDList, NewJigyosyoCD(row))
	}
	return jigyosyoCDList
}
