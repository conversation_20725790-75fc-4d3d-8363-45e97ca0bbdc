package usecase

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/public"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/repository"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/service"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

const (
	AdminRoleValue = 1
)

type User struct {
	UserRepo          repository.UserRepository
	Auth0TokenService service.Auth0TokenService
}

func NewUser(UserRepo repository.UserRepository, auth0TokenService service.Auth0TokenService) UserUseCase {
	return &User{
		UserRepo:          UserRepo,
		Auth0TokenService: auth0TokenService,
	}
}

func (u User) GetUserInfoByToken(ctx context.Context, token string) (oapi.GetApiUserInfoResponseObject, error) {
	auth0Id, err := u.Auth0TokenService.ParseAuth0Token(token)
	if err != nil {
		message := "Invalid token: " + err.Error()
		return oapi.GetApiUserInfo401JSONResponse{
			Message: &message,
		}, err
	}

	user, err := u.UserRepo.GetUserDetailByAuth0Id(ctx, auth0Id)
	if err != nil {
		message := err.Error()
		return oapi.GetApiUserInfo500JSONResponse{
			Message: message,
		}, err
	}

	userDetail := oapi.UserDetail{
		Id:          user.UserID,
		Name:        user.Name,
		Email:       openapi_types.Email(user.Email),
		Auth0UserId: user.Auth0ID,
		Comment:     user.Comment,

		IsAdmin: user.Role == AdminRoleValue,

		IsBlocked: user.IsBlocked,
		IsMfa:     user.IsMFA,
		LastLogin: user.LastLogin,

		EmplrId:   &user.EmplrID,
		EmplrCd:   user.EmplrCD,
		EmplrName: user.EmplrName,

		HierarchyLevel: user.HierarchyLevel,
		JigyosyoCd:     user.JigyosyoCD,
	}

	return oapi.GetApiUserInfo200JSONResponse(userDetail), nil
}

func (u User) GetLevel2Users(ctx context.Context, keyword *string) (oapi.GetApiLevel2UsersResponseObject, error) {
	users, err := u.UserRepo.GetLevel2Users(ctx, keyword)
	if err != nil {
		message := err.Error()
		return oapi.GetApiLevel2Users500JSONResponse{
			Message: message,
		}, err
	}

	var userDetails []oapi.UserDetail
	for _, user := range users {
		userDetail := oapi.UserDetail{
			Id:             user.UserID,
			Name:           user.Name,
			Email:          openapi_types.Email(user.Email),
			Auth0UserId:    user.Auth0ID,
			Comment:        user.Comment,
			IsAdmin:        user.Role == 1,
			IsBlocked:      user.IsBlocked,
			IsMfa:          user.IsMFA,
			LastLogin:      user.LastLogin,
			EmplrId:        &user.EmplrID,
			EmplrCd:        user.EmplrCD,
			EmplrName:      user.EmplrName,
			HierarchyLevel: user.HierarchyLevel,
			JigyosyoCd:     user.JigyosyoCD,
		}
		userDetails = append(userDetails, userDetail)
	}

	totalUsers := len(userDetails)
	return oapi.GetApiLevel2Users200JSONResponse{
		Users: &userDetails,
		Total: &totalUsers,
	}, nil
}
