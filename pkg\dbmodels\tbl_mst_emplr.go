// Package dbmodels is models from database tables.
package dbmodels

import (
	"time"
	"github.com/uptrace/bun"
)

type MSTEmplr struct {
	bun.BaseModel      `bun:"kensuke_plus.tbl_mst_emplr"`
	EmplrID            int        `bun:"emplr_id,pk"`
	EmplrName          string     `bun:"emplr_name"`
	EmplrLabel         string     `bun:"emplr_label"`
	RSVPtn             int        `bun:"rsv_ptn"`
	EmplrKBN           int        `bun:"emplr_kbn"`
	ReceSTYM           int        `bun:"rece_st_ym"`
	ReceEDYM           int        `bun:"rece_ed_ym"`
	MemSTYM            int        `bun:"mem_st_ym"`
	MemEDYM            int        `bun:"mem_ed_ym"`
	HeSTYM             int        `bun:"he_st_ym"`
	HeEDYM             int        `bun:"he_ed_ym"`
	FlaSTYM            int        `bun:"fla_st_ym"`
	FlaEDYM            int        `bun:"fla_ed_ym"`
	FullSTYM           int        `bun:"full_st_ym"`
	FullEDYM           int        `bun:"full_ed_ym"`
	MemUPDDATE         *time.Time `bun:"mem_upd_date"`
	FlaUPDDATE         *time.Time `bun:"fla_upd_date"`
	ReceUPDDATE        *time.Time `bun:"rece_upd_date"`
	DeptUseFlg         int        `bun:"dept_use_flg"`
	DelFlg             int        `bun:"del_flg"`
	PasswordPattern    string     `bun:"password_pattern"`
	PasswordChangeDays int        `bun:"password_change_days"`
	LockCount          int        `bun:"lock_count"`
}
