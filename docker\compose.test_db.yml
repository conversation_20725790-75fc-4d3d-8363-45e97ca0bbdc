services:
  db_test:
    image: postgres:latest
    container_name: kensuke-plus-mng_test
    restart: always
    hostname: pgsql-db
    volumes:
      - postgresql:/var/lib/postgresql/data
    ports:
      - 5532:5432
      - 5533:5433
      - 5544:5444
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    healthcheck:
      test: ["CMD","pg_isready"]
      interval: 30s
      timeout: 10s
      retries: 5
volumes:
  postgresql:
    driver: local

