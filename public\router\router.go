package router

import (
	"encoding/gob"
	"log/slog"
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/cors"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/config"
	oapi_public "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/public"
	handler "github.com/jmdc-inc/kensuke-plus-server/public/api/handler"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/injector"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/middleware"
)

func NewPublicRouter(appLogger *slog.Logger, appConfig config.AppConfig) *chi.Mux {

	apiHandler := injector.InitializeAPIHandler(appLogger, appConfig.Database, appConfig.Snowflake, appConfig.Auth0, appConfig.AWS, appConfig)

	coreHandler := oapi_public.NewStrictHandlerWithOptions(
		apiHandler,
		[]oapi_public.StrictMiddlewareFunc{},
		oapi_public.StrictHTTPServerOptions{
			RequestErrorHandlerFunc:  handler.RequestErrorHandlerFunc(),
			ResponseErrorHandlerFunc: handler.ResponseErrorHandlerFunc(appLogger),
		},
	)

	gob.Register(map[string]interface{}{})

	// chi ルーターを作成
	router := chi.NewRouter()

	router.Use(newCORSHandler(appConfig.CORS))
	router.Use(middleware.AuthHeaderMiddleware)

	router.Get("/api/version", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte(`{"version":"1.0.0"}`))
	})

	oapi_public.HandlerFromMuxWithBaseURL(coreHandler, router, "/api")

	return router
}

func newCORSHandler(corsConfig config.CORS) func(http.Handler) http.Handler {
	corsOptions := cors.Options{}
	corsOptions.AllowCredentials = true
	if corsConfig.AllowOrigins != nil {
		corsOptions.AllowedOrigins = corsConfig.AllowOrigins
	} else {
		corsOptions.AllowedOrigins = []string{"*"}
	}
	corsOptions.AllowedMethods = []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"}
	corsOptions.AllowedHeaders = []string{"Accept", "Authorization", "Content-Type", "X-CSRF-Token", "X-Requested-With"}
	corsOptions.ExposedHeaders = []string{"Link", "Content-Disposition"}
	corsOptions.MaxAge = 300
	corsOptions.Debug = corsConfig.ShouldDebug
	return cors.New(corsOptions).Handler
}
