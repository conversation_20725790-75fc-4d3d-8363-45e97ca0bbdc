CREATE TABLE kensuke_plus.TBL_MST_EMPLR (
    emplr_id int NOT NULL PRIMARY KEY,
    emplr_cd VARCHAR(3) NOT NULL UNIQUE,
    secondary_use_property int NOT NULL,
    emplr_name VARCHAR(1000) NOT NULL,
    reg_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    upd_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE kensuke_plus.TBL_MST_EMPLR IS '組合マスター';
-----------------------------------------------------------------------
-----------------------------------------------------------------------

CREATE TABLE kensuke_plus.TBL_MST_JIGYOSYO (
    emplr_id integer NOT NULL, -- 組合ID
    jigyosyo_cd VARCHAR(255) NOT NULL, -- 事業所コード：記号
    order_num integer NOT NULL, -- 順番：管理ツールの表示順序で利用
    jigyosyo_name VARCHAR(255), -- 事業所名：表示用
    jigyosyo_kbn integer NOT NULL, -- 事業所区分：未利用
    business_kbn integer, -- ビジネス区分：未利用
    emplr_scale integer, -- 未利用
    from_ym integer NOT NULL, -- 未利用
    to_ym integer NOT NULL, -- 未利用
    close_flg integer NOT NULL, -- 削除フラグ：1以上の場合削除扱い
    PRIMARY KEY (emplr_id, jigyosyo_cd) -- 組合IDと事業所コードの組み合わせでユニーク制約
);
COMMENT ON TABLE kensuke_plus.TBL_MST_JIGYOSYO IS '事業所の情報が格納されるマスターテーブル
週のロード処理時にSnowFlakeのデータを参照し更新される
※テーブルのカラム構成は基幹システムの物をそのまま流用
※Updateはなく追加のみ
※管理ツールのみで利用。';

COMMENT ON COLUMN kensuke_plus.TBL_MST_JIGYOSYO.emplr_id IS '組合ID';
COMMENT ON COLUMN kensuke_plus.TBL_MST_JIGYOSYO.jigyosyo_cd IS '事業所コード：記号';
COMMENT ON COLUMN kensuke_plus.TBL_MST_JIGYOSYO.order_num IS '順番：管理ツールの表示順序で利用';
COMMENT ON COLUMN kensuke_plus.TBL_MST_JIGYOSYO.jigyosyo_name IS '事業所名：表示用';
COMMENT ON COLUMN kensuke_plus.TBL_MST_JIGYOSYO.jigyosyo_kbn IS '事業所区分：未利用';
COMMENT ON COLUMN kensuke_plus.TBL_MST_JIGYOSYO.business_kbn IS 'ビジネス区分：未利用';
COMMENT ON COLUMN kensuke_plus.TBL_MST_JIGYOSYO.emplr_scale IS '未利用';
COMMENT ON COLUMN kensuke_plus.TBL_MST_JIGYOSYO.from_ym IS '未利用';
COMMENT ON COLUMN kensuke_plus.TBL_MST_JIGYOSYO.to_ym IS '未利用';
COMMENT ON COLUMN kensuke_plus.TBL_MST_JIGYOSYO.close_flg IS '削除フラグ：1以上の場合削除扱い';

-----------------------------------------------------------------------
-----------------------------------------------------------------------
CREATE TABLE kensuke_plus.TBL_MNG_EMPLR (
    emplr_id int NOT NULL PRIMARY KEY,
    emplr_cd VARCHAR(3) NOT NULL UNIQUE,
    custom_emplr_name text,
    group_id int NOT NULL,
    secondary_use_property int NOT NULL,
    reg_user text,
    reg_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    upd_user text,
    upd_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE kensuke_plus.TBL_MNG_EMPLR IS '組合';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_EMPLR.emplr_id IS '組合ID';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_EMPLR.group_id IS '区分';
-----------------------------------------------------------------------
-----------------------------------------------------------------------
CREATE TABLE kensuke_plus.TBL_MNG_HIERARCHY_ROLE (
    hierarchy_role_id serial PRIMARY KEY, -- ID
    emplr_id integer NOT NULL, -- 組合ID
    hierarchy_level integer NOT NULL, -- 階層情報
    jigyosyo_cd text, -- 事業所コード：階層3の場合のみ入力されるフィールド
    reg_user text, -- 登録ユーザー
    reg_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    upd_user text, -- 更新ユーザー
    upd_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE kensuke_plus.TBL_MNG_HIERARCHY_ROLE IS '階層情報のロールテーブル
管理ツールで作成される
削除は物理削除';

COMMENT ON COLUMN kensuke_plus.TBL_MNG_HIERARCHY_ROLE.hierarchy_role_id IS 'ID';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_HIERARCHY_ROLE.emplr_id IS '組合ID';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_HIERARCHY_ROLE.hierarchy_level IS '階層情報
・階層１⇒（選択不可：JMDCユーザーは階層1固定）
・階層２⇒（選択可：組合を選択する必要がある）
・階層３⇒（選択可：組合とそれに紐づく記号を選択する必要がある）
・階層４⇒（選択不可）';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_HIERARCHY_ROLE.jigyosyo_cd IS '事業所コード：階層3の場合のみ入力されるフィールド';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_HIERARCHY_ROLE.reg_user IS '登録ユーザー';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_HIERARCHY_ROLE.reg_date IS '登録年月日';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_HIERARCHY_ROLE.upd_user IS '更新ユーザー';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_HIERARCHY_ROLE.upd_date IS '更新年月日';
-----------------------------------------------------------------------
-----------------------------------------------------------------------
CREATE TABLE kensuke_plus.TBL_MNG_USER (
    user_id serial PRIMARY KEY,
    hierarchy_role_id int NOT NULL,
    auth0_id VARCHAR(100) NOT NULL UNIQUE,
    name VARCHAR(50) NOT NULL,
    email text NOT NULL UNIQUE,
    role int NOT NULL,
    use_personal_list boolean NOT NULL DEFAULT false,
    tableau_id text, -- Tableau ID
    comment text,
    reg_user text,
    reg_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    upd_user text,
    upd_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE kensuke_plus.TBL_MNG_USER IS 'ユーザー';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_USER.user_id IS 'UserID';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_USER.hierarchy_role_id IS '階層ロールID';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_USER.auth0_id IS 'Auth0ID';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_USER.name IS '名前';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_USER.email IS 'メールアドレス';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_USER.role IS '0:一般 99:管理者';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_USER.use_personal_list IS 'あんしんリストの使用';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_USER.tableau_id IS 'Tableau ID';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_USER.comment IS 'コメント';

-----------------------------------------------------------------------
-----------------------------------------------------------------------
CREATE TABLE kensuke_plus.TBL_MNG_SERVICE_STATUS (
    status integer NOT NULL,
    upd_user text,
    upd_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE kensuke_plus.TBL_MNG_SERVICE_STATUS IS 'サービスステータス';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_SERVICE_STATUS.status IS 'ステータス';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_SERVICE_STATUS.upd_date IS '更新日';

-----------------------------------------------------------------------
-----------------------------------------------------------------------
CREATE TABLE kensuke_plus.TBL_MNG_MAINTENANCE_INFO (
    id serial PRIMARY KEY,
    release_date timestamp NOT NULL,
    title text NOT NULL,
    comment text NOT NULL,
    reg_user text,
    reg_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    upd_user text,
    upd_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE kensuke_plus.TBL_MNG_MAINTENANCE_INFO IS 'メンテナンス情報';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_MAINTENANCE_INFO.id IS 'ID';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_MAINTENANCE_INFO.release_date IS 'リリース日';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_MAINTENANCE_INFO.title IS 'タイトル';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_MAINTENANCE_INFO.comment IS 'コメント';

-----------------------------------------------------------------------
-----------------------------------------------------------------------
CREATE TABLE kensuke_plus.TBL_MNG_NOTICE_INFO (
    id serial PRIMARY KEY,
    release_date timestamp NOT NULL,
    title text NOT NULL,
    comment text NOT NULL,
    file_name text,
    reg_user text,
    reg_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    upd_user text,
    upd_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE kensuke_plus.TBL_MNG_NOTICE_INFO IS 'お知らせ情報';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_NOTICE_INFO.id IS 'ID';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_NOTICE_INFO.release_date IS 'リリース日';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_NOTICE_INFO.title IS 'タイトル';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_NOTICE_INFO.comment IS 'コメント';
COMMENT ON COLUMN kensuke_plus.TBL_MNG_NOTICE_INFO.file_name IS '添付ファイル名';

-----------------------------------------------------------------------
-----------------------------------------------------------------------

CREATE FUNCTION kensuke_plus.set_update_time() RETURNS trigger AS '
  begin
    new.upd_date := ''now'';
    return new;
  end;
' LANGUAGE plpgsql;

CREATE TRIGGER UPDATE_TBL_MNG_EMPLR_TRIGGER BEFORE UPDATE ON kensuke_plus.TBL_MNG_EMPLR FOR EACH ROW EXECUTE PROCEDURE kensuke_plus.set_update_time();
CREATE TRIGGER UPDATE_TBL_MNG_USER_TRIGGER BEFORE UPDATE ON kensuke_plus.TBL_MNG_USER FOR EACH ROW EXECUTE PROCEDURE kensuke_plus.set_update_time();
CREATE TRIGGER UPDATE_TBL_MNG_SERVICE_STATUS_TRIGGER BEFORE UPDATE ON kensuke_plus.TBL_MNG_SERVICE_STATUS FOR EACH ROW EXECUTE PROCEDURE kensuke_plus.set_update_time();
CREATE TRIGGER UPDATE_TBL_MNG_MAINTENANCE_INFO_TRIGGER BEFORE UPDATE ON kensuke_plus.TBL_MNG_MAINTENANCE_INFO FOR EACH ROW EXECUTE PROCEDURE kensuke_plus.set_update_time();
CREATE TRIGGER UPDATE_TBL_MNG_NOTICE_INFO_TRIGGER BEFORE UPDATE ON kensuke_plus.TBL_MNG_NOTICE_INFO FOR EACH ROW EXECUTE PROCEDURE kensuke_plus.set_update_time();
CREATE TRIGGER UPDATE_TBL_MNG_HIERARCHY_ROLE_TRIGGER BEFORE UPDATE ON kensuke_plus.TBL_MNG_HIERARCHY_ROLE FOR EACH ROW EXECUTE PROCEDURE kensuke_plus.set_update_time();


