// Package model is model definition.
package model

import (
	"fmt"
	"time"

	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
)

type MaintenanceInfo struct {
	ID      int64
	Date    time.Time
	Title   string
	Comment string
	RegUser string
	UpdUser string
}

func (s *MaintenanceInfo) NameWithLabel() string {
	return fmt.Sprintf("%d:%s:%s", s.ID, s.Title, s.Comment)
}

func NewMaintenanceInfo(maintenanceInfo dbmodels.MNGMaintenanceInfo) MaintenanceInfo {
	return MaintenanceInfo{
		ID:      maintenanceInfo.ID,
		Date:    maintenanceInfo.Date,
		Title:   maintenanceInfo.Title,
		Comment: maintenanceInfo.Comment,
		RegUser: maintenanceInfo.RegUser,
		UpdUser: maintenanceInfo.UpdUser,
	}
}

func NewMaintenanceInfoList(rows []dbmodels.MNGMaintenanceInfo) []MaintenanceInfo {
	maintenanceInfoList := make([]MaintenanceInfo, 0, len(rows))
	for _, row := range rows {
		maintenanceInfoList = append(maintenanceInfoList, NewMaintenanceInfo(row))
	}
	return maintenanceInfoList
}
