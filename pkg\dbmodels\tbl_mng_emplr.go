// Package dbmodels is models from database tables.
package dbmodels

import (
	"strconv"
	"time"

	"github.com/uptrace/bun"
)

type MNGEmplr struct {
	bun.BaseModel        `bun:"kensuke_plus.tbl_mng_emplr"`
	EmplrID              int       `bun:"emplr_id,pk"`
	EmplrCD              string    `bun:"emplr_cd,notnull"`
	CustomEmplrName      string    `bun:"custom_emplr_name,notnull"`
	GroupID              int       `bun:"group_id,notnull"`
	SecondaryUseProperty int       `bun:"secondary_use_property,notnull"`
	RegDate              time.Time `bun:"reg_date,notnull,default:current_timestamp"`
	UpdDate              time.Time `bun:"upd_date,notnull,default:current_timestamp"`
}

// TableHeaders returns the headers for the CSV file.
func (e *MNGEmplr) TableHeaders() []string {
	return []string{
		"emplr_id",
		"emplr_cd",
		"custom_emplr_name",
		"group_id",
		"secondary_use_property",
		"reg_date",
		"upd_date",
	}
}

// MNGEmplrを文字列配列に変換する
func (e *MNGEmplr) ToStringArray() []string {
	return []string{
		strconv.Itoa(e.EmplrID),
		e.EmplrCD,
		e.CustomEmplrName,
		strconv.Itoa(e.GroupID),
		strconv.Itoa(e.SecondaryUseProperty),
		e.RegDate.Format("2006-01-02 15:04:05"),
		e.UpdDate.Format("2006-01-02 15:04:05"),
	}
}
