package service

import (
	"bytes"
	"context"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"github.com/jmdc-inc/kensuke-plus-server/pkg/config"
	"github.com/pkg/errors"
)

type TableauDownloadService interface {
	DownloadPDF(ctx context.Context, viewId string, filters map[string]interface{}) ([]byte, error)
	DownloadCSV(ctx context.Context, viewId string, filters map[string]interface{}) ([]byte, error)
}

type tableauDownloadService struct {
	baseURL  string
	username string
	password string
	client   *http.Client
}

type tableauView struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	ContentURL string `json:"contentUrl"`
	WorkbookID string `json:"workbookId"`
}

type tableauSession struct {
	token  string
	siteID string
}

type tableauViews []tableauView

func (t tableauViews) FindByName(name string) *tableauView {
	for _, view := range t {
		if view.Name == name {
			return &view
		}
	}
	return nil
}

func NewTableauDownloadService(cfg config.AppConfig) TableauDownloadService {
	return &tableauDownloadService{
		baseURL:  cfg.Tableau.Server.BaseURL,
		username: cfg.Tableau.Server.Username,
		password: cfg.Tableau.Server.Password,
		client:   &http.Client{},
	}
}

func (s *tableauDownloadService) DownloadPDF(ctx context.Context, viewId string, filters map[string]interface{}) ([]byte, error) {
	session, err := s.signIn(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "failed to sign in to Tableau")
	}

	views, err := s.getViews(ctx, session)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get views")
	}

	view := views.FindByName(viewId)
	if view == nil {
		return nil, errors.Errorf("tableauView with name '%s' not found", viewId)
	}

	filterParams := s.buildFilterParams(filters)
	downloadURL := fmt.Sprintf("%s/sites/%s/views/%s/pdf%s", s.baseURL, session.siteID, view.ID, filterParams)

	return s.downloadFile(ctx, session, downloadURL)
}

func (s *tableauDownloadService) DownloadCSV(ctx context.Context, viewId string, filters map[string]interface{}) ([]byte, error) {
	session, err := s.signIn(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "failed to sign in to Tableau")
	}

	views, err := s.getViews(ctx, session)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get views")
	}

	view := views.FindByName(viewId)
	if view == nil {
		return nil, errors.Errorf("tableauView with name '%s' not found", viewId)
	}

	filterParams := s.buildFilterParams(filters)
	downloadURL := fmt.Sprintf("%s/sites/%s/views/%s/data.csv%s", s.baseURL, session.siteID, view.ID, filterParams)

	return s.downloadFile(ctx, session, downloadURL)
}

func (s *tableauDownloadService) signIn(ctx context.Context) (*tableauSession, error) {
	// Create request body
	reqBody, err := json.Marshal(
		map[string]interface{}{
			"credentials": map[string]interface{}{
				"name":     s.username,
				"password": s.password,
				"site": map[string]interface{}{
					"contentUrl": "",
				},
			},
		},
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to marshal sign in request")
	}

	// Create request
	req, err := http.NewRequestWithContext(
		ctx,
		"POST",
		fmt.Sprintf("%s/auth/signin", s.baseURL),
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create sign in request")
	}
	req.Header.Set("Content-Type", "application/json")

	// Request to Tableau Server
	resp, err := s.request(req)
	if err != nil {
		return nil, err
	}

	// Parse XML response from Tableau Server
	var tsResp struct {
		XMLName     xml.Name `xml:"tsResponse"`
		Credentials struct {
			Token string `xml:"token,attr"`
			Site  struct {
				ID         string `xml:"id,attr"`
				ContentURL string `xml:"contentUrl,attr"`
			} `xml:"site"`
			User struct {
				ID string `xml:"id,attr"`
			} `xml:"user"`
		} `xml:"credentials"`
	}
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.Wrap(err, "failed to read response body")
	}
	if err := xml.Unmarshal(bodyBytes, &tsResp); err != nil {
		return nil, errors.Wrap(err, "failed to decode sign in response")
	}

	return &tableauSession{
		token:  tsResp.Credentials.Token,
		siteID: tsResp.Credentials.Site.ID,
	}, nil
}

func (s *tableauDownloadService) getViews(ctx context.Context, session *tableauSession) (tableauViews, error) {
	// Create request
	req, err := http.NewRequestWithContext(
		ctx,
		"GET",
		fmt.Sprintf("%s/sites/%s/views?pageSize=1000&pageNumber=1", s.baseURL, session.siteID),
		nil,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create get views request")
	}
	req.Header.Set("X-Tableau-Auth", session.token)

	// Request to Tableau Server
	resp, err := s.request(req)
	if err != nil {
		return nil, err
	}

	// Read response body
	var viewsResp struct {
		XMLName xml.Name `xml:"tsResponse"`
		Views   struct {
			View []struct {
				ID         string `xml:"id,attr"`
				Name       string `xml:"name,attr"`
				ContentURL string `xml:"contentUrl,attr"`
				Workbook   struct {
					ID string `xml:"id,attr"`
				} `xml:"workbook"`
			} `xml:"view"`
		} `xml:"views"`
	}
	bodyBytes, err := io.ReadAll(resp.Body)

	if err != nil {
		return nil, errors.Wrap(err, "failed to read views response body")
	}
	if err := xml.Unmarshal(bodyBytes, &viewsResp); err != nil {
		return nil, errors.Wrap(err, "failed to decode views response")
	}

	// Convert XML views to standard tableauView struct
	views := make([]tableauView, 0, len(viewsResp.Views.View))
	for _, xmlView := range viewsResp.Views.View {
		views = append(views, tableauView{
			ID:         xmlView.ID,
			Name:       xmlView.Name,
			ContentURL: xmlView.ContentURL,
			WorkbookID: xmlView.Workbook.ID,
		})
	}
	return views, nil
}

func (s *tableauDownloadService) buildFilterParams(filters map[string]interface{}) string {
	if len(filters) == 0 {
		return ""
	}

	params := url.Values{}
	for key, value := range filters {
		// Map common filter keys to Tableau field names
		tableauFieldName := key
		// Map filter keys to actual Tableau field names
		switch key {
		case "emplrId":
			tableauFieldName = "Emplr Id"
		case "employerId":
			tableauFieldName = "Emplr Id"
		case "region":
			tableauFieldName = "Region"
		case "year":
			tableauFieldName = "Year"
		}
		filterKey := fmt.Sprintf("vf_%s", tableauFieldName)
		filterValue := fmt.Sprintf("%v", value)

		params.Add(filterKey, filterValue)
	}

	return "?" + params.Encode()
}

func (s *tableauDownloadService) downloadFile(ctx context.Context, session *tableauSession, downloadURL string) ([]byte, error) {
	// Create request
	req, err := http.NewRequestWithContext(ctx, "GET", downloadURL, nil)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create download request")
	}
	req.Header.Set("X-Tableau-Auth", session.token)

	// Request to Tableau Server
	resp, err := s.request(req)
	if err != nil {
		return nil, err
	}

	// Read response body
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.Wrap(err, "failed to read download response")
	}

	return data, nil
}

func (s *tableauDownloadService) request(req *http.Request) (*http.Response, error) {
	resp, err := s.client.Do(req)
	if err != nil {
		return nil, errors.Wrap(err, "failed to request")
	}

	// Check for response
	if resp.StatusCode == http.StatusUnauthorized {
		return nil, errors.New("unauthorized - token may be expired")
	}
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, errors.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}
	return resp, nil
}
