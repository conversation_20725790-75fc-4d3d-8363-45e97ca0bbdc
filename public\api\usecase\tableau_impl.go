package usecase

import (
	"context"

	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/repository"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/service"
)

type tableauUseCase struct {
	tableauJWTService service.TableauJWTService
	userRepo          repository.UserRepository
}

func NewTableauUseCase(tableauJWTService service.TableauJWTService, userRepo repository.UserRepository) TableauUseCase {
	return &tableauUseCase{
		tableauJWTService: tableauJWTService,
		userRepo:          userRepo,
	}
}

func (u *tableauUseCase) GenerateTableauJWT(ctx context.Context, auth0ID string) (string, error) {
	userDetail, err := u.userRepo.GetUserDetailByAuth0Id(ctx, auth0ID)
	if err != nil {
		return "", err
	}

	return u.tableauJWTService.GenerateJWT(userDetail.TableauID)
}
