#!/bin/bash

# Test script for the new /emplr-by-id endpoint

# Set the base URL (adjust as needed)
BASE_URL="http://localhost:8080"

# Test with a sample employId
EMPLOY_ID=100001

echo "Testing /emplr-by-id endpoint..."
echo "URL: ${BASE_URL}/emplr-by-id"
echo "Request body: {\"employId\": ${EMPLOY_ID}}"
echo ""

# Make the POST request
curl -X POST "${BASE_URL}/emplr-by-id" \
  -H "Content-Type: application/json" \
  -d "{\"employId\": ${EMPLOY_ID}}" \
  | jq '.' 2>/dev/null || cat

echo ""
echo "Test completed."
