// Package repository is DB connection.
package repository

import (
	"context"

	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/model"
)

type EmplrRepository interface {
	Create(context.Context, model.Emplr) error
	Get(context.Context, int) (*model.Emplr, error)
	GetList(context.Context) ([]model.Emplr, error)
	GetRegistPossibleEmplrList(context.Context) ([]model.MSTEmplr, error)
	Update(context.Context, model.Emplr) error
	Delete(context.Context, int) error
}
