// Package csv provides functionality to write data to a CSV file.
package csv

import (
	"encoding/csv"
	"log/slog"
	"os"
	"path/filepath"
)

type CsvClient struct {
}

func NewCSVWriter() CsvClient {

	return CsvClient{}
}

func (a *CsvClient) WriteCSVFile(filePath string, header []string, data [][]string) error {
	// CSVファイルを作成
	file, err := os.Create(filepath.Clean(filePath))
	if err != nil {
		// log.Fatalf("CSVファイル作成エラー: %v", err)
		return err
	}
	defer func() {
		if closeErr := file.Close(); closeErr != nil {
			slog.Error("file close error", "error", closeErr)
		}
	}()

	// CSVライターを作成
	writer := csv.NewWriter(file)
	writer.UseCRLF = true // Windows用にCRLFを使用
	defer writer.Flush()

	// ヘッダーを書き込む
	err = writer.Write(header)
	if err != nil {
		return err
	}

	// データを書き込む
	for _, record := range data {
		err := writer.Write(record)
		if err != nil {
			return err
		}
	}

	return nil
}
