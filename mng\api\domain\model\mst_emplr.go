// Package model is model definition.
package model

import (
	"fmt"

	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
)

type MSTEmplr struct {
	EmplrID              int
	EmplrCD              string
	SecondaryUseProperty int
	EmplrName            string
}

func (e *MSTEmplr) NameWithLabel() string {
	return fmt.Sprintf("%d %s", e.EmplrID, e.EmplrName)
}

func NewMSTEmplr(row dbmodels.MSTEmplr) MSTEmplr {
	return MSTEmplr{
		EmplrID:              row.EmplrID,
		EmplrCD:              row.EmplrCD,
		SecondaryUseProperty: row.SecondaryUseProperty,
		EmplrName:            row.EmplrName,
	}
}

func NewMSTEmplrs(rows []dbmodels.MSTEmplr) []MSTEmplr {
	emplrs := make([]MSTEmplr, 0, len(rows))
	for _, row := range rows {
		emplrs = append(emplrs, NewMSTEmplr(row))
	}
	return emplrs
}
