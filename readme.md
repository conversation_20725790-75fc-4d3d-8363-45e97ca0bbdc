<!-- <p align="center">
  <img src="doc/assets/J-notice_logo_yoko.png" alt="owl" width="2000">
</p>
<p align="center">
  <img src="doc/assets/Manuel_1.png" alt="owl" width="120">
</p> -->

<p align="center">
  <br>
  <img src="https://img.shields.io/badge/Golang-white?style=plastic&logo=go" alt="Golang Badge">
  <img src="https://img.shields.io/badge/Docker-white?style=plastic&logo=docker" alt="Docker Badge">
  <img src="https://img.shields.io/badge/OpenAPI-white?style=plastic&logo=openapi" alt="OpenAPI Badge">
  <img src="https://img.shields.io/badge/lefthook-white?style=plastic&logo=lefthook" alt="lefthook Badge">
  <img src="https://img.shields.io/badge/swagger_merger-white?style=plastic&logo=swagger" alt="swagger Badge">
  <img src="https://img.shields.io/badge/yarn-white?style=plastic&logo=yarn" alt="yarn Badge">
</p>

## 内容

- kensuke plus 管理ツールの Backend を管理しています。

## 事前準備

- Mac で開発する場合は [Docker Desktop](https://www.docker.com/ja-jp/products/docker-desktop/) と [brew](https://brew.sh/) を導入してください。
  - Docker Desktop は商用利用になるため、利用申請が必要になります。
- Windows で開発する場合は [WSL2 と docker の環境を構築](https://jmdc.atlassian.net/wiki/spaces/PDN/pages/13973159993/JLetter) してください

### Zscaler証明書のダウンロード(JMDC)
- [zip ファイル](https://drive.google.com/file/d/1_T8lMFJvHaW49pHSPU3t6q2RjTnCR04Q/view)をダウンロードおよび解凍して証明書（crt）を `build/docker/cert/` においてください
- [Windowsユーザ向け]
  - ファイル名が Mac 用と書いていますが、気にせずダウンロードしてください
- Zscaler に関するドキュメント
  - https://jmdc.atlassian.net/wiki/spaces/JMDC/pages/14730199145/SASE-Zscaler+SASE
  - https://jmdc.atlassian.net/wiki/spaces/JMDC/pages/14656115647/SASE+Zscaler+Client+Connector+ZCC
  - https://jmdc.atlassian.net/wiki/spaces/JMDC/pages/14679572993/SASE-Zscaler+QA
  - https://jmdc.atlassian.net/wiki/spaces/JMDC/pages/14694057426/SASE-Zscaler
- [FYI] https://docs.docker.com/guides/zscaler/ を参考に Zscaler を導入した状態で Docker を使えるようにしました

## ディレクトリ構成

```shell
root/
├── public/：メインサービスのコード(Retty側)
  ├─── api/：
  ├─── openapi/：
├── mng/：管理ツールのコード(JMDC側)
  ├─── api/：
  ├─── openapi/：
├── build/: ローカル環境の構築用ファイルを管理
├── docker/: デプロイ用 Dockerfile を管理
├── doc/: ドキュメントを管理
├── pkg/: ライブラリとして扱うパッケージを管理
```

## 環境構築

**Go を install してください。**

Mac
```shell
$ wget https://go.dev/dl/go1.24.1.darwin-arm64.tar.gz
$ sudo rm -rf /usr/local/go && sudo tar -C /usr/local -xzf go1.24.1.darwin-arm64.tar.gz
```

linux（ubuntu）
```shell
$ wget https://go.dev/dl/go1.24.1.linux-amd64.tar.gz
$ sudo rm -rf /usr/local/go && sudo tar -C /usr/local -xzf go1.24.1.linux-amd64.tar.gz
```

**使用する shell に PATH を追記してください。**
```shell
$ vi ~/.zshrc # bash の場合は .bashrc

# 以下を行末に追加してください ※ PATH の記載があれば不要です
export PATH=$PATH:/usr/local/go/bin
export GOROOT=/usr/local/go
export GOPATH=$HOME/go
export PATH=$PATH:$GOPATH/bin
```

**設定の適用をしてください。**
```shell
$ source ~/.zshrc # bashの場合は.bashrc
```

**node.js & yarn の確認をしてください。**

```shell
$ node -v
$ yarn -v
```
**必要であれば node.js & yarn の install をしてください。**

mac
```shell
$ brew install node
$ brew install yarn
$ node -v
$ yarn -v
```

linux（ubuntu）
```shell
$ sudo apt install -y nodejs npm
$ npm install yarn # -g オプションをつければグローバルインストールでであればパスの指定が不要になります。
$ node -v
$ yarn -v # もしくは ./node_modules/.bin/yarn -v
```

**module の配置をします。**
```shell
$ yarn install # もしくは ./node_modules/.bin/yarn install
```

**DB をセットアップします。**

#TODO 

以上で終了です🙌

**Tips**
```shell
# ルートディレクトリで make を実行すると makefile に設定されているコマンドが確認できます。
$ make
```

**Enjoy your development !! 🥳**
oapi-codegen -config config.yaml openapi.yaml

### Linter
ローカル環境でLinterを動かすのは下記を参照
https://golangci-lint.run/welcome/install/#binaries




### デバッグ構成(WSL)
- Database
　build/docker/compose.test_db.ymlにローカルデバッグ用のDockerのPostgresがあるので事前に立てておく。
　もしくはmain/env.ymlに設定ファイルがあるので、databaseの接続欄をを修正する
- AWS SSO
  S3やAuth0のパラメーター（ParameterStore）にアクセスするために、
  事前にAWS SSO（Default）でkensuke-plus-devにアクセスできるようにしておく

 ### 今後行うこと(publicフォルダ配下)
 1. API(openapi)の定義
 　/openapi/openapi_public.ymlにAPIを定義し「openapi-gen」コマンドでI/Fを作成する。
 1. 上記で実装されたI/Fの中身を実装していく。
 Auth0の連携やAWSの連携など/pkg/配下で実装していく。
 1. Docker化しAWSにデプロイできるようにする。
 1. ginkoを利用して単体テストを実装していく。

### フォルダ構成
/public
/manage
publicはメインサービス用のコードを配置するフォルダ。
manageは管理ツール用のコードを配置するフォルダ。
pkgとdocker関連は共通して利用するので処理は分けていない。