name: Lint

on:
  pull_request:
    types:
      - opened
      - synchronize
      - reopened

permissions:
  contents: read
  actions: read
  pull-requests: read

jobs:
  paths:
    if: ${{ github.event.pull_request.draft == false }}
    uses: ./.github/workflows/paths-filter.yml
    with:
      additionalTriggerFile: ".github/workflows/lint.yml"

  lint-manage:
    needs: [paths]
    if: ${{ github.event.pull_request.draft == false && needs.paths.outputs.isManageChanged == 'true' }}
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v4
      - name: Specify golang version
        uses: actions/setup-go@v5
        with:
          go-version-file: "go.mod"
      - name: Lint
        working-directory: ./mng
        run: make lint

  lint-public:
    needs: [ paths ]
    if: ${{ github.event.pull_request.draft == false && needs.paths.outputs.isPublicChanged == 'true' }}
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v4
      - name: Specify golang version
        uses: actions/setup-go@v5
        with:
          go-version-file: "go.mod"
      - name: Lint
        working-directory: ./public
        run: make lint

  lint-package:
    needs: [ paths ]
    if: ${{ github.event.pull_request.draft == false && needs.paths.outputs.isPackageChanged == 'true' }}
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v4
      - name: Specify golang version
        uses: actions/setup-go@v5
        with:
          go-version-file: "go.mod"
      - name: Lint
        working-directory: ./pkg
        run: go run github.com/golangci/golangci-lint/v2/cmd/golangci-lint@v2.1.6 run -c ../configs/.golangci.yaml
