// Package model is model definition.
package model

import (
	"fmt"
	"time"

	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
)

type UserDetail struct {
	UserID  int
	Auth0Id string
	Name    string
	Email   string
	Role    int
	Comment string

	IsAdmin   bool
	IsBlocked bool
	IsMfa     bool
	LastLogin time.Time

	EmplrID         int
	EmplrCD         string
	CustomEmplrName string

	HierarchyLevel int
	JigyosyoCD     string
}

func (e *UserDetail) NameWithLabel() string {
	return fmt.Sprintf("%d %s", e.EmplrID, e.Name)
}

func NewUserDetail(row dbmodels.MNGUserDetail) UserDetail {
	return UserDetail{
		UserID:          row.UserID,
		Auth0Id:         row.Auth0ID,
		Name:            row.Name,
		Email:           row.Email,
		Role:            row.Role,
		Comment:         row.Comment,
		EmplrID:         row.EmplrID,
		EmplrCD:         row.EmplrCD,
		CustomEmplrName: row.CustomEmplrName,
		HierarchyLevel:  row.HierarchyLevel,
		JigyosyoCD:      row.JigyosyoCD,
	}
}
