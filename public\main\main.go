// main.go
package main

import (
	_ "embed"
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"github.com/jmdc-inc/kensuke-plus-server/pkg/config"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/util"
	"github.com/jmdc-inc/kensuke-plus-server/public/router"
)

//go:embed env.yml
var envYaml []byte

// init関数はmain関数よりも先に実行される
func init() {
	util.IntLocal()
}

func main() {
	config.EnvYaml = envYaml
	appConfig := config.Prepare()
	appLogger := slog.New(slog.NewJSONHandler(os.Stdout, nil))
	slog.SetDefault(appLogger)

	r := router.NewPublicRouter(appLogger, appConfig)

	appLogger.Info("Starting the server")

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGTERM, os.Interrupt)

	serverAddr := fmt.Sprintf(":%s", appConfig.Server.Port)
	appLogger.Info(fmt.Sprintf("Server running on %s", serverAddr))
	s := &http.Server{
		Handler:     r,
		Addr:        serverAddr,
		ReadTimeout: 5 * 1e9,
	}
	// サーバー起動
	if err := s.ListenAndServe(); err != nil {
		appLogger.Error("server error:", slog.Any("cause", err))
		os.Exit(1)
	}
}
