// Package repository is implements DB connection.
package repository

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/repository"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/aws/route53"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/errof"
)

type servicestatus struct {
	route53Client route53.Route53Client
}

func NewServiceStatus(route53Client route53.Route53Client) repository.ServiceStatusRepository {
	return &servicestatus{route53Client}
}

func (r *servicestatus) Get(ctx context.Context) (int, error) {
	isHelthCheck, err := r.route53Client.GetHelthCheck(ctx)
	if err != nil {
		// エラーが発生しているのでエラー情報を返す
		return 0, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	serviceStatus := 0
	if isHelthCheck {
		// サービスが稼働している場合は、ステータスを1に設定
		serviceStatus = 1
	} else {
		// サービスが停止している場合は、ステータスを0に設定
		serviceStatus = 0
	}

	return serviceStatus, nil
}

func (r *servicestatus) Update(ctx context.Context, stauts int) error {

	var helthcheck bool
	if stauts == 0 {
		// サービスが稼働している場合は、ステータスを0に設定
		helthcheck = true
	}

	err := r.route53Client.UpdateHelthCheck(ctx, helthcheck)

	if err != nil {

		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	return nil
}
