env:
  name: "local"

production:
  database:
    host: ""
    port: 5433
    name: ""
    user: ""
    password: ""
    ssl: disable
  cors:
    should_debug: true
    allow_origins:

development:
  database:
    host: vertica-stg-node1.kensuke.local
    port: 5433
    name: crgdb_st
    user: dbadmin
    password: ""
    ssl: disable
  cors:
    should_debug: true
    allow_origins:

local:
  database:
    host: localhost
    port: 5434
    name: postgres
    user: postgres
    password: postgres
    ssl: disable
  cors:
    should_debug: true
    allow_origins:
  auth0:
      callback_url: http://localhost:3000/callback
  aws:
    attached_file_bucket_name: kensuke-plus-dev-notice-attached-file
    share_snowflake_bucket_name: kensuke-plus-dev-notice-attached-file
    helthcheck_id: c2eb30d8-2509-4caa-97e3-2a58a6968bc6

test:
  database:
    host: j-notice-vertica_test
    port: 5433
    name: VMart
    user: dbadmin
    password: vertica
    ssl: disable
  cors:
    should_debug: true
    allow_origins:
