version: "2"
run:
  go: "1.24"
  modules-download-mode: readonly
linters:
  enable:
    - bodyclose
    - errname
    - errorlint
    - gosec
    - misspell
    - sqlclosecheck
    - staticcheck
  exclusions:
    generated: lax
    rules:
      - linters:
          - gosec
          - staticcheck
        path: _test.go
      - linters:
          - staticcheck
        path: api/handler/
    paths:
      - third_party$
      - builtin$
      - examples$
issues:
  max-issues-per-linter: 0
  max-same-issues: 0
formatters:
  enable:
    - gofmt
    - goimports
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
