package handler

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
)

// 組合階層追加
// (PUT /hierarchy-role)
func (h *API) PutHierarchyRole(ctx context.Context, request oapi.PutHierarchyRoleRequestObject) (oapi.PutHierarchyRoleResponseObject, error) {
	err := h.hierarchyRoleUseCase.PutHierarchyRole(ctx, request)
	if err != nil {
		return oapi.PutHierarchyRole500JSONResponse{
			Message: err.Error(),
		}, err
	}
	return oapi.PutHierarchyRole204Response{}, nil
}

// 組合階層削除
// (DELETE /hierarchy-role)
func (h *API) DeleteHierarchyRole(ctx context.Context, request oapi.DeleteHierarchyRoleRequestObject) (oapi.DeleteHierarchyRoleResponseObject, error) {
	err := h.hierarchyRoleUseCase.DeleteHierarchyRole(ctx, request.Params.Id)
	if err != nil {
		return oapi.DeleteHierarchyRole500JSONResponse{
			Message: err.Error(),
		}, err
	}
	return oapi.DeleteHierarchyRole204Response{}, nil
}

// 組合階層取得
// (GET /hierarchy-role)
func (h *API) GetHierarchyRole(ctx context.Context, request oapi.GetHierarchyRoleRequestObject) (oapi.GetHierarchyRoleResponseObject, error) {
	hierarchyRoleList, err := h.hierarchyRoleUseCase.GetHierarchyRole(ctx, request.Params.Id)
	if err != nil {
		return oapi.GetHierarchyRole500JSONResponse{
			Message: err.Error(),
		}, err
	}

	return hierarchyRoleList, nil
}

// 階層情報ロールの一覧取得
// (GET /hierarchy-role-list)
func (h *API) GetHierarchyRoleList(ctx context.Context, request oapi.GetHierarchyRoleListRequestObject) (oapi.GetHierarchyRoleListResponseObject, error) {
	hierarchyRoleList, err := h.hierarchyRoleUseCase.GetListHierarchyRole(ctx, request.Params.EmplrId)
	if err != nil {
		return oapi.GetHierarchyRoleList500JSONResponse{
			Message: err.Error(),
		}, err
	}
	return hierarchyRoleList, nil
}

// 事業所CDリストの取得
// (GET /jigyosyo-cd-list)
func (h *API) GetJigyosyoCdList(ctx context.Context, request oapi.GetJigyosyoCdListRequestObject) (oapi.GetJigyosyoCdListResponseObject, error) {
	jigyousyoCDList, err := h.hierarchyRoleUseCase.GetJigyosyoCdList(ctx, request.Params.Id)
	if err != nil {
		return oapi.GetJigyosyoCdList500JSONResponse{
			Message: err.Error(),
		}, err
	}
	return jigyousyoCDList, nil
}

// 階層情報ロールの一覧取得
// (GET /hierarchy-role-all-list)
func (h *API) GetHierarchyRoleAllList(ctx context.Context, request oapi.GetHierarchyRoleAllListRequestObject) (oapi.GetHierarchyRoleAllListResponseObject, error) {
	hierarchyRoleList, err := h.hierarchyRoleUseCase.GetAllListHierarchyRole(ctx)
	if err != nil {
		return oapi.GetHierarchyRoleAllList500JSONResponse{
			Message: err.Error(),
		}, err
	}
	return hierarchyRoleList, nil
}
