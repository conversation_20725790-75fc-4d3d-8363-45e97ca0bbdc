// Package model_test is Test of model definitions.
package model_test

import (
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/model"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Emplr", Ordered, func() {
	var emplr *model.Emplr

	Describe("NameWithLabel():LabelとNameを結合する文字列を取得するテスト", func() {
		// BeforeAll(func() {
		// 	emplr = &model.Emplr{
		// 		ID:    1,
		// 		Name:  "FOO",
		// 		Label: "JMDC健康保険組合",
		// 	}
		// })

		It("NameとLabelが結合される", func() {
			Expect(emplr.NameWithLabel()).To(Equal("FOO JMDC健康保険組合"))
		})
	})
})
