// Package repository is DB connection.
package repository

import (
	"context"

	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/model"
)

type UserRepository interface {
	Create(context.Context, model.User) error
	GetUserDetail(context.Context, int) (*model.UserDetail, error)
	GetUserByEmailExist(context.Context, string) (bool, error)
	GetUserByEmail(context.Context, string) (*model.User, error)
	GetList(context.Context) ([]model.UserSimple, error)
	Update(context.Context, model.User) error
	Delete(context.Context, int) error
}
