package usecase

import (
	"context"

	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/repository"
	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
)

type serviceStatus struct {
	serviceStatusRepo repository.ServiceStatusRepository
}

func NewServiceStatus(serviceStatusRepo repository.ServiceStatusRepository) ServiceStatusUseCase {
	return &serviceStatus{
		serviceStatusRepo,
	}
}

func (u serviceStatus) GetServerStatus(ctx context.Context) (oapi.GetServiceStatusResponseObject, error) {
	serviceStatus, err := u.serviceStatusRepo.Get(ctx)
	if err != nil {
		return nil, err
	}
	return oapi.GetServiceStatus200JSONResponse{
		Status: serviceStatus,
	}, err
}

func (u serviceStatus) PatchServerStatus(ctx context.Context, request oapi.PatchServiceStatusRequestObject) error {
	return u.serviceStatusRepo.Update(ctx, int(request.Params.Status))
}
