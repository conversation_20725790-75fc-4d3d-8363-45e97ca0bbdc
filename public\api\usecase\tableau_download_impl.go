package usecase

import (
	"context"

	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/service"
)

type tableauDownloadUseCase struct {
	tableauDownloadService service.TableauDownloadService
}

func NewTableauDownloadUseCase(tableauDownloadService service.TableauDownloadService) TableauDownloadUseCase {
	return &tableauDownloadUseCase{
		tableauDownloadService: tableauDownloadService,
	}
}

func (u *tableauDownloadUseCase) DownloadPDF(ctx context.Context, viewId string, filters map[string]interface{}) ([]byte, error) {
	return u.tableauDownloadService.DownloadPDF(ctx, viewId, filters)
}

func (u *tableauDownloadUseCase) DownloadCSV(ctx context.Context, viewId string, filters map[string]interface{}) ([]byte, error) {
	return u.tableauDownloadService.DownloadCSV(ctx, viewId, filters)
}
