package handler

import (
	"context"
	"errors"
	"fmt"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/public"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/model"
)

// PostUniqueJmdcBrdgIds returns unique JMDC_BRDG_ID list from SUBSCRIBER_INFO
// (POST /api/unique-jmdc-brdg-ids)
func (h *API) PostUniqueJmdcBrdgIds(ctx context.Context, request oapi.PostUniqueJmdcBrdgIdsRequestObject) (oapi.PostUniqueJmdcBrdgIdsResponseObject, error) {
	if request.Body == nil {
		return &oapi.PostUniqueJmdcBrdgIds500JSONResponse{
			Message: "missing request body",
		}, errors.New("missing request body")
	}

	reqBody := *request.Body

	filter := model.SubscriberInfoFilter{
		EmplrIDs:               reqBody.EmplrId,
		JigyosyoCDs:            reqBody.JigyosyoCd,
		FilterCDs:              reqBody.FilterCd,
		FilterNames:            reqBody.FilterName,
		FilterTypes:            reqBody.FilterType,
		AggregationCDs:         reqBody.AggregationCd,
		FiscalYears:            reqBody.FiscalYear,
		GenderFamilyKBNs:       reqBody.GenderFamilyKbn,
		Ages:                   reqBody.Age,
		AgeGroupsIncrements5s:  reqBody.AgeGroupsIncrements5,
		AgeGroupsIncrements10s: reqBody.AgeGroupsIncrements10,
	}

	response, err := h.subscriberInfoUseCase.GetUniqueJmdcBrdgIds(ctx, filter)
	if err != nil {
		return &oapi.PostUniqueJmdcBrdgIds500JSONResponse{
			Message: err.Error(),
		}, err
	}

	jmdcBrdgIdsStr := make([]string, len(response.JmdcBrdgIDs))
	for i, id := range response.JmdcBrdgIDs {
		jmdcBrdgIdsStr[i] = fmt.Sprintf("%d", id)
	}

	return &oapi.PostUniqueJmdcBrdgIds200JSONResponse{
		JmdcBrdgIds: jmdcBrdgIdsStr,
		Total:       &response.Total,
	}, nil
}
