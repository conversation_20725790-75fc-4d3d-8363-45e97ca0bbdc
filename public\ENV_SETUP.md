# Environment Setup

## Overview

This application uses a hybrid configuration approach:

- `env.yml` - Embedded YAML structure/template (defines config structure)
- `.env` - Environment variables that override YAML values (runtime overrides)

The configuration loading order:
1. Load `.env` file into environment variables (if exists)
2. Read embedded `env.yml` structure
3. <PERSON> automatically overrides YAML values with environment variables

## Setup Instructions

1. Create a `.env` file in the **public/** directory:

```bash
# Create .env file in public/ directory (public/.env)
cp public/.env.template public/.env
```

2. Add environment variables to `.env`:

## Environment Variables Mapping

Viper maps environment variables to YAML structure using dot notation replaced with underscores:

| YAML Path                   | Environment Variable | Description          |
|-----------------------------|----------------------|----------------------|
| `local.auth0.client_secret` | `LOCAL_AUTH0_CLIENT_SECRET` | Auth0 client secret  |
| `local.auth0.domain`        | `LOCAL_TABLEAU_JWT_SECRET_VALUE` | Tableau Secret Value |
| `local.auth0.connection`    | `LOCAL_TABLEAU_JWT_SECRET_ID` | Tableau Secret ID    |
| `local.sbowflake.password`  | `LOCAL_SNOWFLAKE_PASSWORD` | Snowflake Password   |

## Multi-Environment Support

For different environments, change the prefix:

**Development:**
```bash
ENV_NAME=development
DEVELOPMENT_DATABASE_HOST=dev-db-host
DEVELOPMENT_AUTH0_CLIENT_ID=dev_client_id
```

**Production:**
```bash
ENV_NAME=production
PRODUCTION_DATABASE_HOST=prod-db-host
PRODUCTION_AUTH0_CLIENT_ID=prod_client_id
```

## Docker Configuration

The application searches for `.env` file
`public/.env` (from project root - main location)

The Dockerfile automatically copies `public/.env` to the container working directory as `.env`.

## Notes

- Environment variables always override values in `env.yml`
- For local development, Auth0 credentials can be empty
- For production/staging, Auth0 credentials will be loaded from AWS Parameter Store if not provided
- The `.env` file is gitignored for security
- Use `LOCAL_` prefix for local environment overrides
- **Important**: Create `.env` file in the `public/` directory, not in project root
- File location: `public/.env` (relative to project root) 