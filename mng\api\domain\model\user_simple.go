// Package model is model definition.
package model

import (
	"fmt"

	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
)

type UserSimple struct {
	UserID         int
	Name           string
	Email          string
	Role           int
	EmplrCD        string
	EmplrName      string
	HierarchyLevel int
}

func (e *UserSimple) NameWithLabel() string {
	return fmt.Sprintf("%d %s", e.UserID, e.Name)
}

func NewUserSimple(row dbmodels.MNGUserSimple) UserSimple {
	return UserSimple{
		UserID:         row.UserID,
		Name:           row.Name,
		Email:          row.Email,
		Role:           row.Role,
		EmplrCD:        row.EmplrCD,
		EmplrName:      row.CustomEmplrName,
		HierarchyLevel: row.HierarchyLevel,
	}
}

func NewUserSimples(rows []dbmodels.MNGUserSimple) []UserSimple {
	users := make([]UserSimple, 0, len(rows))
	for _, row := range rows {
		users = append(users, NewUserSimple(row))
	}
	return users
}
