package repository

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/db"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/errof"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/model"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/repository"
)

type notice struct {
	dbClient db.Client
}

func NewNotice(dbClient db.Client) repository.NoticeRepository {
	return &notice{dbClient}
}

func (r *notice) GetNotices(ctx context.Context, filter model.NoticeListFilter) ([]*model.NoticeInfo, int, error) {
	var results []dbmodels.MNGNoticeInfo
	var total int

	countQuery := r.dbClient.DB.NewSelect().Model((*dbmodels.MNGNoticeInfo)(nil))

	total, err := countQuery.Count(ctx)
	if err != nil {
		return nil, 0, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	offset := (filter.Page - 1) * filter.Limit
	query := r.dbClient.DB.NewSelect().Model(&results).
		Order("release_date DESC").
		Limit(filter.Limit).
		Offset(offset)

	if err := query.Scan(ctx); err != nil {
		return nil, 0, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	retList := make([]*model.NoticeInfo, 0, len(results))
	for _, result := range results {
		item := &model.NoticeInfo{
			ID:          int(result.ID),
			Title:       result.Title,
			Comment:     result.Comment,
			FileName:    result.FileName,
			ReleaseDate: result.Date,
			RegUser:     result.RegUser,
			RegDate:     result.Date,
			UpdUser:     result.UpdUser,
			UpdDate:     result.Date,
		}
		retList = append(retList, item)
	}

	return retList, total, nil
}

func (r *notice) GetNoticeByID(ctx context.Context, id int) (*model.NoticeInfo, error) {
	var result dbmodels.MNGNoticeInfo

	if err := r.dbClient.DB.NewSelect().Model(&result).Where("id = ?", id).Scan(ctx); err != nil {
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	return &model.NoticeInfo{
		ID:          int(result.ID),
		Title:       result.Title,
		Comment:     result.Comment,
		FileName:    result.FileName,
		ReleaseDate: result.Date,
		RegUser:     result.RegUser,
		RegDate:     result.Date,
		UpdUser:     result.UpdUser,
		UpdDate:     result.Date,
	}, nil
}
