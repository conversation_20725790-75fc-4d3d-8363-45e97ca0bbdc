package authenticator

import (
	"context"
	"errors"
	"log"
	"log/slog"

	"github.com/auth0/go-auth0"
	"github.com/auth0/go-auth0/authentication"
	"github.com/auth0/go-auth0/authentication/database"
	"github.com/auth0/go-auth0/management"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/config"
)

// Authenticator is used to authenticate our users.
type Authenticator struct {
	ManagementAPI *management.Management
	API           *authentication.Authentication
	Connection    string
	ClientID      string
}

// New インスタンスの生成
func New(config config.Auth0) Authenticator {

	//通常APIの初期化
	api, err := authentication.New(
		context.Background(),
		config.Domain,
		authentication.WithClientID(config.ClientID),
		authentication.WithClientSecret(config.ClientSecret), // Optional depending on the grants used
	)
	if err != nil {
		log.Fatalf("failed to initialize the auth0 management API client: %+v", err)
	}

	//管理APIの初期化
	authManagementAPI, err := management.New(
		config.Domain,
		management.WithClientCredentials(context.Background(),
			config.ClientID, config.ClientSecret),
	)

	if err != nil {
		slog.Error("Auth0 Management APIの初期化エラー", "err", err)
		log.Fatalf("failed to initialize the auth0 management API client: %+v", err)
	}

	return Authenticator{
		ManagementAPI: authManagementAPI,
		API:           api,
		ClientID:      config.ClientID,
		Connection:    config.Connection,
	}
}

// 　CreateUser 新規Userの作成
func (a *Authenticator) CreateUser(ctx context.Context, name string, email string, description string) (*string, error) {

	password, err := GeneratePassword(20)
	if err != nil {
		slog.Error("パスワード生成エラー", "err", err)
		return nil, errors.New("パスワード生成エラー")
	}

	user := &management.User{
		Connection:    &a.Connection,
		Name:          auth0.String(name),
		Email:         auth0.String(email),
		Password:      auth0.String(password),
		EmailVerified: auth0.Bool(false),
		VerifyEmail:   auth0.Bool(false),
	}

	err = a.ManagementAPI.User.Create(ctx, user)
	if err != nil {
		// エラーが発生した場合はErrorを返す
		slog.Error("Auth0 ユーザー作成エラー", "err", err)
		return nil, errors.New("Auth0 ユーザー作成エラー")
	}

	return user.ID, err
}

// UpdateUser 情報の更新
func (a *Authenticator) UpdateUser(ctx context.Context, auth0UserID string, name *string, email *string, description *string) error {
	user, err := a.ManagementAPI.User.Read(ctx, auth0UserID)
	if err != nil {
		slog.Error("Auth0 ユーザー情報の取得エラー", "err", err)
		return errors.New("Auth0 ユーザー情報の取得エラー")
	}

	if name != nil {
		user.Name = name
	}
	if email != nil {
		user.Email = email
	}
	if description != nil {
		user.Description = description
	}

	err = a.ManagementAPI.User.Update(ctx, auth0UserID, user)
	if err != nil {
		slog.Error("Auth0 ユーザー情報の更新エラー", "err", err)
		return errors.New("Auth0 ユーザー情報の更新エラー")
	}

	return err
}

// DeleteUser 情報の削除
func (a *Authenticator) DeleteUser(ctx context.Context, auth0UserID string) error {
	err := a.ManagementAPI.User.Delete(ctx, auth0UserID)
	if err != nil {
		slog.Error("Auth0 ユーザー削除エラー", "err", err)
		return errors.New("Auth0 ユーザー削除エラー")
	}

	return err
}

func (a *Authenticator) MFAResetUser(ctx context.Context, auth0UserID string) error {
	err := a.ManagementAPI.User.DeleteAllAuthenticationMethods(ctx, auth0UserID)
	if err != nil {
		slog.Error("Auth0 ユーザーのMFAリセットエラー", "err", err)
		return errors.New("Auth0 ユーザーのMFAリセットエラー")
	}

	return err
}

func (a *Authenticator) BlockUser(ctx context.Context, auth0UserID string) error {

	user, err := a.ManagementAPI.User.Read(ctx, auth0UserID)
	if err != nil {
		slog.Error("Auth0 ユーザー情報の取得エラー", "err", err)
		return errors.New("Auth0 ユーザー情報の取得エラー")
	}

	user.Blocked = auth0.Bool(true)

	err = a.ManagementAPI.User.Update(ctx, auth0UserID, user)
	if err != nil {
		slog.Error("Auth0 ユーザーのブロックエラー", "err", err)
		return errors.New("Auth0 ユーザーのブロックエラー")
	}
	return err
}

func (a *Authenticator) UnblockUser(ctx context.Context, auth0UserID string) error {
	err := a.ManagementAPI.User.Unblock(ctx, auth0UserID)
	if err != nil {
		slog.Error("Auth0 ユーザーのブロック解除エラー", "err", err)
		return errors.New("Auth0 ユーザーのブロック解除エラー")
	}

	return err
}

func (a *Authenticator) ChangePassword(ctx context.Context, auth0UserID string) error {

	user, err := a.ManagementAPI.User.Read(ctx, auth0UserID)
	if err != nil {
		slog.Error("Auth0 ユーザー情報の取得エラー", "err", err)
		return errors.New("Auth0 ユーザー情報の取得エラー")
	}

	changePasswordRequest := database.ChangePasswordRequest{
		ClientID:   a.ClientID,
		Connection: a.Connection,
		Email:      *user.Email,
	}

	_, err = a.API.Database.ChangePassword(ctx, changePasswordRequest)
	if err != nil {
		slog.Error("Auth0 パスワード変更リクエストの送信エラー", "err", err)
		return errors.New("Auth0 パスワード変更リクエストの送信エラー")
	}
	return err
}
