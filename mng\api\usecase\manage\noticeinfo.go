package usecase

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
)

type NoticeInfoUseCase interface {
	PostNoticeInfo(ctx context.Context, request oapi.PostNoticeInfoRequestObject) error
	GetNoticeInfo(ctx context.Context, id int64) (oapi.GetNoticeInfoResponseObject, error)
	GetListNoticeInfo(ctx context.Context) (oapi.GetNoticeInfoListResponseObject, error)
	DeleteNoticeInfo(ctx context.Context, id int64) error
}
