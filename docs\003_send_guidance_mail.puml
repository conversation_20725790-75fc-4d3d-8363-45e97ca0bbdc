@startuml メール送信シーケンス
actor 管理者 as admin
participant "健助Plus管理サーバー" as mngserver
participant "Auth0サーバー" as auth0
participant "健助Plusサーバー" as server
actor 健保ユーザー as user



admin -> mngserver: パスワードの変更送信
activate mngserver
note left

　■パスワードの変更
  　以下のURLよりパスワードの変更をお願いします。
    https://xxxxx

end note

mngserver -> auth0: change_password
activate auth0
auth0 -> user:パスワードの変更Linkの送信
auth0 --> mngserver:
deactivate auth0
mngserver --> admin:
deactivate mngserver

user -> auth0:パスワードの変更
activate auth0
auth0 --> user:
deactivate auth0

server -> user:ログインページ
activate server
server --> user:
deactivate server

user -> auth0:ログイン
activate auth0
auth0 --> user:
deactivate auth0

opt MFA未設定の場合
user -> auth0:MFA設定
activate auth0
auth0 --> user:
deactivate auth0
end

server -> user:Topページ
activate server
server --> user:
deactivate server


@enduml
