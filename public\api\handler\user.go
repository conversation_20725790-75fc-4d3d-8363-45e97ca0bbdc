package handler

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/public"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/middleware"
)

// Auth0トークンによるユーザー情報取得
// (GET /api/user-info)
func (h *API) GetApiUserInfo(ctx context.Context, request oapi.GetApiUserInfoRequestObject) (oapi.GetApiUserInfoResponseObject, error) {
	authorization := ctx.Value(middleware.AuthorizationKey)
	if authorization == nil {
		message := "Authorization header not found"
		return oapi.GetApiUserInfo401JSONResponse{
			Message: &message,
		}, nil
	}

	token, ok := authorization.(string)
	if !ok {
		message := "Invalid authorization header format"
		return oapi.GetApiUserInfo401JSONResponse{
			Message: &message,
		}, nil
	}

	userInfo, err := h.userUseCase.GetUserInfoByToken(ctx, token)
	if err != nil {
		return userInfo, err
	}

	return userInfo, nil
}

// Level 2 ユーザー一覧取得
// (GET /api/users/level2)
func (h *API) GetApiLevel2Users(ctx context.Context, request oapi.GetApiLevel2UsersRequestObject) (oapi.GetApiLevel2UsersResponseObject, error) {
	authorization := ctx.Value(middleware.AuthorizationKey)
	if authorization == nil {
		message := "Authorization header not found"
		return oapi.GetApiLevel2Users401JSONResponse{
			Message: message,
		}, nil
	}

	token, ok := authorization.(string)
	if !ok {
		message := "Invalid authorization header format"
		return oapi.GetApiLevel2Users401JSONResponse{
			Message: message,
		}, nil
	}

	userInfo, err := h.userUseCase.GetUserInfoByToken(ctx, token)
	if err != nil {
		message := "Authentication failed: " + err.Error()
		return oapi.GetApiLevel2Users401JSONResponse{
			Message: message,
		}, err
	}

	responseObj, ok := userInfo.(oapi.GetApiUserInfo200JSONResponse)
	if !ok {
		message := "Failed to get user info"
		return oapi.GetApiLevel2Users401JSONResponse{
			Message: message,
		}, nil
	}

	userDetail := responseObj

	if userDetail.HierarchyLevel != 1 {
		message := "Permission denied: Only admin can access this API"
		return oapi.GetApiLevel2Users401JSONResponse{
			Message: message,
		}, nil
	}

	usersList, err := h.userUseCase.GetLevel2Users(ctx, request.Params.Keyword)
	if err != nil {
		return usersList, err
	}

	return usersList, nil
}
