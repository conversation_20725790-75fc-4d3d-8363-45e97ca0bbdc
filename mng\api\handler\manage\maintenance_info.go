package handler

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
)

// メンテナンス情報削除
// (DELETE /maintenance-info)
func (h *API) DeleteMaintenanceInfo(ctx context.Context, request oapi.DeleteMaintenanceInfoRequestObject) (oapi.DeleteMaintenanceInfoResponseObject, error) {
	err := h.maintenanceInfoUseCase.DeleteMaintenanceInfo(ctx, request.Params.Id)
	if err != nil {
		return oapi.DeleteMaintenanceInfo500JSONResponse{
			Message: err.Error(),
		}, err
	}
	return oapi.DeleteMaintenanceInfo204Response{}, nil
}

// メンテナンス情報取得
// (GET /maintenance-info)
func (h *API) GetMaintenanceInfo(ctx context.Context, request oapi.GetMaintenanceInfoRequestObject) (oapi.GetMaintenanceInfoResponseObject, error) {
	maintenanceInfo, err := h.maintenanceInfoUseCase.GetMaintenanceInfo(ctx, request.Params.Id)
	if err != nil {
		return oapi.GetMaintenanceInfo500JSONResponse{
			Message: err.Error(),
		}, err
	}

	return maintenanceInfo, nil
}

// メンテナンス情報更新
// (PATCH /maintenance-info)
func (h *API) PatchMaintenanceInfo(ctx context.Context, request oapi.PatchMaintenanceInfoRequestObject) (oapi.PatchMaintenanceInfoResponseObject, error) {
	err := h.maintenanceInfoUseCase.PatchMaintenanceInfo(ctx, request)
	if err != nil {
		return oapi.PatchMaintenanceInfo500JSONResponse{
			Message: err.Error(),
		}, err
	}
	return oapi.PatchMaintenanceInfo204Response{}, nil
}

// メンテナンス情報作成
// (PUT /maintenance-info)
func (h *API) PutMaintenanceInfo(ctx context.Context, request oapi.PutMaintenanceInfoRequestObject) (oapi.PutMaintenanceInfoResponseObject, error) {

	err := h.maintenanceInfoUseCase.PutMaintenanceInfo(ctx, request)
	if err != nil {
		return oapi.PutMaintenanceInfo500JSONResponse{
			Message: err.Error(),
		}, err
	}

	return oapi.PutMaintenanceInfo204Response{}, nil
}

// メンテナンス情報一覧取得
// (GET /maintenance-info-list)
func (h *API) GetMaintenanceInfoList(ctx context.Context, request oapi.GetMaintenanceInfoListRequestObject) (oapi.GetMaintenanceInfoListResponseObject, error) {
	maintenanceInfoList, err := h.maintenanceInfoUseCase.GetListMaintenanceInfo(ctx)
	if err != nil {
		return oapi.GetMaintenanceInfoList500JSONResponse{
			Message: err.Error(),
		}, err
	}

	return maintenanceInfoList, nil
}
