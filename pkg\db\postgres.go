package db

import (
	"database/sql"
	"fmt"
	"log/slog"

	"github.com/jmdc-inc/kensuke-plus-server/pkg/config"
	_ "github.com/lib/pq"
)

// PostgresDB is a type alias for *sql.DB to specifically represent a PostgreSQL connection.
// This helps differentiate it from other *sql.DB instances (e.g., Snowflake) in dependency injection.
type PostgresDB *sql.DB

func NewPostgres(c config.Database, logger *slog.Logger) PostgresDB {
	var err error

	postgresURL := fmt.Sprintf("postgresql://%s:%s/%s?user=%s&password=%s&sslmode=disable", c.Host, c.Port, c.DBName, c.Username, c.Password)

	db, err := sql.Open("postgres", postgresURL)

	if err != nil {
		logger.Error("Failed to open postgres", "error", err)
		panic(err)
	}

	if err = db.Ping(); err != nil {
		logger.Error("Failed to ping postgres", "error", err)
		panic(err)
	}

	return db
}
