package handler

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
)

// お知らせ作成
// (POST /NoticeInfo)
func (h *API) PostNoticeInfo(ctx context.Context, request oapi.PostNoticeInfoRequestObject) (oapi.PostNoticeInfoResponseObject, error) {

	err := h.noticeInfoUseCase.PostNoticeInfo(ctx, request)
	if err != nil {
		return oapi.PostNoticeInfo500JSONResponse{
			Message: err.Error(),
		}, err
	}
	return oapi.PostNoticeInfo204Response{}, nil
}

// お知らせ削除
// (DELETE /NoticeInfo)
func (h *API) DeleteNoticeInfo(ctx context.Context, request oapi.DeleteNoticeInfoRequestObject) (oapi.DeleteNoticeInfoResponseObject, error) {

	err := h.noticeInfoUseCase.DeleteNoticeInfo(ctx, request.Params.Id)
	if err != nil {
		return oapi.DeleteNoticeInfo500JSONResponse{
			Message: err.Error(),
		}, err
	}
	return oapi.DeleteNoticeInfo204Response{}, nil
}

// お知らせ取得
// (GET /NoticeInfo)
func (h *API) GetNoticeInfo(ctx context.Context, request oapi.GetNoticeInfoRequestObject) (oapi.GetNoticeInfoResponseObject, error) {

	noticeInfo, err := h.noticeInfoUseCase.GetNoticeInfo(ctx, request.Params.Id)
	if err != nil {
		return oapi.GetNoticeInfo500JSONResponse{
			Message: err.Error(),
		}, err
	}
	return noticeInfo, nil
}

// お知らせ一覧取得
// (GET /NoticeInfo-list)
func (h *API) GetNoticeInfoList(ctx context.Context, request oapi.GetNoticeInfoListRequestObject) (oapi.GetNoticeInfoListResponseObject, error) {
	noticeInfoList, err := h.noticeInfoUseCase.GetListNoticeInfo(ctx)
	if err != nil {
		return oapi.GetNoticeInfoList500JSONResponse{
			Message: err.Error(),
		}, err
	}
	return noticeInfoList, nil
}
