# Goの公式イメージを使用
FROM golang:1.24-alpine AS builder


WORKDIR /app

# 依存関係をキャッシュ
COPY go.mod go.sum ./
RUN go mod download

# ソースコードをコピー
COPY . .

# Goバイナリをビルド
RUN CGO_ENABLED=0 GOOS=linux go build -o main ./main/main.go

# 実行用イメージ（Alpine Linuxを使用）
FROM alpine:latest

WORKDIR /root/

# 必要な証明書をインストール（https通信などで必要）
RUN apk --no-cache add ca-certificates

# ビルドしたバイナリをコピー
COPY --from=builder /app/main .

# ポート番号（必要に応じて変更）
EXPOSE 8080

# エントリーポイント
CMD ["./main"]


