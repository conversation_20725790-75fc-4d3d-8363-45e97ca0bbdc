package repository

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/authenticator"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/db"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/errof"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/model"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/repository"
	"github.com/uptrace/bun"
)

type user struct {
	dbClient    db.Client
	auth0Client authenticator.Authenticator
}

func NewUser(dbClient db.Client, auth0Client authenticator.Authenticator) repository.UserRepository {
	return &user{dbClient, auth0Client}
}

func (r *user) GetUserDetailByAuth0Id(ctx context.Context, auth0Id string) (*model.UserDetail, error) {
	var result dbmodels.MNGUserDetail

	if err := r.dbClient.DB.NewSelect().Model(&result).Where("auth0_id = ?", auth0Id).
		Column("user_id").
		Column("auth0_id").
		Column("name").
		Column("email").
		Column("role").
		Column("comment").
		Column("tableau_id").
		Column("e.emplr_id").
		Column("e.emplr_cd").
		ColumnExpr("e.emplr_name AS custom_emplr_name").
		Column("h.hierarchy_level").
		Column("h.jigyosyo_cd").
		Join("JOIN kensuke_plus.tbl_mng_hierarchy_role as h").
		JoinOn("h.hierarchy_role_id = tmu.hierarchy_role_id").
		Join("JOIN kensuke_plus.tbl_mst_emplr as e").
		JoinOn("e.emplr_id = h.emplr_id").Scan(ctx); err != nil {

		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	ret := model.UserDetail{
		UserID:         result.UserID,
		Auth0ID:        result.Auth0ID,
		Name:           result.Name,
		Email:          result.Email,
		Role:           result.Role,
		Comment:        result.Comment,
		TableauID:      result.TableauID,
		EmplrID:        result.EmplrID,
		EmplrCD:        result.EmplrCD,
		EmplrName:      result.CustomEmplrName,
		HierarchyLevel: result.HierarchyLevel,
		JigyosyoCD:     result.JigyosyoCD,
	}
	return &ret, nil
}

func (r *user) GetLevel2Users(ctx context.Context, keyword *string) ([]*model.UserDetail, error) {
	var results []dbmodels.MNGUserDetail

	if err := r.dbClient.DB.NewSelect().Model(&results).
		Column("user_id", "auth0_id", "name", "email", "role", "comment").
		Column("e.emplr_id").
		Column("e.emplr_cd").
		ColumnExpr("e.emplr_name AS custom_emplr_name").
		Column("h.hierarchy_level", "h.jigyosyo_cd").
		Join("JOIN kensuke_plus.tbl_mng_hierarchy_role as h").
		JoinOn("h.hierarchy_role_id = tmu.hierarchy_role_id").
		Join("JOIN kensuke_plus.tbl_mst_emplr as e").
		JoinOn("e.emplr_id = h.emplr_id").
		Where("h.hierarchy_level = ?", 2).
		Apply(func(query *bun.SelectQuery) *bun.SelectQuery {
			if keyword == nil {
				return query
			}
			keywordWithWildcard := "%" + *keyword + "%"
			return query.Where("e.emplr_cd ILIKE ? OR e.emplr_name ILIKE ?", keywordWithWildcard, keywordWithWildcard)
		}).
		Order("e.emplr_cd").
		Scan(ctx); err != nil {

		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	retList := make([]*model.UserDetail, 0, len(results))
	for _, result := range results {
		item := model.UserDetail{
			UserID:         result.UserID,
			Auth0ID:        result.Auth0ID,
			Name:           result.Name,
			Email:          result.Email,
			Role:           result.Role,
			Comment:        result.Comment,
			EmplrID:        result.EmplrID,
			EmplrCD:        result.EmplrCD,
			EmplrName:      result.CustomEmplrName,
			HierarchyLevel: result.HierarchyLevel,
			JigyosyoCD:     result.JigyosyoCD,
		}
		retList = append(retList, &item)
	}

	return retList, nil
}
