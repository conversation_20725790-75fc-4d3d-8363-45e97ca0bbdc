FROM golang:1.24-alpine

WORKDIR /app

COPY ../docker/cert/*.crt /cert/
RUN if [ -e /cert/ZscalerRootCertificate-2048-SHA256.crt ]; then \
        cp /cert/ZscalerRootCertificate-2048-SHA256.crt /usr/local/share/ca-certificates && \
        apk update && apk add --no-cache ca-certificates && update-ca-certificates; \
    fi

# 依存関係をキャッシュ
RUN go install github.com/air-verse/air@latest

COPY go.mod go.sum ./
RUN go mod download

COPY . .

WORKDIR /app/public

EXPOSE 8000

CMD ["/go/bin/air"]

