package model

import "time"

type SubscriberInfo struct {
	EmplrID               int       `json:"emplr_id"`
	JigyosyoCD            string    `json:"jigyosyo_cd"`
	FilterCD              string    `json:"filter_cd"`
	FilterName            string    `json:"filter_name"`
	FilterType            int       `json:"filter_type"`
	TargetYearMonth       time.Time `json:"target_year_month"`
	JmdcBrdgID            int       `json:"jmdc_brdg_id"`
	AggregationCD         int       `json:"aggregation_cd"`
	FiscalYear            int       `json:"fiscal_year"`
	GenderFamilyKBN       string    `json:"gender_family_kbn"`
	SelfFamyKBN           string    `json:"self_famy_kbn"`
	PtntGender            string    `json:"ptnt_gender"`
	PtntBirth             time.Time `json:"ptnt_birth"`
	FiscalYearStartMonth  time.Time `json:"fiscal_year_start_month"`
	FiscalYearEndMonth    time.Time `json:"fiscal_year_end_month"`
	BirthFiscalYear       int       `json:"birth_fiscal_year"`
	Age                   int       `json:"age"`
	AgeGroupsIncrements5  string    `json:"age_groups_increments_5"`
	AgeGroupsIncrements10 string    `json:"age_groups_increments_10"`
	CreatedAt             time.Time `json:"created_at"`
}

type SubscriberInfoFilter struct {
	EmplrIDs               []int    `json:"emplr_ids"`
	JigyosyoCDs            []string `json:"jigyosyo_cds"`
	FilterCDs              []string `json:"filter_cds"`
	FilterNames            []string `json:"filter_names"`
	FilterTypes            []int    `json:"filter_types"`
	AggregationCDs         []int    `json:"aggregation_cds"`
	FiscalYears            []int    `json:"fiscal_years"`
	GenderFamilyKBNs       []string `json:"gender_family_kbns"`
	Ages                   []int    `json:"ages"`
	AgeGroupsIncrements5s  []string `json:"age_groups_increments_5s"`
	AgeGroupsIncrements10s []string `json:"age_groups_increments_10s"`
}

type UniqueJmdcResponse struct {
	JmdcBrdgIDs []int `json:"jmdc_brdg_ids"`
	Total       int   `json:"total"`
}
