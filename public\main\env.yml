env:
  name: "local"

production:
  database:
    host: ""
    port: 5433
    name: ""
    user: ""
    password: ""
    ssl: disable
  cors:
    should_debug: true
    allow_origins:
  tableau:
    jwt:
      secret_value: ""
      client_id: ""
      secret_id: ""
      expiry_minutes: 10
    server:
      base_url: "https://viz.kensuke-plus.dev.jmdc.co.jp/api/3.19"
      username: "sonnh"
      password: ""
  snowflake:
    account: "your_snowflake_account_production"
    user: "your_snowflake_user_production"
    password: "your_snowflake_password_production"
    role: "your_snowflake_role_production"
    warehouse: "your_snowflake_warehouse_production"
    database: "your_snowflake_database_production"
    schema: "your_snowflake_schema_production"

development:
  database:
    host: vertica-stg-node1.kensuke.local
    port: 5433
    name: crgdb_st
    user: dbadmin
    password: ""
    ssl: disable
  cors:
    should_debug: true
    allow_origins:
  server:
    port: 8000
  auth0:
    client_id: 0RtmrKUSKuhcYBiwKQ6tecIXxc7TQs0A
    client_secret: ""
    domain: kensuke-plus-dev.jp.auth0.com
    connection: Username-Password-Authentication
  tableau:
    jwt:
      secret_value: ""
      client_id: 3e26f740-3857-4e43-8f69-801426637f65
      secret_id: ""
      expiry_minutes: 10
    server:
      base_url: "https://viz.kensuke-plus.dev.jmdc.co.jp/api/3.19"
      username: "sonnh"
      password: ""
  aws:
    attached_file_bucket_name: kensuke-plus-dev-notice-attached-file
    share_snowflake_bucket_name: kensuke-plus-dev-notice-attached-file
  snowflake:
    account: "your_snowflake_account_development"
    user: "your_snowflake_user_development"
    password: "your_snowflake_password_development"
    role: "your_snowflake_role_development"
    warehouse: "your_snowflake_warehouse_development"
    database: "your_snowflake_database_development"
    schema: "your_snowflake_schema_development"

local:
  database:
    host: postgres
    port: 5432
    name: postgres
    user: postgres
    password: postgres
    ssl: disable
  cors:
    should_debug: true
    allow_origins:
  server:
    port: 8000
  auth0:
    client_id: "0RtmrKUSKuhcYBiwKQ6tecIXxc7TQs0A"
    client_secret: ""
    domain: "kensuke-plus-dev.jp.auth0.com"
    connection: "Username-Password-Authentication"
  tableau:
    jwt:
      secret_value: ""
      client_id: "3e26f740-3857-4e43-8f69-801426637f65"
      secret_id: ""
      expiry_minutes: 10
    server:
      base_url: "https://viz.kensuke-plus.dev.jmdc.co.jp/api/3.19"
      username: "sonnh"
      password: ""
  aws:
    attached_file_bucket_name: kensuke-plus-dev-notice-attached-file
    share_snowflake_bucket_name: kensuke-plus-dev-notice-attached-file
  snowflake:
    account: "JMDC-KEN_PLUS_DEV"
    user: "DEV_SONNH"
    password: ""
    role: "JMDC_DEVELOPER_DEV"
    warehouse: "KENSUKE_PLUS_DEVELOPER_DEV"
    database: "KENSUKE_DEV"
    schema: "BI_WORK"

test:
  database:
    host: postgres
    port: 5432
    name: postgres
    user: postgres
    password: postgres
    ssl: disable
  cors:
    should_debug: true
    allow_origins:
  tableau:
    jwt:
      expiry_minutes: 10
  snowflake:
    account: "your_snowflake_account_test"
    user: "your_snowflake_user_test"
    password: "your_snowflake_password_test"
    role: "your_snowflake_role_test"
    warehouse: "your_snowflake_warehouse_test"
    database: "your_snowflake_database_test"
    schema: "your_snowflake_schema_test"
