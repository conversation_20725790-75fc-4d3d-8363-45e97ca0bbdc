// Package handler is sample handler for the API.
package handler

import (
	"context"
	"log/slog"
	"net/http"

	usecase "github.com/jmdc-inc/kensuke-plus-server/mng/api/usecase/manage"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/authenticator"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/aws/route53"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/aws/s3"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/config"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/db"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/errof"
	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
	"github.com/pkg/errors"
)

// Make sure we conform to StrictServerInterface
var _ oapi.StrictServerInterface = (*API)(nil)

type API struct {
	logger                 *slog.Logger
	config                 config.AppConfig
	dbClient               db.Client
	authClient             authenticator.Authenticator
	s3Client               s3.S3Client
	route53Client          route53.Route53Client
	emplrUseCase           usecase.EmplrUseCase
	hierarchyRoleUseCase   usecase.HierarchyRoleUseCase
	userUseCase            usecase.UserUseCase
	serviceStatusUseCase   usecase.ServiceStatusUseCase
	maintenanceInfoUseCase usecase.MaintenanceInfoUseCase
	noticeInfoUseCase      usecase.NoticeInfoUseCase
	csvImportUseCase       usecase.CsvImportUseCase
}

func NewAPI(
	logger *slog.Logger,
	appConfig config.AppConfig,
	dbClient db.Client,
	authClient authenticator.Authenticator,
	s3Client s3.S3Client,
	route53Client route53.Route53Client,
	emplrUseCase usecase.EmplrUseCase,
	hierarchyRoleUseCase usecase.HierarchyRoleUseCase,
	userUseCase usecase.UserUseCase,
	serviceStatusUseCase usecase.ServiceStatusUseCase,
	maintenanceInfoUseCase usecase.MaintenanceInfoUseCase,
	noticeInfoUseCase usecase.NoticeInfoUseCase,
	csvImportUseCase usecase.CsvImportUseCase,
) *API {
	return &API{
		logger,
		appConfig,
		dbClient,
		authClient,
		s3Client,
		route53Client,
		emplrUseCase,
		hierarchyRoleUseCase,
		userUseCase,
		serviceStatusUseCase,
		maintenanceInfoUseCase,
		noticeInfoUseCase,
		csvImportUseCase,
	}
}

func RequestErrorHandlerFunc() func(w http.ResponseWriter, r *http.Request, err error) {
	return func(w http.ResponseWriter, r *http.Request, err error) {
		http.Error(w, err.Error(), http.StatusBadRequest)
	}
}
func ResponseErrorHandlerFunc(logger *slog.Logger) func(w http.ResponseWriter, r *http.Request, err error) {
	return func(w http.ResponseWriter, r *http.Request, err error) {
		logError(logger, r.Context(), err)
		http.Error(w, errors.Cause(err).Error(), errof.HTTPStatusOf(err))
	}
}

func logError(logger *slog.Logger, ctx context.Context, err error) {
	switch _err := errors.Cause(err); {
	case errors.As(_err, errof.ErrPtr[errof.UserError]()):
		logger.WarnContext(ctx, _err.Error(), slog.Any("error", err))
	case errors.As(_err, errof.ErrPtr[errof.VerifyError]()):
		logger.WarnContext(ctx, _err.Error(), slog.Any("error", err))
	default:
		logger.WarnContext(ctx, _err.Error(), slog.Any("error", err))
	}
}
