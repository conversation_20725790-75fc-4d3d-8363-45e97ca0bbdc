// Package usecase is UseCase definition and implements.
package usecase

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
)

type UserUseCase interface {
	PutUser(context.Context, oapi.User, string) error
	GetUser(context.Context, int) (oapi.GetUserResponseObject, error)
	GetListUser(context.Context) (oapi.GetUserListResponseObject, error)
	PatchUser(context.Context, oapi.PatchUserRequestObject) error
	DeleteUser(context.Context, int) error
}
