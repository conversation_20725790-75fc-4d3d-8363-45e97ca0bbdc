// Package usecase is UseCase definition and implements.
package usecase

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
)

type CsvImportUseCase interface {
	ConfirmRegistUserList(ctx context.Context, request oapi.PostConfirmRegistUserListRequestObject) ([]oapi.RegistCsvResult, []string, error)
	ConfirmSendMailUserList(ctx context.Context, request oapi.PostConfirmSendMailUserListRequestObject) ([]oapi.RegistCsvResult, []string, error)
	RegistUserList(ctx context.Context, request oapi.PostRegistUserListRequestObject) ([]oapi.RegistCsvResult, []string, error)
	SendMailUserList(ctx context.Context, request oapi.PostSendMailUserListRequestObject) ([]oapi.RegistCsvResult, []string, error)
}
