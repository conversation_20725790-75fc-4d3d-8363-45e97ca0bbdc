@startuml
actor 管理者 as admin
participant "健助Plus管理サーバー" as mngserver
participant "Auth0サーバー" as auth0
participant "Postgres" as p

admin -> mngserver: 健保ユーザー作成
note left
  新規ユーザー作成
end note
activate mngserver
mngserver -> mngserver: パスワード生成（仮パスワード）
note left
  ここで作成されたパスワード実質使われない
end note

mngserver -> auth0: ユーザー作成（仮パスワード）
activate auth0
auth0 --> mngserver: Auth0のユーザーID
deactivate auth0
mngserver -> p: ユーザー作成(Auth0のユーザーID含む)

mngserver --> admin:


deactivate mngserver

@enduml
