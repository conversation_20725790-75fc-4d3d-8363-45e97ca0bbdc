package middleware

import (
	"context"
	"net/http"
)

type contextKey string

const AuthorizationKey contextKey = "Authorization"

func AuthHeaderMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		authHeader := r.Header.Get(string(AuthorizationKey))
		if authHeader != "" {
			ctx := context.WithValue(r.Context(), AuthorizationKey, authHeader)
			r = r.WithContext(ctx)
		}

		next.ServeHTTP(w, r)
	})
}
