//go:build wireinject
// +build wireinject

package injector

import (
	"log/slog"

	"github.com/google/wire"
	handler "github.com/jmdc-inc/kensuke-plus-server/mng/api/handler/manage"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/infra/repository"
	usecase "github.com/jmdc-inc/kensuke-plus-server/mng/api/usecase/manage"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/authenticator"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/aws/route53"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/aws/s3"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/config"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/csv"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/db"
)

func InitializeAPIHandler(*slog.Logger, config.Database, config.Auth0, config.AWS, config.AppConfig) (_ *handler.API) {
	wire.Build(
		db.NewPostgres,
		db.NewManagementClient,
		authenticator.New,
		s3.New,
		route53.New,
		csv.NewCSVWriter,
		repository.NewEmplr,
		usecase.NewEmplr,
		repository.NewHierarchyRole,
		usecase.NewHierarchyRole,
		repository.NewUser,
		usecase.NewUser,
		repository.NewServiceStatus,
		usecase.NewServiceStatus,
		repository.NewMaintenanceInfo,
		usecase.NewMaintenanceInfo,
		repository.NewNoticeInfo,
		usecase.NewNoticeInfo,
		usecase.NewCsvImport,

		handler.NewAPI,
	)
	return

}
