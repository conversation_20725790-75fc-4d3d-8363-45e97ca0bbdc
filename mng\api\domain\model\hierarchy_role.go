// Package model is model definition.
package model

import (
	"fmt"

	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
)

type HierarchyRole struct {
	ID             int
	EmplrID        int
	HierarchyLevel int
	JigyosyoCD     string
}

func (e *HierarchyRole) NameWithLabel() string {
	return fmt.Sprintf("%d %d", e.EmplrID, e.EmplrID)
}

func NewHierarchyRole(row dbmodels.MNGHierarchyRole) HierarchyRole {
	return HierarchyRole{
		ID:             *row.ID,
		EmplrID:        row.EmplrID,
		HierarchyLevel: row.HierarchyLevel,
		JigyosyoCD:     row.JigyosyoCD,
	}
}

func NewHierarchyRoles(rows []dbmodels.MNGHierarchyRole) []HierarchyRole {
	hierarchyRoles := make([]HierarchyRole, 0, len(rows))
	for _, row := range rows {
		hierarchyRoles = append(hierarchyRoles, NewHierarchyRole(row))
	}
	return hierarchyRoles
}
