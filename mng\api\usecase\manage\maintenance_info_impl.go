package usecase

import (
	"context"
	"time"

	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/model"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/repository"
	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
)

type maintenanceInfo struct {
	maintenanceInfoRepo repository.MaintenanceInfoRepository
}

func NewMaintenanceInfo(maintenanceInfoRepo repository.MaintenanceInfoRepository) MaintenanceInfoUseCase {
	return &maintenanceInfo{
		maintenanceInfoRepo,
	}
}

func (u maintenanceInfo) PutMaintenanceInfo(ctx context.Context, request oapi.PutMaintenanceInfoRequestObject) error {
	inputMaintenanceInfo := oapi.PutMaintenanceInfoJSONRequestBody(*request.Body)
	date, _ := time.Parse("2006-01-02", inputMaintenanceInfo.ReleaseDate)
	maintenanceInfo := model.MaintenanceInfo{
		Date:    date,
		Title:   inputMaintenanceInfo.Title,
		Comment: inputMaintenanceInfo.Comment,
	}
	err := u.maintenanceInfoRepo.Create(ctx, maintenanceInfo)

	if err != nil {
		return err
	}
	return nil
}

func (u maintenanceInfo) GetMaintenanceInfo(ctx context.Context, id int64) (oapi.GetMaintenanceInfoResponseObject, error) {
	maintenanceInfo, err := u.maintenanceInfoRepo.Get(ctx, id)
	if err != nil {
		return nil, err
	}
	return oapi.GetMaintenanceInfo200JSONResponse{
		Id:          &maintenanceInfo.ID,
		Title:       maintenanceInfo.Title,
		ReleaseDate: maintenanceInfo.Date.Format("2006-01-02"),
		Comment:     maintenanceInfo.Comment,
	}, nil
}

func (u maintenanceInfo) GetListMaintenanceInfo(ctx context.Context) (oapi.GetMaintenanceInfoListResponseObject, error) {
	maintenanceInfoList, err := u.maintenanceInfoRepo.GetList(ctx)
	if err != nil {
		return nil, err
	}

	results := make([]oapi.MaintenanceInfo, 0, len(maintenanceInfoList))
	for _, row := range maintenanceInfoList {
		maintenanceInfo := oapi.MaintenanceInfo{
			Id:          &row.ID,
			Title:       row.Title,
			ReleaseDate: row.Date.Format("2006-01-02"),
			Comment:     row.Comment,
		}
		results = append(results, maintenanceInfo)
	}

	return oapi.GetMaintenanceInfoList200JSONResponse(results), nil
}

func (u maintenanceInfo) PatchMaintenanceInfo(ctx context.Context, request oapi.PatchMaintenanceInfoRequestObject) error {
	maintenanceInfo := oapi.PatchMaintenanceInfoJSONRequestBody(*request.Body)
	date, _ := time.Parse("2006-01-02", maintenanceInfo.ReleaseDate)
	inputMaintenanceInfo := model.MaintenanceInfo{
		Date:    date,
		Title:   maintenanceInfo.Title,
		Comment: maintenanceInfo.Comment,
	}
	return u.maintenanceInfoRepo.Update(ctx, inputMaintenanceInfo)
}

func (u maintenanceInfo) DeleteMaintenanceInfo(ctx context.Context, id int64) error {

	return u.maintenanceInfoRepo.Delete(ctx, id)
}
