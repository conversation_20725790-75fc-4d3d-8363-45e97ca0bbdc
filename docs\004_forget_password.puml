@startuml
participant "Auth0サーバー" as auth0
participant "健助Plusサーバー" as server
actor 健保ユーザー as user

user -> auth0: パスワードの変更
activate auth0
auth0 -> user: パスワードの変更メールURL送信
auth0 --> user: 
deactivate auth0

user -> auth0: パスワードの変更
activate auth0
auth0 --> user: 
deactivate auth0

server -> user:ログインページ
activate server
server --> user:
deactivate server

user -> auth0:ログイン
activate auth0
auth0 --> user:
deactivate auth0

@enduml