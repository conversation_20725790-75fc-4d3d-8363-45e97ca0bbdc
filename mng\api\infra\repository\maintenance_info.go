// Package repository is implements DB connection.
package repository

import (
	"bytes"
	"context"
	"encoding/json"

	"github.com/cockroachdb/errors"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/model"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/repository"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/aws/s3"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/csv"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/db"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/errof"
)

type maintenanceInfo struct {
	dbClient  db.Client
	csvClient csv.CsvClient
	s3Client  s3.S3Client
}

// JSONの1件分の構造体
type MaintenanceInfoJSON struct {
	ReleaseDate string `json:"releaseDate"`
	Title       string `json:"title"`
	Comment     string `json:"comment"`
}

func NewMaintenanceInfo(dbClient db.Client, csvClient csv.CsvClient, s3Client s3.S3Client) repository.MaintenanceInfoRepository {
	return &maintenanceInfo{dbClient, csvClient, s3Client}
}
func (r *maintenanceInfo) Create(ctx context.Context, model model.MaintenanceInfo) error {
	input := dbmodels.MNGMaintenanceInfo{
		Date:    model.Date,
		Title:   model.Title,
		Comment: model.Comment,
	}

	_, err := r.dbClient.DB.NewInsert().Model(&input).Exec(ctx)
	if err != nil {

		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	err = r.exportAllToS3AsJSON(ctx)
	if err != nil {
		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	return nil
}

// メンテナンス情報を全て取得し、S3にJSON形式で保存する
func (r *maintenanceInfo) exportAllToS3AsJSON(ctx context.Context) error {
	var results []dbmodels.MNGMaintenanceInfo
	err := r.dbClient.DB.NewSelect().Model(&results).Scan(ctx)
	if err != nil {
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	var infos []MaintenanceInfoJSON
	for _, m := range results {
		infos = append(infos, MaintenanceInfoJSON{
			ReleaseDate: m.Date.Format("2006-01-02"),
			Title:       m.Title,
			Comment:     m.Comment,
		})
	}

	buf := new(bytes.Buffer)
	if err := json.NewEncoder(buf).Encode(infos); err != nil {
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	if err := r.s3Client.PutMaintenanceJsonFile(ctx, buf); err != nil {
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	return nil
}

func (r *maintenanceInfo) Get(ctx context.Context, id int64) (*model.MaintenanceInfo, error) {
	result := dbmodels.MNGMaintenanceInfo{}

	if err := r.dbClient.DB.NewSelect().Model(&result).Where("id = ?", id).Scan(ctx); err != nil {
		// エラーが発生しているのでエラー情報を返す
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	ret := model.NewMaintenanceInfo(result)

	return &ret, nil
}
func (r *maintenanceInfo) GetList(ctx context.Context) ([]model.MaintenanceInfo, error) {
	var results []dbmodels.MNGMaintenanceInfo
	if err := r.dbClient.DB.NewSelect().Model(&results).Scan(ctx); err != nil {
		// エラーが発生しているのでエラー情報を返す
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	return model.NewMaintenanceInfoList(results), nil
}

func (r *maintenanceInfo) Update(ctx context.Context, maintenanceInfo model.MaintenanceInfo) error {
	input := dbmodels.MNGMaintenanceInfo{
		ID:      maintenanceInfo.ID,
		Date:    maintenanceInfo.Date,
		Title:   maintenanceInfo.Title,
		Comment: maintenanceInfo.Comment,
	}

	_, err := r.dbClient.DB.NewUpdate().Model(&input).Where("id = ?", maintenanceInfo.ID).Exec(ctx)
	if err != nil {
		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	return nil
}
func (r *maintenanceInfo) Delete(ctx context.Context, id int64) error {
	model := dbmodels.MNGMaintenanceInfo{
		ID: id,
	}
	_, err := r.dbClient.DB.NewDelete().Model(&model).WherePK().Exec(ctx)
	if err != nil {
		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	err = r.exportAllToS3AsJSON(ctx)
	if err != nil {
		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	return nil
}
