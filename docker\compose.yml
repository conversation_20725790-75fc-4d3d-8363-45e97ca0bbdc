services:
  postgres:
    image: postgres:latest
    container_name: postgres_local
    restart: always
    volumes:
      - type: volume
        source: db
        target: /data
      - ./data:/etc/data
      - ./query:/etc/query
      - ./script:/etc/script
    ports:
      - "5434:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    tty:
      true
    networks:
      - rakuraku-network
  # api:
  #   container_name: kensuke-plus-api
  #   build:
  #     context: .
  #     dockerfile: ./Dockerfile
  #     target: debug
  #   working_dir: /go/src/app
  #   tty: true
  #   ports:
  #     - 2345:2345
  #   volumes:
  #     - ./../../:/go/src/app
  #     - /go/src/app/node_modules
  #   command: >
  #     sh -c "
  #       go run github.com/air-verse/air@latest -c .air.toml
  #     "
volumes:
  db:

networks:
  rakuraku-network:
    name: rakuraku-network
