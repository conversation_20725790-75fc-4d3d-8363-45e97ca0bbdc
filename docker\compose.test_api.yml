services:
  api_test:
    container_name: kensuke-plus-manager-api_test
    build:
      context: .
      dockerfile: ./Dockerfile
      target: debug
    working_dir: /go/src/app
    depends_on:
      db_test:
        condition: service_healthy
    environment:
      - J-NOTICE_ENV_NAME=test
    tty: true
    volumes:
      - ./../../:/go/src/app
    command: >
      sh -c "
          go run github.com/onsi/ginkgo/v2/ginkgo@latest -r -v
      "
