package model

import "time"

type MaintenanceInfo struct {
	ID          int       `json:"id"`
	Title       string    `json:"title"`
	Comment     string    `json:"comment"`
	ReleaseDate time.Time `json:"release_date"`
	RegUser     string    `json:"reg_user"`
	RegDate     time.Time `json:"reg_date"`
	UpdUser     string    `json:"upd_user"`
	UpdDate     time.Time `json:"upd_date"`
}

type MaintenanceListFilter struct {
	Page    int     `json:"page"`
	Limit   int     `json:"limit"`
	Keyword *string `json:"keyword"`
}

type MaintenanceListResponse struct {
	MaintenanceInfo []*MaintenanceInfo `json:"maintenance_info"`
	Total           int                `json:"total"`
	Page            int                `json:"page"`
	Limit           int                `json:"limit"`
	TotalPages      int                `json:"total_pages"`
}
