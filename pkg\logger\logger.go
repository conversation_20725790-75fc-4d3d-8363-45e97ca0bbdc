// Package logger : ロギングをするパッケージ
package logger

import (
	"context"
	"io"
	"log/slog"
	"os"
)

func NewLogger() slog.Handler {
	return NewOriginalHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	})
}

type OriginalHandler struct {
	jsonHandler *slog.JSONHandler
}

func NewOriginalHandler(w io.Writer, opts *slog.HandlerOptions) *OriginalHandler {
	return &OriginalHandler{
		jsonHandler: slog.NewJSONHandler(w, opts),
	}
}

func (h *OriginalHandler) Enabled(ctx context.Context, level slog.Level) bool {
	return h.jsonHandler.Enabled(ctx, level)
}

func (h *OriginalHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	return h.jsonHandler.WithAttrs(attrs)
}

func (h *OriginalHandler) WithGroup(name string) slog.Handler {
	return h.jsonHandler.WithGroup(name)
}

func (h *OriginalHandler) Handle(ctx context.Context, r slog.Record) error {
	return h.jsonHandler.Handle(ctx, r)
}
