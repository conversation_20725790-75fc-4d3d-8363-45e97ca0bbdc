// Package usecase is UseCase definition and implements.
package usecase

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
)

type HierarchyRoleUseCase interface {
	PutHierarchyRole(ctx context.Context, request oapi.PutHierarchyRoleRequestObject) error
	GetHierarchyRole(ctx context.Context, id int) (oapi.GetHierarchyRoleResponseObject, error)
	GetListHierarchyRole(ctx context.Context, id int) (oapi.GetHierarchyRoleListResponseObject, error)
	GetAllListHierarchyRole(ctx context.Context) (oapi.GetHierarchyRoleAllListResponseObject, error)
	GetJigyosyoCdList(ctx context.Context, id int) (oapi.GetJigyosyoCdListResponseObject, error)
	DeleteHierarchyRole(ctx context.Context, id int) error
}
