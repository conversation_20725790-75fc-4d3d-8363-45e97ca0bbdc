// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package injector

import (
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/handler/manage"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/infra/repository"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/usecase/manage"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/authenticator"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/aws/route53"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/aws/s3"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/config"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/csv"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/db"
	"log/slog"
)

// Injectors from wire.go:

func InitializeAPIHandler(logger *slog.Logger, database config.Database, auth0 config.Auth0, aws config.AWS, appConfig config.AppConfig) *handler.API {
	postgresDB := db.NewPostgres(database, logger)
	client := db.NewManagementClient(postgresDB, logger)
	authenticatorAuthenticator := authenticator.New(auth0)
	s3Client := s3.New(aws)
	route53Client := route53.New(aws)
	csvClient := csv.NewCSVWriter()
	emplrRepository := repository.NewEmplr(client, s3Client, csvClient)
	emplrUseCase := usecase.NewEmplr(emplrRepository)
	hierarchyRoleRepository := repository.NewHierarchyRole(client, s3Client, csvClient)
	hierarchyRoleUseCase := usecase.NewHierarchyRole(hierarchyRoleRepository)
	userRepository := repository.NewUser(client, authenticatorAuthenticator)
	userUseCase := usecase.NewUser(userRepository)
	serviceStatusRepository := repository.NewServiceStatus(route53Client)
	serviceStatusUseCase := usecase.NewServiceStatus(serviceStatusRepository)
	maintenanceInfoRepository := repository.NewMaintenanceInfo(client, csvClient, s3Client)
	maintenanceInfoUseCase := usecase.NewMaintenanceInfo(maintenanceInfoRepository)
	noticeInfoRepository := repository.NewNoticeInfo(client, s3Client)
	noticeInfoUseCase := usecase.NewNoticeInfo(noticeInfoRepository)
	csvImportUseCase := usecase.NewCsvImport(emplrRepository, hierarchyRoleRepository, userRepository, authenticatorAuthenticator)
	api := handler.NewAPI(logger, appConfig, client, authenticatorAuthenticator, s3Client, route53Client, emplrUseCase, hierarchyRoleUseCase, userUseCase, serviceStatusUseCase, maintenanceInfoUseCase, noticeInfoUseCase, csvImportUseCase)
	return api
}
