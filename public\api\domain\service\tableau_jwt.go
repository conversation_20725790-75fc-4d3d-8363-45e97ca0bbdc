package service

import (
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/config"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/errof"
	"github.com/pkg/errors"
)

type TableauJWTService interface {
	GenerateJWT(tableauID string) (string, error)
}

type tableauJWTService struct {
	secretKey string
	clientID  string
	secretID  string
	expiry    time.Duration
}

func NewTableauJWTService(cfg config.AppConfig) TableauJWTService {
	return &tableauJWTService{
		secretKey: cfg.Tableau.JWT.SecretValue,
		clientID:  cfg.Tableau.JWT.ClientID,
		secretID:  cfg.Tableau.JWT.SecretID,
		expiry:    time.Duration(cfg.Tableau.JWT.ExpiryMinutes) * time.Minute,
	}
}

func (s *tableauJWTService) GenerateJWT(tableauID string) (string, error) {
	if tableauID == "" {
		return "", errors.WithStack(errof.ErrTableauIDNotFound)
	}

	scopes := []string{"tableau:views:embed"}

	claims := jwt.MapClaims{
		"jti": uuid.NewString(),
		"aud": "tableau",
		"sub": tableauID,
		"scp": scopes,
		"exp": time.Now().Add(s.expiry).Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token.Header["kid"] = s.secretID
	token.Header["iss"] = s.clientID

	return token.SignedString([]byte(s.secretKey))
}
