# API Endpoint: /emplr-by-id

## 概要
このエンドポイントは、指定されたemployIdに基づいてTBL_MST_EMPLRテーブルから組合情報を取得します。

## エンドポイント詳細
- **URL**: `/emplr-by-id`
- **Method**: `POST`
- **Content-Type**: `application/json`

## リクエスト

### リクエストボディ
```json
{
  "employId": 100001
}
```

### パラメータ
- `employId` (integer, required): 取得したい組合のID

## レスポンス

### 成功時 (200 OK)
```json
{
  "emplr": {
    "emplrId": 100001,
    "emplrName": "テスト組合",
    "emplrLabel": "テスト組合ラベル",
    "rsvPtn": 1,
    "emplrKbn": 1,
    "receStYm": 202401,
    "receEdYm": 202412,
    "memStYm": 202401,
    "memEdYm": 202412,
    "heStYm": 202401,
    "heEdYm": 202412,
    "flaStYm": 202401,
    "flaEdYm": 202412,
    "fullStYm": 202401,
    "fullEdYm": 202412,
    "deptUseFlg": 1,
    "delFlg": 0,
    "lockCount": 0
  }
}
```

### エラー時

#### 400 Bad Request
```json
{
  "message": "Request body is required"
}
```

#### 404 Not Found
```json
{
  "message": "Employer not found"
}
```

#### 500 Internal Server Error
```json
{
  "message": "Internal server error"
}
```

## 使用例

### cURLを使用した例
```bash
curl -X POST "http://localhost:8080/emplr-by-id" \
  -H "Content-Type: application/json" \
  -d '{"employId": 100001}'
```

### JavaScriptを使用した例
```javascript
const response = await fetch('/emplr-by-id', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    employId: 100001
  })
});

const data = await response.json();
console.log(data);
```

## フィールド説明

| フィールド名 | 型 | 説明 |
|-------------|----|----|
| emplrId | integer | 組合ID |
| emplrName | string | 組合名 |
| emplrLabel | string | 組合ラベル |
| rsvPtn | integer | RSVパターン |
| emplrKbn | integer | 組合区分 |
| receStYm | integer | レセプト開始年月 |
| receEdYm | integer | レセプト終了年月 |
| memStYm | integer | メンバー開始年月 |
| memEdYm | integer | メンバー終了年月 |
| heStYm | integer | 健診開始年月 |
| heEdYm | integer | 健診終了年月 |
| flaStYm | integer | FLA開始年月 |
| flaEdYm | integer | FLA終了年月 |
| fullStYm | integer | FULL開始年月 |
| fullEdYm | integer | FULL終了年月 |
| deptUseFlg | integer | 部署使用フラグ |
| delFlg | integer | 削除フラグ |
| lockCount | integer | ロックカウント |

## 注意事項
- このエンドポイントはSnowflakeのTBL_MST_EMPLRテーブルからデータを取得します
- 存在しないemployIdを指定した場合は404エラーが返されます
- リクエストボディが空の場合は400エラーが返されます
