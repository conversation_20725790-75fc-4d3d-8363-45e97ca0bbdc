// Package dbmodels is models from database tables.
package dbmodels

import (
	"strconv"
	"time"

	"github.com/uptrace/bun"
)

type MNGHierarchyRole struct {
	bun.BaseModel  `bun:"kensuke_plus.tbl_mng_hierarchy_role"`
	ID             *int      `bun:"hierarchy_role_id,pk"`
	EmplrID        int       `bun:"emplr_id,notnull"`
	HierarchyLevel int       `bun:"hierarchy_level,notnull"`
	JigyosyoCD     string    `bun:"jigyosyo_cd"`
	RegDate        time.Time `bun:"reg_date,notnull,default:current_timestamp"`
	UpdDate        time.Time `bun:"upd_date,notnull,default:current_timestamp"`
}

// TableHeaders returns the headers for the CSV file.
func (e *MNGHierarchyRole) TableHeaders() []string {
	return []string{
		"hierarchy_role_id",
		"emplr_id",
		"hierarchy_level",
		"jigyosyo_cd",
		"reg_date",
		"upd_date",
	}
}
func (e *MNGHierarchyRole) ToStringArray() []string {
	return []string{
		strconv.Itoa(*e.ID),
		strconv.Itoa(e.EmplrID),
		strconv.Itoa(e.HierarchyLevel),
		e.JigyosyoCD,
		e.RegDate.Format("2006-01-02 15:04:05"),
		e.UpdDate.Format("2006-01-02 15:04:05"),
	}
}
