#!/usr/bin/env sh

# ベースディレクトリ
BASE_DIR="/etc/data"

# データベースユーザーとパスワード
USER="postgres"

# BASE_DIR の中の各サブディレクトリをスキーマ名とみなす
for schema_dir in "$BASE_DIR"/*; do
    if [ -d "$schema_dir" ]; then

        # スキーマ名をディレクトリ名から取得
        SCHEMA_NAME=$(basename "$schema_dir")

        echo "Processing schema: $SCHEMA_NAME"

        # 各スキーマ内のすべての CSV ファイルを対象テーブルにインポート
        for csv_file in "$schema_dir"/*.csv; do
            if [ -f "$csv_file" ]; then

                # テーブル名を CSV ファイル名から取得（拡張子 .csv を除去）
                TABLE_NAME=$(basename "$csv_file" .csv)

                echo "Truncating $SCHEMA_NAME.$TABLE_NAME..."

                # テーブルを TRUNCATE してからインポート
                psql -U "$USER" -c "TRUNCATE TABLE $SCHEMA_NAME.$TABLE_NAME;"

                echo "Importing $csv_file into $SCHEMA_NAME.$TABLE_NAME..."

                # COPY コマンドに CSV HEADER オプションを追加する
                psql -U "$USER" -c "\COPY $SCHEMA_NAME.$TABLE_NAME FROM '$csv_file' DELIMITER ',' CSV HEADER;"

                # インポート結果を確認
                if [ $? -eq 0 ]; then
                    echo "Successfully imported $csv_file into $SCHEMA_NAME.$TABLE_NAME"
                else
                    echo "Failed to import $csv_file into $SCHEMA_NAME.$TABLE_NAME"
                fi
            fi
        done
    fi
done
