package handler

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/public"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/middleware"
)

// お知らせ一覧取得（ページング付き）
// (GET /api/notices)
func (h *API) GetApiNotices(ctx context.Context, request oapi.GetApiNoticesRequestObject) (oapi.GetApiNoticesResponseObject, error) {
	authorization := ctx.Value(middleware.AuthorizationKey)
	if authorization == nil {
		message := "Authorization header not found"
		return oapi.GetApiNotices401JSONResponse{
			Message: message,
		}, nil
	}

	token, ok := authorization.(string)
	if !ok {
		message := "Invalid authorization header format"
		return oapi.GetApiNotices401JSONResponse{
			Message: message,
		}, nil
	}

	_, err := h.auth0TokenService.ParseAuth0Token(token)
	if err != nil {
		message := "Authentication failed: " + err.Error()
		return oapi.GetApiNotices401JSONResponse{
			Message: message,
		}, err
	}

	page := 1
	limit := 10

	if request.Params.Page != nil {
		page = *request.Params.Page
	}
	if request.Params.Limit != nil {
		limit = *request.Params.Limit
	}

	noticesList, err := h.noticeUseCase.GetNotices(ctx, page, limit, request.Params.Keyword)
	if err != nil {
		return noticesList, err
	}

	return noticesList, nil
}

// お知らせ詳細取得
// (GET /api/notices/{id})
func (h *API) GetApiNoticeById(ctx context.Context, request oapi.GetApiNoticeByIdRequestObject) (oapi.GetApiNoticeByIdResponseObject, error) {
	authorization := ctx.Value(middleware.AuthorizationKey)
	if authorization == nil {
		message := "Authorization header not found"
		return oapi.GetApiNoticeById401JSONResponse{
			Message: message,
		}, nil
	}

	token, ok := authorization.(string)
	if !ok {
		message := "Invalid authorization header format"
		return oapi.GetApiNoticeById401JSONResponse{
			Message: message,
		}, nil
	}

	_, err := h.auth0TokenService.ParseAuth0Token(token)
	if err != nil {
		message := "Authentication failed: " + err.Error()
		return oapi.GetApiNoticeById401JSONResponse{
			Message: message,
		}, err
	}

	notice, err := h.noticeUseCase.GetNoticeByID(ctx, request.Id)
	if err != nil {
		return notice, err
	}

	return notice, nil
}
