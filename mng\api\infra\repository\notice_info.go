// Package repository is implements DB connection.
package repository

import (
	"context"
	"io"

	"github.com/cockroachdb/errors"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/model"
	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/repository"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/aws/s3"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/db"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/dbmodels"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/errof"
)

type noticeInfo struct {
	dbClient db.Client
	s3Client s3.S3Client
}

func NewNoticeInfo(dbClient db.Client, s3Client s3.S3Client) repository.NoticeInfoRepository {
	return &noticeInfo{dbClient, s3Client}
}

func (r *noticeInfo) Create(ctx context.Context, model model.NoticeInfo, file io.Reader) error {
	dbmodels := dbmodels.MNGNoticeInfo{
		Date:    model.Date,
		Title:   model.Title,
		Comment: model.Comment,
		RegUser: model.RegUser,
		UpdUser: model.UpdUser,
	}

	if _, err := r.dbClient.DB.NewInsert().Model(&dbmodels).Returning("id").Exec(ctx); err != nil {

		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	// multipart.File
	err := r.s3Client.PutFile(ctx, dbmodels.ID, model.FileName, file)
	if err != nil {
		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	return nil
}

func (r *noticeInfo) Get(ctx context.Context, id int64) (*model.NoticeInfo, error) {
	var result dbmodels.MNGNoticeInfo

	if err := r.dbClient.DB.NewSelect().Model(&result).Where("id = ?", id).Scan(ctx); err != nil {

		// エラーが発生しているのでエラー情報を返す
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	ret := model.NewNoticeInfo(result)
	return &ret, nil
}

func (r *noticeInfo) GetList(ctx context.Context) ([]model.NoticeInfo, error) {
	var results []dbmodels.MNGNoticeInfo

	if err := r.dbClient.DB.NewSelect().Model(&results).Scan(ctx); err != nil {

		// エラーが発生しているのでエラー情報を返す
		return nil, errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	return model.NewNoticeInfoList(results), nil
}

func (r *noticeInfo) Update(ctx context.Context, info model.NoticeInfo) error {
	_, err := r.dbClient.DB.NewUpdate().Model(&info).Where("id = ?", info.ID).Exec(ctx)
	if err != nil {
		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	return nil
}

func (r *noticeInfo) Delete(ctx context.Context, id int64) error {
	result := dbmodels.MNGNoticeInfo{
		ID: id,
	}
	if err := r.dbClient.DB.NewSelect().Model(&result).Where("id = ?", id).Scan(ctx); err != nil {

		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}
	// S3からファイルを削除する
	if result.FileName != "" {
		err := r.s3Client.DeleteFile(ctx, result.ID, result.FileName)
		if err != nil {
			// エラーが発生しているのでエラー情報を返す
			return errors.Wrap(errof.ErrInternalGlobal, err.Error())
		}
	}
	_, err := r.dbClient.DB.NewDelete().Model(&result).WherePK().Exec(ctx)
	if err != nil {
		// エラーが発生しているのでエラー情報を返す
		return errors.Wrap(errof.ErrInternalGlobal, err.Error())
	}

	return nil
}
