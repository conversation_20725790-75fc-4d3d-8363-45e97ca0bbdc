// Package repository is DB connection.
package repository

import (
	"context"

	"github.com/jmdc-inc/kensuke-plus-server/mng/api/domain/model"
)

type MaintenanceInfoRepository interface {
	Create(context.Context, model.MaintenanceInfo) error
	Get(context.Context, int64) (*model.MaintenanceInfo, error)
	GetList(context.Context) ([]model.MaintenanceInfo, error)
	Update(context.Context, model.MaintenanceInfo) error
	Delete(context.Context, int64) error
}
