// Package s3 is used to authenticate our users.
package route53

import (
	"context"
	"fmt"
	"log"
	"os"

	awsconfig "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/route53"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/config"
)

// S3 is used to authenticate our users.
type Route53Client struct {
	client       *route53.Client
	helthcheckID string
}

// New インスタンスの生成
func New(config config.AWS) Route53Client {
	cfg, err := awsconfig.LoadDefaultConfig(context.Background(), awsconfig.WithRegion("ap-northeast-1"))
	if err != nil {
		panic(err)
	}

	client := route53.NewFromConfig(cfg, func(o *route53.Options) {
		o.Region = "ap-northeast-1"
	})

	return Route53Client{
		client:       client,
		helthcheckID: config.HelthcheckID,
	}
}

func (a *Route53Client) GetHelthCheck(ctx context.Context) (bool, error) {

	getHealthCheckInput := &route53.GetHealthCheckInput{
		HealthCheckId: aws.String(a.helthcheckID),
	}
	log.Printf("getHealthCheckInput: %v", getHealthCheckInput)

	result, err := a.client.GetHealthCheck(ctx, getHealthCheckInput)
	if err != nil {
		fmt.Fprintf(os.Stderr, "エラー: ヘルスチェック情報の取得に失敗しました: %v\n", err)
	}

	HealthCheckConfig := result.HealthCheck.HealthCheckConfig

	// Disabled フラグの値を取得 (nil の場合は false として扱います)
	// 通常、GetHealthCheck API はこれらのフラグについて nil を返すことは稀で、
	// 設定されていれば *true または *false を返します。
	// disabledStatus := HealthCheckConfig.Disabled

	// Inverted フラグの値を取得 (nil の場合は false として扱います)
	invertedStatus := HealthCheckConfig.Inverted
	// aws.ToBool は *bool を bool に変換し、nil の場合は false を返します。
	if invertedStatus == nil {
		return false, nil
	}

	return *invertedStatus, nil
}
func (a *Route53Client) UpdateHelthCheck(ctx context.Context, isHelthCheck bool) error {

	// ヘルスチェックを設定
	updateHealthCheckInput := &route53.UpdateHealthCheckInput{
		HealthCheckId: aws.String(a.helthcheckID), // ポインタ型なので aws.String() を使用
		Disabled:      aws.Bool(isHelthCheck),     // ポインタ型なので aws.Bool() を使用
	}
	_, err := a.client.UpdateHealthCheck(ctx, updateHealthCheckInput)
	if err != nil {
		fmt.Fprintf(os.Stderr, "エラー: ヘルスチェックの更新に失敗しました: %v\n", err)
	}

	return nil
}
