// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package injector

import (
	"github.com/jmdc-inc/kensuke-plus-server/pkg/authenticator"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/aws/s3"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/config"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/db"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/service"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/handler"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/infra/repository"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/usecase"
	"log/slog"
)

// Injectors from wire.go:

func InitializeAPIHandler(appLogger *slog.Logger, pgCfg config.Database, sfCfg config.SnowflakeDB, auth0Cfg config.Auth0, awsCfg config.AWS, appCfg config.AppConfig) *handler.API {
	postgresDB := db.NewPostgres(pgCfg, appLogger)
	snowflakeDB := db.NewSnowflakeDB(sfCfg)
	client := db.NewClient(postgresDB, snowflakeDB, appLogger)
	authenticatorAuthenticator := authenticator.New(auth0Cfg)
	s3Client := s3.New(awsCfg)
	tableauJWTService := service.NewTableauJWTService(appCfg)
	userRepository := repository.NewUser(client, authenticatorAuthenticator)
	tableauUseCase := usecase.NewTableauUseCase(tableauJWTService, userRepository)
	tableauDownloadService := service.NewTableauDownloadService(appCfg)
	tableauDownloadUseCase := usecase.NewTableauDownloadUseCase(tableauDownloadService)
	auth0TokenService := service.NewAuth0TokenService()
	userUseCase := usecase.NewUser(userRepository, auth0TokenService)
	emplrRepository := repository.NewEmplr(client)
	emplrUseCase := usecase.NewEmplr(emplrRepository)
	noticeRepository := repository.NewNotice(client)
	noticeUseCase := usecase.NewNotice(noticeRepository)
	maintenanceRepository := repository.NewMaintenance(client)
	maintenanceUseCase := usecase.NewMaintenance(maintenanceRepository)
	subscriberInfoRepository := repository.NewSubscriberInfoRepository(client)
	subscriberInfoUseCase := usecase.NewSubscriberInfoUseCase(subscriberInfoRepository)
	api := handler.NewAPI(appLogger, appCfg, client, authenticatorAuthenticator, s3Client, tableauUseCase, tableauDownloadUseCase, userUseCase, emplrUseCase, noticeUseCase, maintenanceUseCase, subscriberInfoUseCase, auth0TokenService)
	return api
}
