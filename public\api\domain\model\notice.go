package model

import "time"

type NoticeInfo struct {
	ID          int       `json:"id"`
	Title       string    `json:"title"`
	Comment     string    `json:"comment"`
	FileName    string    `json:"file_name"`
	ReleaseDate time.Time `json:"release_date"`
	RegUser     string    `json:"reg_user"`
	RegDate     time.Time `json:"reg_date"`
	UpdUser     string    `json:"upd_user"`
	UpdDate     time.Time `json:"upd_date"`
}

type NoticeListFilter struct {
	Page    int     `json:"page"`
	Limit   int     `json:"limit"`
	Keyword *string `json:"keyword"`
}

type NoticeListResponse struct {
	Data       []*NoticeInfo `json:"data"`
	Total      int           `json:"total"`
	Page       int           `json:"page"`
	Limit      int           `json:"limit"`
	TotalPages int           `json:"total_pages"`
}
