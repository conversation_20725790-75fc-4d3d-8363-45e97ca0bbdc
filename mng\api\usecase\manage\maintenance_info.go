package usecase

import (
	"context"

	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/manage"
)

type MaintenanceInfoUseCase interface {
	PutMaintenanceInfo(ctx context.Context, request oapi.PutMaintenanceInfoRequestObject) error
	GetMaintenanceInfo(ctx context.Context, id int64) (oapi.GetMaintenanceInfoResponseObject, error)
	GetListMaintenanceInfo(ctx context.Context) (oapi.GetMaintenanceInfoListResponseObject, error)
	PatchMaintenanceInfo(ctx context.Context, request oapi.PatchMaintenanceInfoRequestObject) error
	DeleteMaintenanceInfo(ctx context.Context, id int64) error
}
