#!/bin/bash

[ -f ./.env ] && { echo "Loading environment variables"; set -a; source ./.env; set +a; }

PORT=${SERVER_PORT:-8000}
AIR_CMD=$(go env GOPATH)/bin/air

# Check if air is installed, if not, install it
if [ ! -f "$AIR_CMD" ]; then
    echo "'air' not found, installing..."
    go install github.com/air-verse/air@latest
fi

kill_process() {
  PORT_PID=$(lsof -ti :$PORT)
  [ ! -z "$PORT_PID" ] && { echo "Killing process on port $PORT (PID: $PORT_PID)"; kill -9 $PORT_PID 2>/dev/null || true; }

  pkill -9 -f "$AIR_CMD" 2>/dev/null || true
  pkill -9 -f "./tmp/main" 2>/dev/null || true
}

cleanup() {
  echo -e "\nCleaning up..."
  kill_process
  echo "Done."
  exit 0
}

trap cleanup SIGINT SIGTERM EXIT

if lsof -ti :$PORT > /dev/null; then
  echo "Port $PORT is already in use. Stopping existing process..."
  kill_process
fi

echo "Starting PostgreSQL..."
(cd ../docker && docker compose up -d postgres)

echo "Checking PostgreSQL connection..."

PG_HOST=${DATABASE_HOST:-localhost}
PG_PORT=${DATABASE_PORT:-5432}

if nc -z -w 1 $PG_HOST $PG_PORT > /dev/null 2>&1; then
  echo "PostgreSQL is ready!"
else
  echo "Waiting for PostgreSQL to be ready..."

  MAX_TRIES=5
  COUNT=0

  while [ $COUNT -lt $MAX_TRIES ]; do
    if nc -z -w 1 $PG_HOST $PG_PORT > /dev/null 2>&1; then
      echo -e "\nPostgreSQL is ready after $COUNT seconds!"
      break
    fi
    echo -n "."
    COUNT=$((COUNT+1))
    sleep 1
  done

  if [ $COUNT -eq $MAX_TRIES ]; then
    echo -e "\nPostgreSQL not ready after $MAX_TRIES seconds. Continue starting the application..."
  fi
fi

echo "Starting application with air for hot-reload on port $PORT..."
$AIR_CMD &
echo "Air process started with PID: $!"

wait $!