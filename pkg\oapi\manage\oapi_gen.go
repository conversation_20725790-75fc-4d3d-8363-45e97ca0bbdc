// Package manage provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package manage

import (
	"context"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"net/http"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/oapi-codegen/runtime"
	strictnethttp "github.com/oapi-codegen/runtime/strictmiddleware/nethttp"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

// Emplr 健康保険組合
type Emplr struct {
	Cd *string `json:"cd,omitempty"`

	// CustomEmplrName 組合名
	CustomEmplrName string `json:"customEmplrName"`

	// GroupId 組合区分 1: 単一 2: 組合 3: 自治体
	GroupId int `json:"groupId"`

	// Id 組合ID
	Id int `json:"id"`
}

// EmplrMaster 組合マスタ
type EmplrMaster struct {
	// Cd 組合CD
	Cd string `json:"cd"`

	// Id 組合ID
	Id                   int    `json:"id"`
	Name                 string `json:"name"`
	SecondaryUseProperty int    `json:"secondaryUseProperty"`
}

// HierarchyRole 階層情報ロール
type HierarchyRole struct {
	// EmplrId 組合ID
	EmplrId int `json:"emplrId"`

	// HierarchyLevel 階層レベル
	HierarchyLevel int `json:"hierarchyLevel"`

	// HierarchyRoleId 階層情報ロールID
	HierarchyRoleId *int `json:"hierarchyRoleId,omitempty"`

	// JigyosyoCd 事業所コード
	JigyosyoCd *string `json:"jigyosyoCd,omitempty"`
}

// JigyosyoInfo 事業所情報
type JigyosyoInfo struct {
	// JigyosyoCd 事業所コード
	JigyosyoCd string `json:"jigyosyoCd"`

	// JigyosyoName 事業所名
	JigyosyoName string `json:"jigyosyoName"`
}

// MaintenanceInfo defines model for MaintenanceInfo.
type MaintenanceInfo struct {
	// Comment 内容
	Comment string `json:"comment"`

	// Id お知らせID
	Id *int64 `json:"id,omitempty"`

	// ReleaseDate 日付
	ReleaseDate string `json:"releaseDate"`

	// Title タイトル
	Title string `json:"title"`
}

// NoticeInfo defines model for NoticeInfo.
type NoticeInfo struct {
	Category *int `json:"category,omitempty"`

	// Comment 内容
	Comment *string `json:"comment,omitempty"`

	// FileName ファイル名
	FileName *string `json:"fileName,omitempty"`

	// Id お知らせID
	Id int64 `json:"id"`

	// ReleaseDate 日付
	ReleaseDate *time.Time `json:"releaseDate,omitempty"`

	// Title タイトル
	Title *string `json:"title,omitempty"`
}

// RegistCsvResult defines model for RegistCsvResult.
type RegistCsvResult struct {
	// Auth0UserId Auth0のユーザーID
	Auth0UserId *string `json:"auth0UserId,omitempty"`

	// Email メールアドレス
	Email openapi_types.Email `json:"email"`

	// EmplrId 企業ID
	EmplrId int `json:"emplrId"`

	// EmplrName 組合名
	EmplrName string `json:"emplrName"`

	// HierarchyRoleId 階層情報ロールID
	HierarchyRoleId int `json:"hierarchyRoleId"`

	// IsAdmin メールアドレス
	IsAdmin bool `json:"isAdmin"`

	// IsRegist 登録済みかどうか
	IsRegist bool `json:"isRegist"`

	// Memo メモ
	Memo string `json:"memo"`

	// Name 名前
	Name string `json:"name"`
}

// User defines model for User.
type User struct {
	// Comment 備考
	Comment *string `json:"comment,omitempty"`

	// Email メールアドレス
	Email openapi_types.Email `json:"email"`

	// HierarchyRoleId 階層情報ロールID
	HierarchyRoleId int `json:"hierarchyRoleId"`

	// Id ユーザーID
	Id *int `json:"id,omitempty"`

	// IsAdmin 組合ID
	IsAdmin bool `json:"isAdmin"`

	// Name ユーザー名
	Name string `json:"name"`
}

// UserDetail defines model for UserDetail.
type UserDetail struct {
	// Auth0UserId Auth0のユーザーID
	Auth0UserId string `json:"auth0UserId"`

	// Comment 備考
	Comment string `json:"comment"`

	// Email メールアドレス
	Email openapi_types.Email `json:"email"`

	// EmplrCd 組合ID
	EmplrCd string `json:"emplrCd"`

	// EmplrName 組合ID
	EmplrName string `json:"emplrName"`

	// HierarchyLevel 階層レベル
	HierarchyLevel int `json:"hierarchyLevel"`

	// Id ユーザーID
	Id int `json:"id"`

	// IsAdmin 権限
	IsAdmin bool `json:"isAdmin"`

	// IsBlocked ブロック状態
	IsBlocked bool `json:"isBlocked"`

	// IsMfa MFA状態
	IsMfa bool `json:"isMfa"`

	// JigyosyoCd 事業所コード
	JigyosyoCd string `json:"jigyosyoCd"`

	// LastLogin 最終ログイン日時
	LastLogin time.Time `json:"lastLogin"`

	// Name ユーザー名
	Name string `json:"name"`
}

// UserSimple defines model for UserSimple.
type UserSimple struct {
	// Email メールアドレス
	Email openapi_types.Email `json:"email"`

	// EmplrName 階層情報ロールID
	EmplrName string `json:"emplrName"`

	// Id ユーザーID
	Id int `json:"id"`

	// IsAdmin 組合ID
	IsAdmin bool `json:"isAdmin"`

	// Name ユーザー名
	Name string `json:"name"`
}

// PostConfirmRegistUserListMultipartBody defines parameters for PostConfirmRegistUserList.
type PostConfirmRegistUserListMultipartBody struct {
	File openapi_types.File `json:"file"`
}

// PostConfirmSendMailUserListMultipartBody defines parameters for PostConfirmSendMailUserList.
type PostConfirmSendMailUserListMultipartBody struct {
	File openapi_types.File `json:"file"`
}

// DeleteEmplrParams defines parameters for DeleteEmplr.
type DeleteEmplrParams struct {
	Id int `form:"id" json:"id"`
}

// GetEmplrParams defines parameters for GetEmplr.
type GetEmplrParams struct {
	Id int `form:"id" json:"id"`
}

// DeleteHierarchyRoleParams defines parameters for DeleteHierarchyRole.
type DeleteHierarchyRoleParams struct {
	Id int `form:"id" json:"id"`
}

// GetHierarchyRoleParams defines parameters for GetHierarchyRole.
type GetHierarchyRoleParams struct {
	Id int `form:"id" json:"id"`
}

// GetHierarchyRoleListParams defines parameters for GetHierarchyRoleList.
type GetHierarchyRoleListParams struct {
	EmplrId int `form:"emplrId" json:"emplrId"`
}

// GetJigyosyoCdListParams defines parameters for GetJigyosyoCdList.
type GetJigyosyoCdListParams struct {
	Id int `form:"id" json:"id"`
}

// DeleteMaintenanceInfoParams defines parameters for DeleteMaintenanceInfo.
type DeleteMaintenanceInfoParams struct {
	Id int64 `form:"id" json:"id"`
}

// GetMaintenanceInfoParams defines parameters for GetMaintenanceInfo.
type GetMaintenanceInfoParams struct {
	Id int64 `form:"id" json:"id"`
}

// DeleteNoticeInfoParams defines parameters for DeleteNoticeInfo.
type DeleteNoticeInfoParams struct {
	Id int64 `form:"id" json:"id"`
}

// GetNoticeInfoParams defines parameters for GetNoticeInfo.
type GetNoticeInfoParams struct {
	Id int64 `form:"id" json:"id"`
}

// PostNoticeInfoMultipartBody defines parameters for PostNoticeInfo.
type PostNoticeInfoMultipartBody struct {
	// Comment コメント
	Comment string              `json:"comment"`
	File    *openapi_types.File `json:"file,omitempty"`

	// Id お知らせID（新規作成の場合のみ）
	Id *int `json:"id,omitempty"`

	// ReleaseDate 日付
	ReleaseDate openapi_types.Date `json:"releaseDate"`

	// Title タイトル
	Title string `json:"title"`
}

// PostRegistUserListMultipartBody defines parameters for PostRegistUserList.
type PostRegistUserListMultipartBody struct {
	File openapi_types.File `json:"file"`
}

// PostSendMailUserListMultipartBody defines parameters for PostSendMailUserList.
type PostSendMailUserListMultipartBody struct {
	File openapi_types.File `json:"file"`
}

// PatchServiceStatusParams defines parameters for PatchServiceStatus.
type PatchServiceStatusParams struct {
	Status int64 `form:"status" json:"status"`
}

// DeleteUserParams defines parameters for DeleteUser.
type DeleteUserParams struct {
	Id int `form:"id" json:"id"`
}

// GetUserParams defines parameters for GetUser.
type GetUserParams struct {
	Id int `form:"id" json:"id"`
}

// PostConfirmRegistUserListMultipartRequestBody defines body for PostConfirmRegistUserList for multipart/form-data ContentType.
type PostConfirmRegistUserListMultipartRequestBody PostConfirmRegistUserListMultipartBody

// PostConfirmSendMailUserListMultipartRequestBody defines body for PostConfirmSendMailUserList for multipart/form-data ContentType.
type PostConfirmSendMailUserListMultipartRequestBody PostConfirmSendMailUserListMultipartBody

// PatchEmplrJSONRequestBody defines body for PatchEmplr for application/json ContentType.
type PatchEmplrJSONRequestBody = Emplr

// PutEmplrJSONRequestBody defines body for PutEmplr for application/json ContentType.
type PutEmplrJSONRequestBody = Emplr

// PutHierarchyRoleJSONRequestBody defines body for PutHierarchyRole for application/json ContentType.
type PutHierarchyRoleJSONRequestBody = HierarchyRole

// PatchMaintenanceInfoJSONRequestBody defines body for PatchMaintenanceInfo for application/json ContentType.
type PatchMaintenanceInfoJSONRequestBody = MaintenanceInfo

// PutMaintenanceInfoJSONRequestBody defines body for PutMaintenanceInfo for application/json ContentType.
type PutMaintenanceInfoJSONRequestBody = MaintenanceInfo

// PostNoticeInfoMultipartRequestBody defines body for PostNoticeInfo for multipart/form-data ContentType.
type PostNoticeInfoMultipartRequestBody PostNoticeInfoMultipartBody

// PostRegistUserListMultipartRequestBody defines body for PostRegistUserList for multipart/form-data ContentType.
type PostRegistUserListMultipartRequestBody PostRegistUserListMultipartBody

// PostSendMailUserListMultipartRequestBody defines body for PostSendMailUserList for multipart/form-data ContentType.
type PostSendMailUserListMultipartRequestBody PostSendMailUserListMultipartBody

// PatchUserJSONRequestBody defines body for PatchUser for application/json ContentType.
type PatchUserJSONRequestBody = User

// PutUserJSONRequestBody defines body for PutUser for application/json ContentType.
type PutUserJSONRequestBody = User

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// ユーザー登録確認CSV作成
	// (POST /confirm-regist-user-list)
	PostConfirmRegistUserList(w http.ResponseWriter, r *http.Request)
	// メール送信確認
	// (POST /confirm-send-mail-user-list)
	PostConfirmSendMailUserList(w http.ResponseWriter, r *http.Request)
	// 組合削除
	// (DELETE /emplr)
	DeleteEmplr(w http.ResponseWriter, r *http.Request, params DeleteEmplrParams)
	// 組合取得
	// (GET /emplr)
	GetEmplr(w http.ResponseWriter, r *http.Request, params GetEmplrParams)
	// 組合更新
	// (PATCH /emplr)
	PatchEmplr(w http.ResponseWriter, r *http.Request)
	// 組合作成
	// (PUT /emplr)
	PutEmplr(w http.ResponseWriter, r *http.Request)
	// 組合一覧取得
	// (GET /emplr-list)
	GetEmplrList(w http.ResponseWriter, r *http.Request)
	// 組合階層削除
	// (DELETE /hierarchy-role)
	DeleteHierarchyRole(w http.ResponseWriter, r *http.Request, params DeleteHierarchyRoleParams)
	// 組合階層取得
	// (GET /hierarchy-role)
	GetHierarchyRole(w http.ResponseWriter, r *http.Request, params GetHierarchyRoleParams)
	// 組合階層追加
	// (PUT /hierarchy-role)
	PutHierarchyRole(w http.ResponseWriter, r *http.Request)
	// 階層情報ロールの一覧取得
	// (GET /hierarchy-role-all-list)
	GetHierarchyRoleAllList(w http.ResponseWriter, r *http.Request)
	// 階層情報ロールの一覧取得
	// (GET /hierarchy-role-list)
	GetHierarchyRoleList(w http.ResponseWriter, r *http.Request, params GetHierarchyRoleListParams)
	// 事業所CDリストの取得
	// (GET /jigyosyo-cd-list)
	GetJigyosyoCdList(w http.ResponseWriter, r *http.Request, params GetJigyosyoCdListParams)
	// メンテナンス情報削除
	// (DELETE /maintenance-info)
	DeleteMaintenanceInfo(w http.ResponseWriter, r *http.Request, params DeleteMaintenanceInfoParams)
	// メンテナンス情報取得
	// (GET /maintenance-info)
	GetMaintenanceInfo(w http.ResponseWriter, r *http.Request, params GetMaintenanceInfoParams)
	// メンテナンス情報更新
	// (PATCH /maintenance-info)
	PatchMaintenanceInfo(w http.ResponseWriter, r *http.Request)
	// メンテナンス情報作成
	// (PUT /maintenance-info)
	PutMaintenanceInfo(w http.ResponseWriter, r *http.Request)
	// メンテナンス情報一覧取得
	// (GET /maintenance-info-list)
	GetMaintenanceInfoList(w http.ResponseWriter, r *http.Request)
	// 組合マスターリストの取得
	// (GET /master-emplr-list)
	GetMasterEmplrList(w http.ResponseWriter, r *http.Request)
	// お知らせ削除
	// (DELETE /notice-info)
	DeleteNoticeInfo(w http.ResponseWriter, r *http.Request, params DeleteNoticeInfoParams)
	// お知らせ取得
	// (GET /notice-info)
	GetNoticeInfo(w http.ResponseWriter, r *http.Request, params GetNoticeInfoParams)
	// お知らせ作成
	// (POST /notice-info)
	PostNoticeInfo(w http.ResponseWriter, r *http.Request)
	// お知らせ一覧取得
	// (GET /notice-info-list)
	GetNoticeInfoList(w http.ResponseWriter, r *http.Request)
	// ユーザー登録
	// (POST /regist-user-list)
	PostRegistUserList(w http.ResponseWriter, r *http.Request)
	// メール送信
	// (POST /send-mail-user-list)
	PostSendMailUserList(w http.ResponseWriter, r *http.Request)
	// サーバーステータス取得
	// (GET /service-status)
	GetServiceStatus(w http.ResponseWriter, r *http.Request)
	// サーバーステータス更新
	// (PATCH /service-status)
	PatchServiceStatus(w http.ResponseWriter, r *http.Request, params PatchServiceStatusParams)
	// ユーザー削除
	// (DELETE /user)
	DeleteUser(w http.ResponseWriter, r *http.Request, params DeleteUserParams)
	// ユーザー取得
	// (GET /user)
	GetUser(w http.ResponseWriter, r *http.Request, params GetUserParams)
	// ユーザー更新
	// (PATCH /user)
	PatchUser(w http.ResponseWriter, r *http.Request)
	// ユーザー作成
	// (PUT /user)
	PutUser(w http.ResponseWriter, r *http.Request)
	// ユーザー一覧取得
	// (GET /user-list)
	GetUserList(w http.ResponseWriter, r *http.Request)
}

// Unimplemented server implementation that returns http.StatusNotImplemented for each endpoint.

type Unimplemented struct{}

// ユーザー登録確認CSV作成
// (POST /confirm-regist-user-list)
func (_ Unimplemented) PostConfirmRegistUserList(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// メール送信確認
// (POST /confirm-send-mail-user-list)
func (_ Unimplemented) PostConfirmSendMailUserList(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// 組合削除
// (DELETE /emplr)
func (_ Unimplemented) DeleteEmplr(w http.ResponseWriter, r *http.Request, params DeleteEmplrParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// 組合取得
// (GET /emplr)
func (_ Unimplemented) GetEmplr(w http.ResponseWriter, r *http.Request, params GetEmplrParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// 組合更新
// (PATCH /emplr)
func (_ Unimplemented) PatchEmplr(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// 組合作成
// (PUT /emplr)
func (_ Unimplemented) PutEmplr(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// 組合一覧取得
// (GET /emplr-list)
func (_ Unimplemented) GetEmplrList(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// 組合階層削除
// (DELETE /hierarchy-role)
func (_ Unimplemented) DeleteHierarchyRole(w http.ResponseWriter, r *http.Request, params DeleteHierarchyRoleParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// 組合階層取得
// (GET /hierarchy-role)
func (_ Unimplemented) GetHierarchyRole(w http.ResponseWriter, r *http.Request, params GetHierarchyRoleParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// 組合階層追加
// (PUT /hierarchy-role)
func (_ Unimplemented) PutHierarchyRole(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// 階層情報ロールの一覧取得
// (GET /hierarchy-role-all-list)
func (_ Unimplemented) GetHierarchyRoleAllList(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// 階層情報ロールの一覧取得
// (GET /hierarchy-role-list)
func (_ Unimplemented) GetHierarchyRoleList(w http.ResponseWriter, r *http.Request, params GetHierarchyRoleListParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// 事業所CDリストの取得
// (GET /jigyosyo-cd-list)
func (_ Unimplemented) GetJigyosyoCdList(w http.ResponseWriter, r *http.Request, params GetJigyosyoCdListParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// メンテナンス情報削除
// (DELETE /maintenance-info)
func (_ Unimplemented) DeleteMaintenanceInfo(w http.ResponseWriter, r *http.Request, params DeleteMaintenanceInfoParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// メンテナンス情報取得
// (GET /maintenance-info)
func (_ Unimplemented) GetMaintenanceInfo(w http.ResponseWriter, r *http.Request, params GetMaintenanceInfoParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// メンテナンス情報更新
// (PATCH /maintenance-info)
func (_ Unimplemented) PatchMaintenanceInfo(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// メンテナンス情報作成
// (PUT /maintenance-info)
func (_ Unimplemented) PutMaintenanceInfo(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// メンテナンス情報一覧取得
// (GET /maintenance-info-list)
func (_ Unimplemented) GetMaintenanceInfoList(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// 組合マスターリストの取得
// (GET /master-emplr-list)
func (_ Unimplemented) GetMasterEmplrList(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// お知らせ削除
// (DELETE /notice-info)
func (_ Unimplemented) DeleteNoticeInfo(w http.ResponseWriter, r *http.Request, params DeleteNoticeInfoParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// お知らせ取得
// (GET /notice-info)
func (_ Unimplemented) GetNoticeInfo(w http.ResponseWriter, r *http.Request, params GetNoticeInfoParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// お知らせ作成
// (POST /notice-info)
func (_ Unimplemented) PostNoticeInfo(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// お知らせ一覧取得
// (GET /notice-info-list)
func (_ Unimplemented) GetNoticeInfoList(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ユーザー登録
// (POST /regist-user-list)
func (_ Unimplemented) PostRegistUserList(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// メール送信
// (POST /send-mail-user-list)
func (_ Unimplemented) PostSendMailUserList(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// サーバーステータス取得
// (GET /service-status)
func (_ Unimplemented) GetServiceStatus(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// サーバーステータス更新
// (PATCH /service-status)
func (_ Unimplemented) PatchServiceStatus(w http.ResponseWriter, r *http.Request, params PatchServiceStatusParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ユーザー削除
// (DELETE /user)
func (_ Unimplemented) DeleteUser(w http.ResponseWriter, r *http.Request, params DeleteUserParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ユーザー取得
// (GET /user)
func (_ Unimplemented) GetUser(w http.ResponseWriter, r *http.Request, params GetUserParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ユーザー更新
// (PATCH /user)
func (_ Unimplemented) PatchUser(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ユーザー作成
// (PUT /user)
func (_ Unimplemented) PutUser(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ユーザー一覧取得
// (GET /user-list)
func (_ Unimplemented) GetUserList(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandlerFunc   func(w http.ResponseWriter, r *http.Request, err error)
}

type MiddlewareFunc func(http.Handler) http.Handler

// PostConfirmRegistUserList operation middleware
func (siw *ServerInterfaceWrapper) PostConfirmRegistUserList(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PostConfirmRegistUserList(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PostConfirmSendMailUserList operation middleware
func (siw *ServerInterfaceWrapper) PostConfirmSendMailUserList(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PostConfirmSendMailUserList(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DeleteEmplr operation middleware
func (siw *ServerInterfaceWrapper) DeleteEmplr(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params DeleteEmplrParams

	// ------------- Required query parameter "id" -------------

	if paramValue := r.URL.Query().Get("id"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "id"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "id", r.URL.Query(), &params.Id)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DeleteEmplr(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetEmplr operation middleware
func (siw *ServerInterfaceWrapper) GetEmplr(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params GetEmplrParams

	// ------------- Required query parameter "id" -------------

	if paramValue := r.URL.Query().Get("id"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "id"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "id", r.URL.Query(), &params.Id)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetEmplr(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PatchEmplr operation middleware
func (siw *ServerInterfaceWrapper) PatchEmplr(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PatchEmplr(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PutEmplr operation middleware
func (siw *ServerInterfaceWrapper) PutEmplr(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PutEmplr(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetEmplrList operation middleware
func (siw *ServerInterfaceWrapper) GetEmplrList(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetEmplrList(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DeleteHierarchyRole operation middleware
func (siw *ServerInterfaceWrapper) DeleteHierarchyRole(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params DeleteHierarchyRoleParams

	// ------------- Required query parameter "id" -------------

	if paramValue := r.URL.Query().Get("id"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "id"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "id", r.URL.Query(), &params.Id)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DeleteHierarchyRole(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetHierarchyRole operation middleware
func (siw *ServerInterfaceWrapper) GetHierarchyRole(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params GetHierarchyRoleParams

	// ------------- Required query parameter "id" -------------

	if paramValue := r.URL.Query().Get("id"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "id"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "id", r.URL.Query(), &params.Id)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetHierarchyRole(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PutHierarchyRole operation middleware
func (siw *ServerInterfaceWrapper) PutHierarchyRole(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PutHierarchyRole(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetHierarchyRoleAllList operation middleware
func (siw *ServerInterfaceWrapper) GetHierarchyRoleAllList(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetHierarchyRoleAllList(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetHierarchyRoleList operation middleware
func (siw *ServerInterfaceWrapper) GetHierarchyRoleList(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params GetHierarchyRoleListParams

	// ------------- Required query parameter "emplrId" -------------

	if paramValue := r.URL.Query().Get("emplrId"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "emplrId"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "emplrId", r.URL.Query(), &params.EmplrId)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "emplrId", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetHierarchyRoleList(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetJigyosyoCdList operation middleware
func (siw *ServerInterfaceWrapper) GetJigyosyoCdList(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params GetJigyosyoCdListParams

	// ------------- Required query parameter "id" -------------

	if paramValue := r.URL.Query().Get("id"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "id"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "id", r.URL.Query(), &params.Id)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetJigyosyoCdList(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DeleteMaintenanceInfo operation middleware
func (siw *ServerInterfaceWrapper) DeleteMaintenanceInfo(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params DeleteMaintenanceInfoParams

	// ------------- Required query parameter "id" -------------

	if paramValue := r.URL.Query().Get("id"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "id"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "id", r.URL.Query(), &params.Id)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DeleteMaintenanceInfo(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetMaintenanceInfo operation middleware
func (siw *ServerInterfaceWrapper) GetMaintenanceInfo(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params GetMaintenanceInfoParams

	// ------------- Required query parameter "id" -------------

	if paramValue := r.URL.Query().Get("id"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "id"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "id", r.URL.Query(), &params.Id)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetMaintenanceInfo(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PatchMaintenanceInfo operation middleware
func (siw *ServerInterfaceWrapper) PatchMaintenanceInfo(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PatchMaintenanceInfo(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PutMaintenanceInfo operation middleware
func (siw *ServerInterfaceWrapper) PutMaintenanceInfo(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PutMaintenanceInfo(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetMaintenanceInfoList operation middleware
func (siw *ServerInterfaceWrapper) GetMaintenanceInfoList(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetMaintenanceInfoList(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetMasterEmplrList operation middleware
func (siw *ServerInterfaceWrapper) GetMasterEmplrList(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetMasterEmplrList(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DeleteNoticeInfo operation middleware
func (siw *ServerInterfaceWrapper) DeleteNoticeInfo(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params DeleteNoticeInfoParams

	// ------------- Required query parameter "id" -------------

	if paramValue := r.URL.Query().Get("id"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "id"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "id", r.URL.Query(), &params.Id)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DeleteNoticeInfo(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetNoticeInfo operation middleware
func (siw *ServerInterfaceWrapper) GetNoticeInfo(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params GetNoticeInfoParams

	// ------------- Required query parameter "id" -------------

	if paramValue := r.URL.Query().Get("id"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "id"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "id", r.URL.Query(), &params.Id)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetNoticeInfo(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PostNoticeInfo operation middleware
func (siw *ServerInterfaceWrapper) PostNoticeInfo(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PostNoticeInfo(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetNoticeInfoList operation middleware
func (siw *ServerInterfaceWrapper) GetNoticeInfoList(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetNoticeInfoList(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PostRegistUserList operation middleware
func (siw *ServerInterfaceWrapper) PostRegistUserList(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PostRegistUserList(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PostSendMailUserList operation middleware
func (siw *ServerInterfaceWrapper) PostSendMailUserList(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PostSendMailUserList(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetServiceStatus operation middleware
func (siw *ServerInterfaceWrapper) GetServiceStatus(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetServiceStatus(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PatchServiceStatus operation middleware
func (siw *ServerInterfaceWrapper) PatchServiceStatus(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params PatchServiceStatusParams

	// ------------- Required query parameter "status" -------------

	if paramValue := r.URL.Query().Get("status"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "status"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "status", r.URL.Query(), &params.Status)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "status", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PatchServiceStatus(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DeleteUser operation middleware
func (siw *ServerInterfaceWrapper) DeleteUser(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params DeleteUserParams

	// ------------- Required query parameter "id" -------------

	if paramValue := r.URL.Query().Get("id"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "id"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "id", r.URL.Query(), &params.Id)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DeleteUser(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetUser operation middleware
func (siw *ServerInterfaceWrapper) GetUser(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params GetUserParams

	// ------------- Required query parameter "id" -------------

	if paramValue := r.URL.Query().Get("id"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "id"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "id", r.URL.Query(), &params.Id)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetUser(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PatchUser operation middleware
func (siw *ServerInterfaceWrapper) PatchUser(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PatchUser(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PutUser operation middleware
func (siw *ServerInterfaceWrapper) PutUser(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PutUser(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetUserList operation middleware
func (siw *ServerInterfaceWrapper) GetUserList(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetUserList(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

type UnescapedCookieParamError struct {
	ParamName string
	Err       error
}

func (e *UnescapedCookieParamError) Error() string {
	return fmt.Sprintf("error unescaping cookie parameter '%s'", e.ParamName)
}

func (e *UnescapedCookieParamError) Unwrap() error {
	return e.Err
}

type UnmarshalingParamError struct {
	ParamName string
	Err       error
}

func (e *UnmarshalingParamError) Error() string {
	return fmt.Sprintf("Error unmarshaling parameter %s as JSON: %s", e.ParamName, e.Err.Error())
}

func (e *UnmarshalingParamError) Unwrap() error {
	return e.Err
}

type RequiredParamError struct {
	ParamName string
}

func (e *RequiredParamError) Error() string {
	return fmt.Sprintf("Query argument %s is required, but not found", e.ParamName)
}

type RequiredHeaderError struct {
	ParamName string
	Err       error
}

func (e *RequiredHeaderError) Error() string {
	return fmt.Sprintf("Header parameter %s is required, but not found", e.ParamName)
}

func (e *RequiredHeaderError) Unwrap() error {
	return e.Err
}

type InvalidParamFormatError struct {
	ParamName string
	Err       error
}

func (e *InvalidParamFormatError) Error() string {
	return fmt.Sprintf("Invalid format for parameter %s: %s", e.ParamName, e.Err.Error())
}

func (e *InvalidParamFormatError) Unwrap() error {
	return e.Err
}

type TooManyValuesForParamError struct {
	ParamName string
	Count     int
}

func (e *TooManyValuesForParamError) Error() string {
	return fmt.Sprintf("Expected one value for %s, got %d", e.ParamName, e.Count)
}

// Handler creates http.Handler with routing matching OpenAPI spec.
func Handler(si ServerInterface) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{})
}

type ChiServerOptions struct {
	BaseURL          string
	BaseRouter       chi.Router
	Middlewares      []MiddlewareFunc
	ErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

// HandlerFromMux creates http.Handler with routing matching OpenAPI spec based on the provided mux.
func HandlerFromMux(si ServerInterface, r chi.Router) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseRouter: r,
	})
}

func HandlerFromMuxWithBaseURL(si ServerInterface, r chi.Router, baseURL string) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseURL:    baseURL,
		BaseRouter: r,
	})
}

// HandlerWithOptions creates http.Handler with additional options
func HandlerWithOptions(si ServerInterface, options ChiServerOptions) http.Handler {
	r := options.BaseRouter

	if r == nil {
		r = chi.NewRouter()
	}
	if options.ErrorHandlerFunc == nil {
		options.ErrorHandlerFunc = func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		}
	}
	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandlerFunc:   options.ErrorHandlerFunc,
	}

	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/confirm-regist-user-list", wrapper.PostConfirmRegistUserList)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/confirm-send-mail-user-list", wrapper.PostConfirmSendMailUserList)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/emplr", wrapper.DeleteEmplr)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/emplr", wrapper.GetEmplr)
	})
	r.Group(func(r chi.Router) {
		r.Patch(options.BaseURL+"/emplr", wrapper.PatchEmplr)
	})
	r.Group(func(r chi.Router) {
		r.Put(options.BaseURL+"/emplr", wrapper.PutEmplr)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/emplr-list", wrapper.GetEmplrList)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/hierarchy-role", wrapper.DeleteHierarchyRole)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/hierarchy-role", wrapper.GetHierarchyRole)
	})
	r.Group(func(r chi.Router) {
		r.Put(options.BaseURL+"/hierarchy-role", wrapper.PutHierarchyRole)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/hierarchy-role-all-list", wrapper.GetHierarchyRoleAllList)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/hierarchy-role-list", wrapper.GetHierarchyRoleList)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/jigyosyo-cd-list", wrapper.GetJigyosyoCdList)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/maintenance-info", wrapper.DeleteMaintenanceInfo)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/maintenance-info", wrapper.GetMaintenanceInfo)
	})
	r.Group(func(r chi.Router) {
		r.Patch(options.BaseURL+"/maintenance-info", wrapper.PatchMaintenanceInfo)
	})
	r.Group(func(r chi.Router) {
		r.Put(options.BaseURL+"/maintenance-info", wrapper.PutMaintenanceInfo)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/maintenance-info-list", wrapper.GetMaintenanceInfoList)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/master-emplr-list", wrapper.GetMasterEmplrList)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/notice-info", wrapper.DeleteNoticeInfo)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/notice-info", wrapper.GetNoticeInfo)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/notice-info", wrapper.PostNoticeInfo)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/notice-info-list", wrapper.GetNoticeInfoList)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/regist-user-list", wrapper.PostRegistUserList)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/send-mail-user-list", wrapper.PostSendMailUserList)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/service-status", wrapper.GetServiceStatus)
	})
	r.Group(func(r chi.Router) {
		r.Patch(options.BaseURL+"/service-status", wrapper.PatchServiceStatus)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/user", wrapper.DeleteUser)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/user", wrapper.GetUser)
	})
	r.Group(func(r chi.Router) {
		r.Patch(options.BaseURL+"/user", wrapper.PatchUser)
	})
	r.Group(func(r chi.Router) {
		r.Put(options.BaseURL+"/user", wrapper.PutUser)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/user-list", wrapper.GetUserList)
	})

	return r
}

type PostConfirmRegistUserListRequestObject struct {
	Body *multipart.Reader
}

type PostConfirmRegistUserListResponseObject interface {
	VisitPostConfirmRegistUserListResponse(w http.ResponseWriter) error
}

type PostConfirmRegistUserList200JSONResponse struct {
	Results []RegistCsvResult `json:"results"`
}

func (response PostConfirmRegistUserList200JSONResponse) VisitPostConfirmRegistUserListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type PostConfirmRegistUserList400JSONResponse struct {
	Errors []string `json:"errors"`
}

func (response PostConfirmRegistUserList400JSONResponse) VisitPostConfirmRegistUserListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(400)

	return json.NewEncoder(w).Encode(response)
}

type PostConfirmRegistUserList500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response PostConfirmRegistUserList500JSONResponse) VisitPostConfirmRegistUserListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type PostConfirmSendMailUserListRequestObject struct {
	Body *multipart.Reader
}

type PostConfirmSendMailUserListResponseObject interface {
	VisitPostConfirmSendMailUserListResponse(w http.ResponseWriter) error
}

type PostConfirmSendMailUserList200JSONResponse struct {
	Results []RegistCsvResult `json:"results"`
}

func (response PostConfirmSendMailUserList200JSONResponse) VisitPostConfirmSendMailUserListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type PostConfirmSendMailUserList400JSONResponse struct {
	Errors []string `json:"errors"`
}

func (response PostConfirmSendMailUserList400JSONResponse) VisitPostConfirmSendMailUserListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(400)

	return json.NewEncoder(w).Encode(response)
}

type PostConfirmSendMailUserList500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response PostConfirmSendMailUserList500JSONResponse) VisitPostConfirmSendMailUserListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type DeleteEmplrRequestObject struct {
	Params DeleteEmplrParams
}

type DeleteEmplrResponseObject interface {
	VisitDeleteEmplrResponse(w http.ResponseWriter) error
}

type DeleteEmplr204Response struct {
}

func (response DeleteEmplr204Response) VisitDeleteEmplrResponse(w http.ResponseWriter) error {
	w.WriteHeader(204)
	return nil
}

type DeleteEmplr400JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response DeleteEmplr400JSONResponse) VisitDeleteEmplrResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(400)

	return json.NewEncoder(w).Encode(response)
}

type DeleteEmplr500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response DeleteEmplr500JSONResponse) VisitDeleteEmplrResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetEmplrRequestObject struct {
	Params GetEmplrParams
}

type GetEmplrResponseObject interface {
	VisitGetEmplrResponse(w http.ResponseWriter) error
}

type GetEmplr200JSONResponse Emplr

func (response GetEmplr200JSONResponse) VisitGetEmplrResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetEmplr204Response struct {
}

func (response GetEmplr204Response) VisitGetEmplrResponse(w http.ResponseWriter) error {
	w.WriteHeader(204)
	return nil
}

type GetEmplr400JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response GetEmplr400JSONResponse) VisitGetEmplrResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(400)

	return json.NewEncoder(w).Encode(response)
}

type GetEmplr500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response GetEmplr500JSONResponse) VisitGetEmplrResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type PatchEmplrRequestObject struct {
	Body *PatchEmplrJSONRequestBody
}

type PatchEmplrResponseObject interface {
	VisitPatchEmplrResponse(w http.ResponseWriter) error
}

type PatchEmplr204Response struct {
}

func (response PatchEmplr204Response) VisitPatchEmplrResponse(w http.ResponseWriter) error {
	w.WriteHeader(204)
	return nil
}

type PatchEmplr400JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response PatchEmplr400JSONResponse) VisitPatchEmplrResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(400)

	return json.NewEncoder(w).Encode(response)
}

type PatchEmplr500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response PatchEmplr500JSONResponse) VisitPatchEmplrResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type PutEmplrRequestObject struct {
	Body *PutEmplrJSONRequestBody
}

type PutEmplrResponseObject interface {
	VisitPutEmplrResponse(w http.ResponseWriter) error
}

type PutEmplr201Response struct {
}

func (response PutEmplr201Response) VisitPutEmplrResponse(w http.ResponseWriter) error {
	w.WriteHeader(201)
	return nil
}

type PutEmplr400JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response PutEmplr400JSONResponse) VisitPutEmplrResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(400)

	return json.NewEncoder(w).Encode(response)
}

type PutEmplr409JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response PutEmplr409JSONResponse) VisitPutEmplrResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(409)

	return json.NewEncoder(w).Encode(response)
}

type PutEmplr500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response PutEmplr500JSONResponse) VisitPutEmplrResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetEmplrListRequestObject struct {
}

type GetEmplrListResponseObject interface {
	VisitGetEmplrListResponse(w http.ResponseWriter) error
}

type GetEmplrList200JSONResponse []Emplr

func (response GetEmplrList200JSONResponse) VisitGetEmplrListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetEmplrList500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response GetEmplrList500JSONResponse) VisitGetEmplrListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type DeleteHierarchyRoleRequestObject struct {
	Params DeleteHierarchyRoleParams
}

type DeleteHierarchyRoleResponseObject interface {
	VisitDeleteHierarchyRoleResponse(w http.ResponseWriter) error
}

type DeleteHierarchyRole204Response struct {
}

func (response DeleteHierarchyRole204Response) VisitDeleteHierarchyRoleResponse(w http.ResponseWriter) error {
	w.WriteHeader(204)
	return nil
}

type DeleteHierarchyRole500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response DeleteHierarchyRole500JSONResponse) VisitDeleteHierarchyRoleResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetHierarchyRoleRequestObject struct {
	Params GetHierarchyRoleParams
}

type GetHierarchyRoleResponseObject interface {
	VisitGetHierarchyRoleResponse(w http.ResponseWriter) error
}

type GetHierarchyRole200JSONResponse HierarchyRole

func (response GetHierarchyRole200JSONResponse) VisitGetHierarchyRoleResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetHierarchyRole500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response GetHierarchyRole500JSONResponse) VisitGetHierarchyRoleResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type PutHierarchyRoleRequestObject struct {
	Body *PutHierarchyRoleJSONRequestBody
}

type PutHierarchyRoleResponseObject interface {
	VisitPutHierarchyRoleResponse(w http.ResponseWriter) error
}

type PutHierarchyRole204Response struct {
}

func (response PutHierarchyRole204Response) VisitPutHierarchyRoleResponse(w http.ResponseWriter) error {
	w.WriteHeader(204)
	return nil
}

type PutHierarchyRole500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response PutHierarchyRole500JSONResponse) VisitPutHierarchyRoleResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetHierarchyRoleAllListRequestObject struct {
}

type GetHierarchyRoleAllListResponseObject interface {
	VisitGetHierarchyRoleAllListResponse(w http.ResponseWriter) error
}

type GetHierarchyRoleAllList200JSONResponse []HierarchyRole

func (response GetHierarchyRoleAllList200JSONResponse) VisitGetHierarchyRoleAllListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetHierarchyRoleAllList500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response GetHierarchyRoleAllList500JSONResponse) VisitGetHierarchyRoleAllListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetHierarchyRoleListRequestObject struct {
	Params GetHierarchyRoleListParams
}

type GetHierarchyRoleListResponseObject interface {
	VisitGetHierarchyRoleListResponse(w http.ResponseWriter) error
}

type GetHierarchyRoleList200JSONResponse []HierarchyRole

func (response GetHierarchyRoleList200JSONResponse) VisitGetHierarchyRoleListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetHierarchyRoleList500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response GetHierarchyRoleList500JSONResponse) VisitGetHierarchyRoleListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetJigyosyoCdListRequestObject struct {
	Params GetJigyosyoCdListParams
}

type GetJigyosyoCdListResponseObject interface {
	VisitGetJigyosyoCdListResponse(w http.ResponseWriter) error
}

type GetJigyosyoCdList200JSONResponse []JigyosyoInfo

func (response GetJigyosyoCdList200JSONResponse) VisitGetJigyosyoCdListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetJigyosyoCdList500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response GetJigyosyoCdList500JSONResponse) VisitGetJigyosyoCdListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type DeleteMaintenanceInfoRequestObject struct {
	Params DeleteMaintenanceInfoParams
}

type DeleteMaintenanceInfoResponseObject interface {
	VisitDeleteMaintenanceInfoResponse(w http.ResponseWriter) error
}

type DeleteMaintenanceInfo204Response struct {
}

func (response DeleteMaintenanceInfo204Response) VisitDeleteMaintenanceInfoResponse(w http.ResponseWriter) error {
	w.WriteHeader(204)
	return nil
}

type DeleteMaintenanceInfo500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response DeleteMaintenanceInfo500JSONResponse) VisitDeleteMaintenanceInfoResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetMaintenanceInfoRequestObject struct {
	Params GetMaintenanceInfoParams
}

type GetMaintenanceInfoResponseObject interface {
	VisitGetMaintenanceInfoResponse(w http.ResponseWriter) error
}

type GetMaintenanceInfo200JSONResponse MaintenanceInfo

func (response GetMaintenanceInfo200JSONResponse) VisitGetMaintenanceInfoResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetMaintenanceInfo500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response GetMaintenanceInfo500JSONResponse) VisitGetMaintenanceInfoResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type PatchMaintenanceInfoRequestObject struct {
	Body *PatchMaintenanceInfoJSONRequestBody
}

type PatchMaintenanceInfoResponseObject interface {
	VisitPatchMaintenanceInfoResponse(w http.ResponseWriter) error
}

type PatchMaintenanceInfo204Response struct {
}

func (response PatchMaintenanceInfo204Response) VisitPatchMaintenanceInfoResponse(w http.ResponseWriter) error {
	w.WriteHeader(204)
	return nil
}

type PatchMaintenanceInfo500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response PatchMaintenanceInfo500JSONResponse) VisitPatchMaintenanceInfoResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type PutMaintenanceInfoRequestObject struct {
	Body *PutMaintenanceInfoJSONRequestBody
}

type PutMaintenanceInfoResponseObject interface {
	VisitPutMaintenanceInfoResponse(w http.ResponseWriter) error
}

type PutMaintenanceInfo204Response struct {
}

func (response PutMaintenanceInfo204Response) VisitPutMaintenanceInfoResponse(w http.ResponseWriter) error {
	w.WriteHeader(204)
	return nil
}

type PutMaintenanceInfo500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response PutMaintenanceInfo500JSONResponse) VisitPutMaintenanceInfoResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetMaintenanceInfoListRequestObject struct {
}

type GetMaintenanceInfoListResponseObject interface {
	VisitGetMaintenanceInfoListResponse(w http.ResponseWriter) error
}

type GetMaintenanceInfoList200JSONResponse []MaintenanceInfo

func (response GetMaintenanceInfoList200JSONResponse) VisitGetMaintenanceInfoListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetMaintenanceInfoList500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response GetMaintenanceInfoList500JSONResponse) VisitGetMaintenanceInfoListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetMasterEmplrListRequestObject struct {
}

type GetMasterEmplrListResponseObject interface {
	VisitGetMasterEmplrListResponse(w http.ResponseWriter) error
}

type GetMasterEmplrList200JSONResponse []EmplrMaster

func (response GetMasterEmplrList200JSONResponse) VisitGetMasterEmplrListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetMasterEmplrList500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response GetMasterEmplrList500JSONResponse) VisitGetMasterEmplrListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type DeleteNoticeInfoRequestObject struct {
	Params DeleteNoticeInfoParams
}

type DeleteNoticeInfoResponseObject interface {
	VisitDeleteNoticeInfoResponse(w http.ResponseWriter) error
}

type DeleteNoticeInfo204Response struct {
}

func (response DeleteNoticeInfo204Response) VisitDeleteNoticeInfoResponse(w http.ResponseWriter) error {
	w.WriteHeader(204)
	return nil
}

type DeleteNoticeInfo500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response DeleteNoticeInfo500JSONResponse) VisitDeleteNoticeInfoResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetNoticeInfoRequestObject struct {
	Params GetNoticeInfoParams
}

type GetNoticeInfoResponseObject interface {
	VisitGetNoticeInfoResponse(w http.ResponseWriter) error
}

type GetNoticeInfo200JSONResponse NoticeInfo

func (response GetNoticeInfo200JSONResponse) VisitGetNoticeInfoResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetNoticeInfo400JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response GetNoticeInfo400JSONResponse) VisitGetNoticeInfoResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(400)

	return json.NewEncoder(w).Encode(response)
}

type GetNoticeInfo500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response GetNoticeInfo500JSONResponse) VisitGetNoticeInfoResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type PostNoticeInfoRequestObject struct {
	Body *multipart.Reader
}

type PostNoticeInfoResponseObject interface {
	VisitPostNoticeInfoResponse(w http.ResponseWriter) error
}

type PostNoticeInfo204Response struct {
}

func (response PostNoticeInfo204Response) VisitPostNoticeInfoResponse(w http.ResponseWriter) error {
	w.WriteHeader(204)
	return nil
}

type PostNoticeInfo500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response PostNoticeInfo500JSONResponse) VisitPostNoticeInfoResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetNoticeInfoListRequestObject struct {
}

type GetNoticeInfoListResponseObject interface {
	VisitGetNoticeInfoListResponse(w http.ResponseWriter) error
}

type GetNoticeInfoList200JSONResponse []NoticeInfo

func (response GetNoticeInfoList200JSONResponse) VisitGetNoticeInfoListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetNoticeInfoList500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response GetNoticeInfoList500JSONResponse) VisitGetNoticeInfoListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type PostRegistUserListRequestObject struct {
	Body *multipart.Reader
}

type PostRegistUserListResponseObject interface {
	VisitPostRegistUserListResponse(w http.ResponseWriter) error
}

type PostRegistUserList200JSONResponse struct {
	Results []RegistCsvResult `json:"results"`
}

func (response PostRegistUserList200JSONResponse) VisitPostRegistUserListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type PostRegistUserList400JSONResponse struct {
	Errors []string `json:"errors"`
}

func (response PostRegistUserList400JSONResponse) VisitPostRegistUserListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(400)

	return json.NewEncoder(w).Encode(response)
}

type PostRegistUserList500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response PostRegistUserList500JSONResponse) VisitPostRegistUserListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type PostSendMailUserListRequestObject struct {
	Body *multipart.Reader
}

type PostSendMailUserListResponseObject interface {
	VisitPostSendMailUserListResponse(w http.ResponseWriter) error
}

type PostSendMailUserList200JSONResponse struct {
	Results []RegistCsvResult `json:"results"`
}

func (response PostSendMailUserList200JSONResponse) VisitPostSendMailUserListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type PostSendMailUserList400JSONResponse struct {
	Errors []string `json:"errors"`
}

func (response PostSendMailUserList400JSONResponse) VisitPostSendMailUserListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(400)

	return json.NewEncoder(w).Encode(response)
}

type PostSendMailUserList500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response PostSendMailUserList500JSONResponse) VisitPostSendMailUserListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetServiceStatusRequestObject struct {
}

type GetServiceStatusResponseObject interface {
	VisitGetServiceStatusResponse(w http.ResponseWriter) error
}

type GetServiceStatus200JSONResponse struct {
	// Status サーバーステータス
	Status int `json:"status"`
}

func (response GetServiceStatus200JSONResponse) VisitGetServiceStatusResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetServiceStatus500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response GetServiceStatus500JSONResponse) VisitGetServiceStatusResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type PatchServiceStatusRequestObject struct {
	Params PatchServiceStatusParams
}

type PatchServiceStatusResponseObject interface {
	VisitPatchServiceStatusResponse(w http.ResponseWriter) error
}

type PatchServiceStatus204Response struct {
}

func (response PatchServiceStatus204Response) VisitPatchServiceStatusResponse(w http.ResponseWriter) error {
	w.WriteHeader(204)
	return nil
}

type PatchServiceStatus500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response PatchServiceStatus500JSONResponse) VisitPatchServiceStatusResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type DeleteUserRequestObject struct {
	Params DeleteUserParams
}

type DeleteUserResponseObject interface {
	VisitDeleteUserResponse(w http.ResponseWriter) error
}

type DeleteUser204Response struct {
}

func (response DeleteUser204Response) VisitDeleteUserResponse(w http.ResponseWriter) error {
	w.WriteHeader(204)
	return nil
}

type DeleteUser500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response DeleteUser500JSONResponse) VisitDeleteUserResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetUserRequestObject struct {
	Params GetUserParams
}

type GetUserResponseObject interface {
	VisitGetUserResponse(w http.ResponseWriter) error
}

type GetUser200JSONResponse UserDetail

func (response GetUser200JSONResponse) VisitGetUserResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetUser500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response GetUser500JSONResponse) VisitGetUserResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type PatchUserRequestObject struct {
	Body *PatchUserJSONRequestBody
}

type PatchUserResponseObject interface {
	VisitPatchUserResponse(w http.ResponseWriter) error
}

type PatchUser204Response struct {
}

func (response PatchUser204Response) VisitPatchUserResponse(w http.ResponseWriter) error {
	w.WriteHeader(204)
	return nil
}

type PatchUser500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response PatchUser500JSONResponse) VisitPatchUserResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type PutUserRequestObject struct {
	Body *PutUserJSONRequestBody
}

type PutUserResponseObject interface {
	VisitPutUserResponse(w http.ResponseWriter) error
}

type PutUser204Response struct {
}

func (response PutUser204Response) VisitPutUserResponse(w http.ResponseWriter) error {
	w.WriteHeader(204)
	return nil
}

type PutUser400JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response PutUser400JSONResponse) VisitPutUserResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(400)

	return json.NewEncoder(w).Encode(response)
}

type PutUser401JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response PutUser401JSONResponse) VisitPutUserResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(401)

	return json.NewEncoder(w).Encode(response)
}

type PutUser500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response PutUser500JSONResponse) VisitPutUserResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

type GetUserListRequestObject struct {
}

type GetUserListResponseObject interface {
	VisitGetUserListResponse(w http.ResponseWriter) error
}

type GetUserList200JSONResponse []UserSimple

func (response GetUserList200JSONResponse) VisitGetUserListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetUserList500JSONResponse struct {
	// Message エラーメッセージ
	Message string `json:"message"`
}

func (response GetUserList500JSONResponse) VisitGetUserListResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response)
}

// StrictServerInterface represents all server handlers.
type StrictServerInterface interface {
	// ユーザー登録確認CSV作成
	// (POST /confirm-regist-user-list)
	PostConfirmRegistUserList(ctx context.Context, request PostConfirmRegistUserListRequestObject) (PostConfirmRegistUserListResponseObject, error)
	// メール送信確認
	// (POST /confirm-send-mail-user-list)
	PostConfirmSendMailUserList(ctx context.Context, request PostConfirmSendMailUserListRequestObject) (PostConfirmSendMailUserListResponseObject, error)
	// 組合削除
	// (DELETE /emplr)
	DeleteEmplr(ctx context.Context, request DeleteEmplrRequestObject) (DeleteEmplrResponseObject, error)
	// 組合取得
	// (GET /emplr)
	GetEmplr(ctx context.Context, request GetEmplrRequestObject) (GetEmplrResponseObject, error)
	// 組合更新
	// (PATCH /emplr)
	PatchEmplr(ctx context.Context, request PatchEmplrRequestObject) (PatchEmplrResponseObject, error)
	// 組合作成
	// (PUT /emplr)
	PutEmplr(ctx context.Context, request PutEmplrRequestObject) (PutEmplrResponseObject, error)
	// 組合一覧取得
	// (GET /emplr-list)
	GetEmplrList(ctx context.Context, request GetEmplrListRequestObject) (GetEmplrListResponseObject, error)
	// 組合階層削除
	// (DELETE /hierarchy-role)
	DeleteHierarchyRole(ctx context.Context, request DeleteHierarchyRoleRequestObject) (DeleteHierarchyRoleResponseObject, error)
	// 組合階層取得
	// (GET /hierarchy-role)
	GetHierarchyRole(ctx context.Context, request GetHierarchyRoleRequestObject) (GetHierarchyRoleResponseObject, error)
	// 組合階層追加
	// (PUT /hierarchy-role)
	PutHierarchyRole(ctx context.Context, request PutHierarchyRoleRequestObject) (PutHierarchyRoleResponseObject, error)
	// 階層情報ロールの一覧取得
	// (GET /hierarchy-role-all-list)
	GetHierarchyRoleAllList(ctx context.Context, request GetHierarchyRoleAllListRequestObject) (GetHierarchyRoleAllListResponseObject, error)
	// 階層情報ロールの一覧取得
	// (GET /hierarchy-role-list)
	GetHierarchyRoleList(ctx context.Context, request GetHierarchyRoleListRequestObject) (GetHierarchyRoleListResponseObject, error)
	// 事業所CDリストの取得
	// (GET /jigyosyo-cd-list)
	GetJigyosyoCdList(ctx context.Context, request GetJigyosyoCdListRequestObject) (GetJigyosyoCdListResponseObject, error)
	// メンテナンス情報削除
	// (DELETE /maintenance-info)
	DeleteMaintenanceInfo(ctx context.Context, request DeleteMaintenanceInfoRequestObject) (DeleteMaintenanceInfoResponseObject, error)
	// メンテナンス情報取得
	// (GET /maintenance-info)
	GetMaintenanceInfo(ctx context.Context, request GetMaintenanceInfoRequestObject) (GetMaintenanceInfoResponseObject, error)
	// メンテナンス情報更新
	// (PATCH /maintenance-info)
	PatchMaintenanceInfo(ctx context.Context, request PatchMaintenanceInfoRequestObject) (PatchMaintenanceInfoResponseObject, error)
	// メンテナンス情報作成
	// (PUT /maintenance-info)
	PutMaintenanceInfo(ctx context.Context, request PutMaintenanceInfoRequestObject) (PutMaintenanceInfoResponseObject, error)
	// メンテナンス情報一覧取得
	// (GET /maintenance-info-list)
	GetMaintenanceInfoList(ctx context.Context, request GetMaintenanceInfoListRequestObject) (GetMaintenanceInfoListResponseObject, error)
	// 組合マスターリストの取得
	// (GET /master-emplr-list)
	GetMasterEmplrList(ctx context.Context, request GetMasterEmplrListRequestObject) (GetMasterEmplrListResponseObject, error)
	// お知らせ削除
	// (DELETE /notice-info)
	DeleteNoticeInfo(ctx context.Context, request DeleteNoticeInfoRequestObject) (DeleteNoticeInfoResponseObject, error)
	// お知らせ取得
	// (GET /notice-info)
	GetNoticeInfo(ctx context.Context, request GetNoticeInfoRequestObject) (GetNoticeInfoResponseObject, error)
	// お知らせ作成
	// (POST /notice-info)
	PostNoticeInfo(ctx context.Context, request PostNoticeInfoRequestObject) (PostNoticeInfoResponseObject, error)
	// お知らせ一覧取得
	// (GET /notice-info-list)
	GetNoticeInfoList(ctx context.Context, request GetNoticeInfoListRequestObject) (GetNoticeInfoListResponseObject, error)
	// ユーザー登録
	// (POST /regist-user-list)
	PostRegistUserList(ctx context.Context, request PostRegistUserListRequestObject) (PostRegistUserListResponseObject, error)
	// メール送信
	// (POST /send-mail-user-list)
	PostSendMailUserList(ctx context.Context, request PostSendMailUserListRequestObject) (PostSendMailUserListResponseObject, error)
	// サーバーステータス取得
	// (GET /service-status)
	GetServiceStatus(ctx context.Context, request GetServiceStatusRequestObject) (GetServiceStatusResponseObject, error)
	// サーバーステータス更新
	// (PATCH /service-status)
	PatchServiceStatus(ctx context.Context, request PatchServiceStatusRequestObject) (PatchServiceStatusResponseObject, error)
	// ユーザー削除
	// (DELETE /user)
	DeleteUser(ctx context.Context, request DeleteUserRequestObject) (DeleteUserResponseObject, error)
	// ユーザー取得
	// (GET /user)
	GetUser(ctx context.Context, request GetUserRequestObject) (GetUserResponseObject, error)
	// ユーザー更新
	// (PATCH /user)
	PatchUser(ctx context.Context, request PatchUserRequestObject) (PatchUserResponseObject, error)
	// ユーザー作成
	// (PUT /user)
	PutUser(ctx context.Context, request PutUserRequestObject) (PutUserResponseObject, error)
	// ユーザー一覧取得
	// (GET /user-list)
	GetUserList(ctx context.Context, request GetUserListRequestObject) (GetUserListResponseObject, error)
}

type StrictHandlerFunc = strictnethttp.StrictHTTPHandlerFunc
type StrictMiddlewareFunc = strictnethttp.StrictHTTPMiddlewareFunc

type StrictHTTPServerOptions struct {
	RequestErrorHandlerFunc  func(w http.ResponseWriter, r *http.Request, err error)
	ResponseErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

func NewStrictHandler(ssi StrictServerInterface, middlewares []StrictMiddlewareFunc) ServerInterface {
	return &strictHandler{ssi: ssi, middlewares: middlewares, options: StrictHTTPServerOptions{
		RequestErrorHandlerFunc: func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		},
		ResponseErrorHandlerFunc: func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusInternalServerError)
		},
	}}
}

func NewStrictHandlerWithOptions(ssi StrictServerInterface, middlewares []StrictMiddlewareFunc, options StrictHTTPServerOptions) ServerInterface {
	return &strictHandler{ssi: ssi, middlewares: middlewares, options: options}
}

type strictHandler struct {
	ssi         StrictServerInterface
	middlewares []StrictMiddlewareFunc
	options     StrictHTTPServerOptions
}

// PostConfirmRegistUserList operation middleware
func (sh *strictHandler) PostConfirmRegistUserList(w http.ResponseWriter, r *http.Request) {
	var request PostConfirmRegistUserListRequestObject

	if reader, err := r.MultipartReader(); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode multipart body: %w", err))
		return
	} else {
		request.Body = reader
	}

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.PostConfirmRegistUserList(ctx, request.(PostConfirmRegistUserListRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "PostConfirmRegistUserList")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(PostConfirmRegistUserListResponseObject); ok {
		if err := validResponse.VisitPostConfirmRegistUserListResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// PostConfirmSendMailUserList operation middleware
func (sh *strictHandler) PostConfirmSendMailUserList(w http.ResponseWriter, r *http.Request) {
	var request PostConfirmSendMailUserListRequestObject

	if reader, err := r.MultipartReader(); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode multipart body: %w", err))
		return
	} else {
		request.Body = reader
	}

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.PostConfirmSendMailUserList(ctx, request.(PostConfirmSendMailUserListRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "PostConfirmSendMailUserList")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(PostConfirmSendMailUserListResponseObject); ok {
		if err := validResponse.VisitPostConfirmSendMailUserListResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// DeleteEmplr operation middleware
func (sh *strictHandler) DeleteEmplr(w http.ResponseWriter, r *http.Request, params DeleteEmplrParams) {
	var request DeleteEmplrRequestObject

	request.Params = params

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.DeleteEmplr(ctx, request.(DeleteEmplrRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "DeleteEmplr")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(DeleteEmplrResponseObject); ok {
		if err := validResponse.VisitDeleteEmplrResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetEmplr operation middleware
func (sh *strictHandler) GetEmplr(w http.ResponseWriter, r *http.Request, params GetEmplrParams) {
	var request GetEmplrRequestObject

	request.Params = params

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetEmplr(ctx, request.(GetEmplrRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetEmplr")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetEmplrResponseObject); ok {
		if err := validResponse.VisitGetEmplrResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// PatchEmplr operation middleware
func (sh *strictHandler) PatchEmplr(w http.ResponseWriter, r *http.Request) {
	var request PatchEmplrRequestObject

	var body PatchEmplrJSONRequestBody
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode JSON body: %w", err))
		return
	}
	request.Body = &body

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.PatchEmplr(ctx, request.(PatchEmplrRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "PatchEmplr")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(PatchEmplrResponseObject); ok {
		if err := validResponse.VisitPatchEmplrResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// PutEmplr operation middleware
func (sh *strictHandler) PutEmplr(w http.ResponseWriter, r *http.Request) {
	var request PutEmplrRequestObject

	var body PutEmplrJSONRequestBody
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode JSON body: %w", err))
		return
	}
	request.Body = &body

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.PutEmplr(ctx, request.(PutEmplrRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "PutEmplr")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(PutEmplrResponseObject); ok {
		if err := validResponse.VisitPutEmplrResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetEmplrList operation middleware
func (sh *strictHandler) GetEmplrList(w http.ResponseWriter, r *http.Request) {
	var request GetEmplrListRequestObject

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetEmplrList(ctx, request.(GetEmplrListRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetEmplrList")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetEmplrListResponseObject); ok {
		if err := validResponse.VisitGetEmplrListResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// DeleteHierarchyRole operation middleware
func (sh *strictHandler) DeleteHierarchyRole(w http.ResponseWriter, r *http.Request, params DeleteHierarchyRoleParams) {
	var request DeleteHierarchyRoleRequestObject

	request.Params = params

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.DeleteHierarchyRole(ctx, request.(DeleteHierarchyRoleRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "DeleteHierarchyRole")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(DeleteHierarchyRoleResponseObject); ok {
		if err := validResponse.VisitDeleteHierarchyRoleResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetHierarchyRole operation middleware
func (sh *strictHandler) GetHierarchyRole(w http.ResponseWriter, r *http.Request, params GetHierarchyRoleParams) {
	var request GetHierarchyRoleRequestObject

	request.Params = params

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetHierarchyRole(ctx, request.(GetHierarchyRoleRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetHierarchyRole")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetHierarchyRoleResponseObject); ok {
		if err := validResponse.VisitGetHierarchyRoleResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// PutHierarchyRole operation middleware
func (sh *strictHandler) PutHierarchyRole(w http.ResponseWriter, r *http.Request) {
	var request PutHierarchyRoleRequestObject

	var body PutHierarchyRoleJSONRequestBody
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode JSON body: %w", err))
		return
	}
	request.Body = &body

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.PutHierarchyRole(ctx, request.(PutHierarchyRoleRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "PutHierarchyRole")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(PutHierarchyRoleResponseObject); ok {
		if err := validResponse.VisitPutHierarchyRoleResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetHierarchyRoleAllList operation middleware
func (sh *strictHandler) GetHierarchyRoleAllList(w http.ResponseWriter, r *http.Request) {
	var request GetHierarchyRoleAllListRequestObject

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetHierarchyRoleAllList(ctx, request.(GetHierarchyRoleAllListRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetHierarchyRoleAllList")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetHierarchyRoleAllListResponseObject); ok {
		if err := validResponse.VisitGetHierarchyRoleAllListResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetHierarchyRoleList operation middleware
func (sh *strictHandler) GetHierarchyRoleList(w http.ResponseWriter, r *http.Request, params GetHierarchyRoleListParams) {
	var request GetHierarchyRoleListRequestObject

	request.Params = params

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetHierarchyRoleList(ctx, request.(GetHierarchyRoleListRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetHierarchyRoleList")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetHierarchyRoleListResponseObject); ok {
		if err := validResponse.VisitGetHierarchyRoleListResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetJigyosyoCdList operation middleware
func (sh *strictHandler) GetJigyosyoCdList(w http.ResponseWriter, r *http.Request, params GetJigyosyoCdListParams) {
	var request GetJigyosyoCdListRequestObject

	request.Params = params

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetJigyosyoCdList(ctx, request.(GetJigyosyoCdListRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetJigyosyoCdList")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetJigyosyoCdListResponseObject); ok {
		if err := validResponse.VisitGetJigyosyoCdListResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// DeleteMaintenanceInfo operation middleware
func (sh *strictHandler) DeleteMaintenanceInfo(w http.ResponseWriter, r *http.Request, params DeleteMaintenanceInfoParams) {
	var request DeleteMaintenanceInfoRequestObject

	request.Params = params

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.DeleteMaintenanceInfo(ctx, request.(DeleteMaintenanceInfoRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "DeleteMaintenanceInfo")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(DeleteMaintenanceInfoResponseObject); ok {
		if err := validResponse.VisitDeleteMaintenanceInfoResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetMaintenanceInfo operation middleware
func (sh *strictHandler) GetMaintenanceInfo(w http.ResponseWriter, r *http.Request, params GetMaintenanceInfoParams) {
	var request GetMaintenanceInfoRequestObject

	request.Params = params

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetMaintenanceInfo(ctx, request.(GetMaintenanceInfoRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetMaintenanceInfo")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetMaintenanceInfoResponseObject); ok {
		if err := validResponse.VisitGetMaintenanceInfoResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// PatchMaintenanceInfo operation middleware
func (sh *strictHandler) PatchMaintenanceInfo(w http.ResponseWriter, r *http.Request) {
	var request PatchMaintenanceInfoRequestObject

	var body PatchMaintenanceInfoJSONRequestBody
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode JSON body: %w", err))
		return
	}
	request.Body = &body

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.PatchMaintenanceInfo(ctx, request.(PatchMaintenanceInfoRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "PatchMaintenanceInfo")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(PatchMaintenanceInfoResponseObject); ok {
		if err := validResponse.VisitPatchMaintenanceInfoResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// PutMaintenanceInfo operation middleware
func (sh *strictHandler) PutMaintenanceInfo(w http.ResponseWriter, r *http.Request) {
	var request PutMaintenanceInfoRequestObject

	var body PutMaintenanceInfoJSONRequestBody
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode JSON body: %w", err))
		return
	}
	request.Body = &body

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.PutMaintenanceInfo(ctx, request.(PutMaintenanceInfoRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "PutMaintenanceInfo")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(PutMaintenanceInfoResponseObject); ok {
		if err := validResponse.VisitPutMaintenanceInfoResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetMaintenanceInfoList operation middleware
func (sh *strictHandler) GetMaintenanceInfoList(w http.ResponseWriter, r *http.Request) {
	var request GetMaintenanceInfoListRequestObject

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetMaintenanceInfoList(ctx, request.(GetMaintenanceInfoListRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetMaintenanceInfoList")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetMaintenanceInfoListResponseObject); ok {
		if err := validResponse.VisitGetMaintenanceInfoListResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetMasterEmplrList operation middleware
func (sh *strictHandler) GetMasterEmplrList(w http.ResponseWriter, r *http.Request) {
	var request GetMasterEmplrListRequestObject

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetMasterEmplrList(ctx, request.(GetMasterEmplrListRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetMasterEmplrList")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetMasterEmplrListResponseObject); ok {
		if err := validResponse.VisitGetMasterEmplrListResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// DeleteNoticeInfo operation middleware
func (sh *strictHandler) DeleteNoticeInfo(w http.ResponseWriter, r *http.Request, params DeleteNoticeInfoParams) {
	var request DeleteNoticeInfoRequestObject

	request.Params = params

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.DeleteNoticeInfo(ctx, request.(DeleteNoticeInfoRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "DeleteNoticeInfo")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(DeleteNoticeInfoResponseObject); ok {
		if err := validResponse.VisitDeleteNoticeInfoResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetNoticeInfo operation middleware
func (sh *strictHandler) GetNoticeInfo(w http.ResponseWriter, r *http.Request, params GetNoticeInfoParams) {
	var request GetNoticeInfoRequestObject

	request.Params = params

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetNoticeInfo(ctx, request.(GetNoticeInfoRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetNoticeInfo")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetNoticeInfoResponseObject); ok {
		if err := validResponse.VisitGetNoticeInfoResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// PostNoticeInfo operation middleware
func (sh *strictHandler) PostNoticeInfo(w http.ResponseWriter, r *http.Request) {
	var request PostNoticeInfoRequestObject

	if reader, err := r.MultipartReader(); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode multipart body: %w", err))
		return
	} else {
		request.Body = reader
	}

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.PostNoticeInfo(ctx, request.(PostNoticeInfoRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "PostNoticeInfo")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(PostNoticeInfoResponseObject); ok {
		if err := validResponse.VisitPostNoticeInfoResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetNoticeInfoList operation middleware
func (sh *strictHandler) GetNoticeInfoList(w http.ResponseWriter, r *http.Request) {
	var request GetNoticeInfoListRequestObject

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetNoticeInfoList(ctx, request.(GetNoticeInfoListRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetNoticeInfoList")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetNoticeInfoListResponseObject); ok {
		if err := validResponse.VisitGetNoticeInfoListResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// PostRegistUserList operation middleware
func (sh *strictHandler) PostRegistUserList(w http.ResponseWriter, r *http.Request) {
	var request PostRegistUserListRequestObject

	if reader, err := r.MultipartReader(); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode multipart body: %w", err))
		return
	} else {
		request.Body = reader
	}

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.PostRegistUserList(ctx, request.(PostRegistUserListRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "PostRegistUserList")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(PostRegistUserListResponseObject); ok {
		if err := validResponse.VisitPostRegistUserListResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// PostSendMailUserList operation middleware
func (sh *strictHandler) PostSendMailUserList(w http.ResponseWriter, r *http.Request) {
	var request PostSendMailUserListRequestObject

	if reader, err := r.MultipartReader(); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode multipart body: %w", err))
		return
	} else {
		request.Body = reader
	}

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.PostSendMailUserList(ctx, request.(PostSendMailUserListRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "PostSendMailUserList")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(PostSendMailUserListResponseObject); ok {
		if err := validResponse.VisitPostSendMailUserListResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetServiceStatus operation middleware
func (sh *strictHandler) GetServiceStatus(w http.ResponseWriter, r *http.Request) {
	var request GetServiceStatusRequestObject

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetServiceStatus(ctx, request.(GetServiceStatusRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetServiceStatus")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetServiceStatusResponseObject); ok {
		if err := validResponse.VisitGetServiceStatusResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// PatchServiceStatus operation middleware
func (sh *strictHandler) PatchServiceStatus(w http.ResponseWriter, r *http.Request, params PatchServiceStatusParams) {
	var request PatchServiceStatusRequestObject

	request.Params = params

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.PatchServiceStatus(ctx, request.(PatchServiceStatusRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "PatchServiceStatus")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(PatchServiceStatusResponseObject); ok {
		if err := validResponse.VisitPatchServiceStatusResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// DeleteUser operation middleware
func (sh *strictHandler) DeleteUser(w http.ResponseWriter, r *http.Request, params DeleteUserParams) {
	var request DeleteUserRequestObject

	request.Params = params

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.DeleteUser(ctx, request.(DeleteUserRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "DeleteUser")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(DeleteUserResponseObject); ok {
		if err := validResponse.VisitDeleteUserResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetUser operation middleware
func (sh *strictHandler) GetUser(w http.ResponseWriter, r *http.Request, params GetUserParams) {
	var request GetUserRequestObject

	request.Params = params

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetUser(ctx, request.(GetUserRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetUser")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetUserResponseObject); ok {
		if err := validResponse.VisitGetUserResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// PatchUser operation middleware
func (sh *strictHandler) PatchUser(w http.ResponseWriter, r *http.Request) {
	var request PatchUserRequestObject

	var body PatchUserJSONRequestBody
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode JSON body: %w", err))
		return
	}
	request.Body = &body

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.PatchUser(ctx, request.(PatchUserRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "PatchUser")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(PatchUserResponseObject); ok {
		if err := validResponse.VisitPatchUserResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// PutUser operation middleware
func (sh *strictHandler) PutUser(w http.ResponseWriter, r *http.Request) {
	var request PutUserRequestObject

	var body PutUserJSONRequestBody
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode JSON body: %w", err))
		return
	}
	request.Body = &body

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.PutUser(ctx, request.(PutUserRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "PutUser")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(PutUserResponseObject); ok {
		if err := validResponse.VisitPutUserResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetUserList operation middleware
func (sh *strictHandler) GetUserList(w http.ResponseWriter, r *http.Request) {
	var request GetUserListRequestObject

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetUserList(ctx, request.(GetUserListRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetUserList")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetUserListResponseObject); ok {
		if err := validResponse.VisitGetUserListResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}
