package handler

import (
	"context"
	"log/slog"
	"net/http"

	"github.com/jmdc-inc/kensuke-plus-server/pkg/authenticator"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/aws/s3"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/config"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/db"
	"github.com/jmdc-inc/kensuke-plus-server/pkg/errof"
	oapi "github.com/jmdc-inc/kensuke-plus-server/pkg/oapi/public"
	"github.com/jmdc-inc/kensuke-plus-server/public/api/domain/service"
	usecase "github.com/jmdc-inc/kensuke-plus-server/public/api/usecase"
	"github.com/pkg/errors"
)

var _ oapi.StrictServerInterface = (*API)(nil)

type API struct {
	logger                 *slog.Logger
	config                 config.AppConfig
	dbClient               db.Client
	authClient             authenticator.Authenticator
	s3Client               s3.S3Client
	tableauUseCase         usecase.TableauUseCase
	tableauDownloadUseCase usecase.TableauDownloadUseCase
	userUseCase            usecase.UserUseCase
	emplrUseCase           usecase.EmplrUseCase
	noticeUseCase          usecase.NoticeUseCase
	maintenanceUseCase     usecase.MaintenanceUseCase
	subscriberInfoUseCase  usecase.SubscriberInfoUseCase
	auth0TokenService      service.Auth0TokenService
}

func NewAPI(
	logger *slog.Logger,
	appConfig config.AppConfig,
	dbClient db.Client,
	authClient authenticator.Authenticator,
	s3Client s3.S3Client,
	tableauUseCase usecase.TableauUseCase,
	tableauDownloadUseCase usecase.TableauDownloadUseCase,
	userUseCase usecase.UserUseCase,
	emplrUseCase usecase.EmplrUseCase,
	noticeUseCase usecase.NoticeUseCase,
	maintenanceUseCase usecase.MaintenanceUseCase,
	subscriberInfoUseCase usecase.SubscriberInfoUseCase,
	auth0TokenService service.Auth0TokenService,
) *API {
	return &API{
		logger,
		appConfig,
		dbClient,
		authClient,
		s3Client,
		tableauUseCase,
		tableauDownloadUseCase,
		userUseCase,
		emplrUseCase,
		noticeUseCase,
		maintenanceUseCase,
		subscriberInfoUseCase,
		auth0TokenService,
	}
}

func RequestErrorHandlerFunc() func(w http.ResponseWriter, r *http.Request, err error) {
	return func(w http.ResponseWriter, r *http.Request, err error) {
		http.Error(w, err.Error(), http.StatusBadRequest)
	}
}
func ResponseErrorHandlerFunc(logger *slog.Logger) func(w http.ResponseWriter, r *http.Request, err error) {
	return func(w http.ResponseWriter, r *http.Request, err error) {
		logError(logger, r.Context(), err)
		http.Error(w, errors.Cause(err).Error(), errof.HTTPStatusOf(err))
	}
}

func logError(logger *slog.Logger, ctx context.Context, err error) {
	switch _err := errors.Cause(err); {
	case errors.As(_err, errof.ErrPtr[errof.UserError]()):
		logger.WarnContext(ctx, _err.Error(), slog.Any("error", err))
	case errors.As(_err, errof.ErrPtr[errof.VerifyError]()):
		logger.WarnContext(ctx, _err.Error(), slog.Any("error", err))
	default:
		logger.WarnContext(ctx, _err.Error(), slog.Any("error", err))
	}
}
