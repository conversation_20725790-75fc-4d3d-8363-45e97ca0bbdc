// Package authenticator
package authenticator

import (
	"crypto/rand"
	"math/big"
)

// GeneratePassword は指定した長さ以上のパスワードを生成する関数です。
// 要件:
// - パスワードの長さは minimumLength(デフォルト20) 以上
// - 小文字(a-z), 大文字(A-Z), 数字(0-9), 特殊文字(!@#$%^&*) を最低 1文字ずつ含む
func GeneratePassword(minimumLength int) (string, error) {
	if minimumLength < 20 {
		minimumLength = 20
	}

	// 各種文字セット
	lowerLetters := "abcdefghijklmnopqrstuvwxyz"
	upperLetters := "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	digits := "0123456789"
	symbols := "!@#$%^&*"

	// ランダム文字選択に使う関数
	randomChar := func(charset string) (rune, error) {
		n, err := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		if err != nil {
			return ' ', err
		}
		return rune(charset[n.Int64()]), nil
	}

	// 必ず1文字ずつ加える(小文字、大文字、数字、特殊文字)
	selected := make([]rune, 0, minimumLength)

	sets := []string{lowerLetters, upperLetters, digits, symbols}
	for _, set := range sets {
		c, err := randomChar(set)
		if err != nil {
			return "", err
		}
		selected = append(selected, c)
	}

	// 残りの文字数分は全ての文字集合からランダムに選ぶ
	allChars := lowerLetters + upperLetters + digits + symbols
	for i := len(sets); i < minimumLength; i++ {
		c, err := randomChar(allChars)
		if err != nil {
			return "", err
		}
		selected = append(selected, c)
	}

	// スライス内をシャッフルしてよりランダム性を上げる
	for i := len(selected) - 1; i > 0; i-- {
		jBig, err := rand.Int(rand.Reader, big.NewInt(int64(i+1)))
		if err != nil {
			return "", err
		}
		j := jBig.Int64()
		selected[i], selected[j] = selected[j], selected[i]
	}

	return string(selected), nil
}
